package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import scrbg.meplat.mall.dto.product.CreateBatchByBillIdDTO;
import scrbg.meplat.mall.dto.product.UpdateProductInventoryStateDTO;
import scrbg.meplat.mall.entity.ProductInventory;
import scrbg.meplat.mall.service.ProductInventoryService;

import javax.validation.Valid;
import java.util.List;

/**
 * @描述：商品库控制类
 * @作者: y
 * @日期: 2022-11-02
 */
@RestController
@RequestMapping("/")
@ApiSort(value = 500)
@Api(tags = "商品库（后台）")
public class ProductInventoryController {

    @Autowired
    public ProductInventoryService productInventoryService;

    /**
     * 批量导入物资到商品库
     *
     * @param dtos
     * @return
     */
    @PostMapping("/platform/productInventory/importBatch")
    @ApiOperation(value = "批量导入物资到商品库")
    public R importBatch(@Valid @RequestBody List<CreateBatchByBillIdDTO> dtos) {
        productInventoryService.importBatch(dtos);
        return R.success();
    }

    @PostMapping("/productInventory/listPage")
    @ApiOperation(value = "根据分类id等参数查询商品库商品分页列表（平台）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "productType", value = "分类类型：0物资 1设备 2周材（物资） 3周材（设备） 4二手设备 5租赁设备", required = true,
                    dataTypeClass = Integer.class),
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字（商品名称）", dataTypeClass = String.class),
            @DynamicParameter(name = "productTitle", value = "商品名称", dataTypeClass = String.class),
            @DynamicParameter(name = "classId", value = "分类ID", dataTypeClass = String.class),
            @DynamicParameter(name = "state", value = "状态（1启用0停用）", dataTypeClass = Integer.class),
            @DynamicParameter(name = "startDate", value = "创建时间开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endDate", value = "创建时间结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "orderBy", value = "排序字段(1:按排序值排序2:按创建时间排序)", dataTypeClass = Integer.class),
    })
    public PageR<ProductInventory> listPlatformProductInventoryPageByClassId(@RequestBody JSONObject jsonObject) {
        PageUtils page = productInventoryService.queryPage(jsonObject, Wrappers.lambdaQuery(ProductInventory.class));
        return PageR.success(page);
    }

    @PostMapping("/platform/productInventory/updateState")
    @ApiOperation(value = "批量修改商品状态（平台）")
    public R updateProductInventoryState(@Valid @RequestBody UpdateProductInventoryStateDTO dto) {
        productInventoryService.updateProductInventoryState(dto);
        return R.success();
    }

    @PostMapping("/platform/productInventory/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody ProductInventory productInventory) {
        productInventoryService.create(productInventory);
        return R.success();
    }

    @PostMapping("/platform/productInventory/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody ProductInventory productInventory) {
        productInventoryService.update(productInventory);
        return R.success();
    }

    @PostMapping("/platform/productInventory/updateBatch")
    @ApiOperation(value = "批量修改")
    public R updateBatch(@RequestBody List<ProductInventory> productInventory) {
        productInventoryService.updateBatchById(productInventory);
        return R.success();
    }

    @PostMapping("/platform/productInventory/deleteBatch")
    @ApiOperation(value = "根据主键批量逻辑删除")

    public R deleteBatch(@RequestBody List<String> ids) {
        productInventoryService.removeLogicBatch(ids);
        return R.success();
    }

//    @GetMapping("/findById")
//    @ApiOperation(value = "根据主键查询")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "id", value = "ID", required = true,
//                    dataType = "String", paramType = "query")
//    })
//    public R<ProductInventory> findById(String id) {
//        ProductInventory productInventory = productInventoryService.getById(id);
//        return R.success(productInventory);
//    }

    //    @GetMapping("/delete")
//    @ApiOperation(value = "根据主键删除")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "id", value = "ID", required = true,
//                    dataType = "String", paramType = "query")
//    })
//    public R delete(String id) {
//        productInventoryService.delete(id);
//        return R.success();
//    }

}

