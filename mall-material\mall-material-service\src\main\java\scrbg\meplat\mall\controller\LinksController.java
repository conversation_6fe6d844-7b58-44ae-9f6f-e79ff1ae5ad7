package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.entity.Links;
import scrbg.meplat.mall.service.LinksService;

import java.util.List;

/**
 * @描述：友情链接控制类
 * @作者: sund
 * @日期: 2022-11-09
 */
@RestController
@RequestMapping("/platform/links")
@ApiSort(value = 500)
@Api(tags = "友情链接")
public class LinksController {
    @Autowired
    MallConfig mallConfig;
    @Autowired
    public LinksService linksService;

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<Links> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = linksService.queryPage(jsonObject, new LambdaQueryWrapper<Links>());
        return PageR.success(page);
    }

    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<Links> findById(String id) {
        Links links = linksService.getById(id);
        return R.success(links);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody Links links) {
        QueryWrapper<Links> wrapper = new QueryWrapper();
        wrapper.eq("mall_type",mallConfig.mallType);
        wrapper.eq("name", links.getName());
        List<Links> results = linksService.list(wrapper);
        if (results.size() > 0) {
            return R.failed(400, "链接名重复");
        }
        links.setState(2);
        links.setMallType(mallConfig.mallType);
        linksService.saveOrUpdate(links);
        return R.success();
    }


    @PostMapping("/findByConditionByPage")
    @ApiOperation(value = "根据条件获取图片并分页")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "url", value = "url", dataTypeClass = Integer.class),
            @DynamicParameter(name = "state", value = "广告状态", dataTypeClass = Integer.class),
            @DynamicParameter(name = "mallType", value = "商城类型", dataTypeClass = String.class),
            @DynamicParameter(name = "orderBy", value = "排序方式", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
    })
    public PageR<Links> findByConditionByPage(@RequestBody JSONObject jsonObject) {
        PageUtils page = linksService.queryByConfigPage(jsonObject, new LambdaQueryWrapper<>());
        return PageR.success(page);
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody Links links) {
        LambdaQueryWrapper<Links> wrapper = new LambdaQueryWrapper();
        if (org.apache.commons.lang.StringUtils.isNotEmpty(links.getName())) {
            wrapper.eq(Links::getName, links.getName());
        } else {
            return R.failed(400, "链接名不能为空");
        }
        if (org.apache.commons.lang.StringUtils.isNotEmpty(links.getLinkId())) {
            wrapper.ne(Links::getLinkId, links.getLinkId());
        }
        int results = linksService.count(wrapper);
        if (results > 0) {
            return R.failed(400, "链接名重复");
        }
        links.setState(2);
        links.setMallType(mallConfig.mallType);
        linksService.update(links);
        return R.success();
    }


    /**
     * 批量更新链接信息
     *
     * @param links
     * @return
     */
    @PostMapping("/updateBatchById")
    @ApiOperation(value = "批量更新店铺信息")
    public R update(@RequestBody List<Links> links) {
        linksService.updateBatchById(links);
        return R.success();
    }

    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        linksService.delete(id);
        return R.success();
    }


    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")

    public R deleteBatch(@RequestBody List<String> ids) {
        linksService.removeByIds(ids);
        return R.success();
    }


    @PostMapping("/updatePublish")
    @ApiOperation(value = "批量发布")
    public R updatePublish(@RequestBody List<String> ids) {
        linksService.updateByPublish(ids, "1");
        return R.success();
    }

    @PostMapping("/updateNotPublish")
    @ApiOperation(value = "批量取消发布")
    @ApiImplicitParam(name = "type", value = "1：发布  2：未发布", required = true,
            dataType = "Integer", paramType = "query")
    public R updateNotPublish(@RequestBody List<String> ids) {
        linksService.updateByPublish(ids, "2");
        return R.success();
    }

}

