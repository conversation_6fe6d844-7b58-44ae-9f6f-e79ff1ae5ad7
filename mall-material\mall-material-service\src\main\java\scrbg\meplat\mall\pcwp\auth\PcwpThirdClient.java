package scrbg.meplat.mall.pcwp.auth;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import scrbg.meplat.mall.pcwp.PcwpClient;
import scrbg.meplat.mall.pcwp.PcwpRes;
import scrbg.meplat.mall.pcwp.auth.model.SupplierRes;

@FeignClient(name = "pcwp-third-service", url = "${mall.prodPcwp2Url02}")
public interface PcwpThirdClient extends PcwpClient {

    @GetMapping("/thirdapi/outer/getSupplierBycreditCode")
    PcwpRes<SupplierRes> getSupplierByCreditCode(
            @RequestParam("creditCode") String creditCode,
            @RequestHeader("token") String token
    );

}
