package scrbg.meplat.mall.dto.payment;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @program: maill_api
 * @description: 蜀道企业导入Excel对象
 * @author: 代文翰
 * @create: 2023-09-05 23:33
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@HeadRowHeight(40)
@ColumnWidth(25)
@ContentRowHeight(40)
public class SdImportResultVo implements Serializable {
    private static final long serialVersionUID = 1L;
    @ExcelProperty(value = "企业名称")
    private String enterpriseName;
    @ExcelProperty(value = "隶属企业")

    private String affiliationEnterprise;
    @ExcelProperty(value = "企业类别(1:一类 2:二类 3:三类)")

    private String enterpriseCategory;
    @ExcelProperty(value = "调整")

    private String adjust;
    @ExcelProperty(value = "企业性质")

    private String enterpriseNature;

    @ExcelProperty(value = "状态")
    private String status;

    @ExcelProperty(value = "失败原因")
    private String failReason;

}
