package scrbg.meplat.mall.dto.DeviceDemand;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-11-04 11:20
 */
@Data
public class UpdateDemandCheckStateDTO {

    @ApiModelProperty(value = "id", required = true)
    @NotEmpty(message = "id不能为空！")
    private List<String> ids;

    @ApiModelProperty(value = "状态（1通过 2不通过）", required = true)
    @NotNull(message = "状态不能为空！")
    @Max(value = 2, message = "状态输入错误！")
    @Min(value = 1, message = "状态输入错误！")
    private Integer checkState;

    @ApiModelProperty(value = "审核失败原因")
    private String failReason;

}
