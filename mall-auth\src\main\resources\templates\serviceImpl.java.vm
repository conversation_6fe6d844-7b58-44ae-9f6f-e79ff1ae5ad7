package ${package.ServiceImpl};

import ${package.Entity}.${entity};
import ${package.Mapper}.${table.mapperName};
import ${package.Service}.${table.serviceName};
import scrbg.meplat.mallauth.config.redis.redisson.NotResubmit;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import java.util.*;
/**
 * @描述：$!{table.comment} 服务类
 * @作者: ${author}
 * @日期: ${date}
 */
@Service
public class ${table.serviceImplName} extends ${superServiceImplClass}<${table.mapperName}, ${entity}> implements ${table.serviceName}{
    @Override

    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<${entity}> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String keywords = (String) innerMap.get("keywords");
        IPage<${entity}> page = this.page(
        new Query<${entity}>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void create(${entity} ${table.entityPath}) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(${table.entityPath});
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void update(${entity} ${table.entityPath}) {
        super.updateById(${table.entityPath});
    }


    @Override
    public ${entity} getById(String id) {
        return super.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void delete(String id) {
        super.removeById(id);
    }

     @Override
     @Transactional(rollbackFor = Exception.class)
     @NotResubmit
     public void deleteBatch(List<String> ids ) {
        super.removeByIds(ids);
        }


        }
