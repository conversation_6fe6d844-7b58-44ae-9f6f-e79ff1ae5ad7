package scrbg.meplat.mall.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import scrbg.meplat.mall.entity.AskAnswer;
import scrbg.meplat.mall.vo.user.userCenter.AskAnswerVo;

import java.util.List;

/**
 * @描述：问答 Mapper 接口
 * @作者: y
 * @日期: 2022-11-22
*/
@Mapper
@Repository
public interface AskAnswerMapper extends BaseMapper<AskAnswer> {

    List<AskAnswerVo> listByDeviceDemand(IPage<AskAnswerVo> pages,   @Param("ew") QueryWrapper<AskAnswerVo> q);
}




