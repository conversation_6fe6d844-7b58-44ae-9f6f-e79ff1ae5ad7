package scrbg.meplat.mall.config.minio;

import cn.hutool.core.util.StrUtil;

public enum ViewContentType {
    DEFAULT("default","application/octet-stream"),

    /**
     * 图片格式
     */
    JPG("jpg", "image/jpeg"),
    TIFF("tiff", "image/tiff"),
    GIF("gif", "image/gif"),
    JFIF("jfif", "image/jpeg"),
    PNG("png", "image/png"),
    TIF("tif", "image/tiff"),
    ICO("ico", "image/x-icon"),
    JPEG("jpeg", "image/jpeg"),
    WBMP("wbmp", "image/vnd.wap.wbmp"),
    FAX("fax", "image/fax"),
    NET("net", "image/pnetvue"),
    JPE("jpe", "image/jpeg"),
    RP("rp", "image/vnd.rn-realpix"),

    /**
     * 视频格式
     */
    MAP4("mp4", "video/mp4"),
    MOV("mov", "video/quicktime"),
    AVI("avi", "video/x-msvideo"),
    FIV("fiv", "video/x-flv"),
    WEBM("webm", "video/webm");

    private String prefix;

    private String type;

    public static String getContentType(String prefix){
        if(StrUtil.isEmpty(prefix)){
            return null;
        }
        prefix = prefix.substring(prefix.lastIndexOf(".") + 1);
        for (ViewContentType value : ViewContentType.values()) {
            if(prefix.equalsIgnoreCase(value.getPrefix())){
                return value.getType();
            }
            if(prefix.equalsIgnoreCase("3gp")){
                return "video/3gpp";
            }
        }
        return null;
    }

    ViewContentType(String prefix, String type) {
        this.prefix = prefix;
        this.type = type;
    }

    public String getPrefix() {
        return prefix;
    }

    public String getType() {
        return type;
    }
}
