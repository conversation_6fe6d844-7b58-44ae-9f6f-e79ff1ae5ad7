/*
 Navicat Premium Data Transfer

 Source Server         : ************
 Source Server Type    : MySQL
 Source Server Version : 80025 (8.0.25-15.1)
 Source Host           : ************:3306
 Source Schema         : mall-device-dev

 Target Server Type    : MySQL
 Target Server Version : 80025 (8.0.25-15.1)
 File Encoding         : 65001

 Date: 25/12/2024 16:25:55
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for account
-- ----------------------------
DROP TABLE IF EXISTS `account`;
CREATE TABLE `account` (
  `bill_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '单据ID',
  `bill_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '单据编号',
  `bill_date` datetime DEFAULT NULL COMMENT '对账日期',
  `work_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '流程ID',
  `state` int DEFAULT NULL COMMENT '状态',
  `org_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '机构',
  `org_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '机构ID',
  `storage_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '供应商',
  `storage_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '供应商ID',
  `source_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '源单编号',
  `source_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '源单Id',
  `source_date` datetime DEFAULT NULL COMMENT '源单日期',
  `source_source_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '源单的源单编号',
  `source_source_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT ' 源单的源单Id',
  `picker` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '供应商经办人',
  `pickerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '供应商经办人Id',
  `receiver_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '收料/退库人员Id',
  `receiver_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '收料/退库人员名称',
  `totala_mount` decimal(10,2) DEFAULT NULL COMMENT '金额合计',
  `remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '备注',
  `recorder_time` datetime DEFAULT NULL COMMENT '录入时间',
  `recorder_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '录入人',
  `recorder` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '录入人ID',
  `last_modifier` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT ' 最后修改人',
  `last_modifier_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '最后修改人ID',
  `last_modify_time` datetime DEFAULT NULL COMMENT '最后修改时间',
  `last_auditor_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '最后审核人ID',
  `last_auditor` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '最后审核人',
  `last_audit_time` datetime DEFAULT NULL COMMENT '最后审核时间',
  `credit_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '供应商社会信用代码',
  PRIMARY KEY (`bill_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='pcwp1对账单';

-- ----------------------------
-- Records of account
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for account_dtl
-- ----------------------------
DROP TABLE IF EXISTS `account_dtl`;
CREATE TABLE `account_dtl` (
  `dtl_id` varchar(233) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '设置明细ID',
  `bill_id` varchar(233) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '设置单据ID',
  `item_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '设置物料名称',
  `item_Id` varchar(233) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '设置物料ID',
  `item_model` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '规格',
  `item_unit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '单位',
  `cai_zhi` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '设置材质',
  `qty` decimal(10,0) DEFAULT NULL COMMENT '数量',
  `listing_price` decimal(10,2) DEFAULT NULL COMMENT '挂牌价',
  `floating_price` decimal(10,2) DEFAULT NULL COMMENT '下浮费用',
  `fixed_price` decimal(10,2) DEFAULT NULL COMMENT '固定价',
  `price` decimal(10,2) DEFAULT NULL COMMENT '合计单价',
  `amount` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '合计金额',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '卸货地址',
  `item_classId` varchar(233) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '类别ID',
  `item_className` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '类别名称',
  PRIMARY KEY (`dtl_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='pcwp1对账单明细';

-- ----------------------------
-- Records of account_dtl
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for account_statement
-- ----------------------------
DROP TABLE IF EXISTS `account_statement`;
CREATE TABLE `account_statement` (
  `bill_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '对账单id',
  `bill_no` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '对账单编号',
  `org_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '所属机构id',
  `org_name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '所属机构名称',
  `site_receiving_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '收料单id',
  `site_receiving_name` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '收料单编号',
  `receiving_personnel_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '收料人员id',
  `receiving_personnel_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '收料人员名称',
  `supplier_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '供应商id',
  `supplier_name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '供应商名称',
  `personnel_information_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '供应商经办人id',
  `personnel_information_name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '供应商经办人名称',
  `total_amount` decimal(18,4) DEFAULT NULL COMMENT '金额合计',
  `account_date` datetime DEFAULT NULL COMMENT '对账日期',
  `state` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '状态（0：已发布，1.作废）',
  `remarks` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '备注',
  `source_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '源单id',
  `source_number` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '源单编号',
  `work_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '流程id',
  `supplier_return_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '退货单id',
  `supplier_return_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '退货单编号',
  `warehouse_keeper_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '库管人员id',
  `warehouse_keeper_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '库管人员名称',
  `generation_date` datetime DEFAULT NULL COMMENT '退货/收料日期',
  `account_source` char(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '对账单来源（1.收料 2.退货）',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `credit_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '统一社会信用代码',
  PRIMARY KEY (`bill_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='pcwp2对账单';

-- ----------------------------
-- Records of account_statement
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for account_statement_dtl
-- ----------------------------
DROP TABLE IF EXISTS `account_statement_dtl`;
CREATE TABLE `account_statement_dtl` (
  `dtl_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '对账单明细id',
  `bill_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '对账单id',
  `material_class_id` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '物资类别id(1级类别id/2级类别id/..)',
  `material_class_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '物资类别名称(1级类别名称/2级类别名称/..)',
  `spec` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '规格型号',
  `texture` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '材质',
  `unit` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '计量单位',
  `quantity` decimal(18,4) DEFAULT NULL COMMENT '收料数量',
  `listing_price` decimal(18,4) DEFAULT NULL COMMENT '挂牌价',
  `floating_cost` decimal(18,4) DEFAULT NULL COMMENT '下浮费用',
  `fixed_expenses` decimal(18,4) DEFAULT NULL COMMENT '固定费',
  `total_unit_price` decimal(18,4) DEFAULT NULL COMMENT '合计单价',
  `total_amount` decimal(18,4) DEFAULT NULL COMMENT '合计金额',
  `unloading_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '卸货地址',
  `material_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '物资id',
  `material_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '物资名称',
  PRIMARY KEY (`dtl_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='对账单明细';

-- ----------------------------
-- Records of account_statement_dtl
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for ad_picture
-- ----------------------------
DROP TABLE IF EXISTS `ad_picture`;
CREATE TABLE `ad_picture` (
  `picture_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '图片id',
  `picture_url_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '图片记录id',
  `picture_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '存放地址',
  `picture_type` tinyint unsigned DEFAULT NULL COMMENT '链接类型（1：无  2：内部链接地址 3：外部链接地址）',
  `picture_link_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '点击图片链接地址',
  `use_type` tinyint unsigned DEFAULT NULL COMMENT '面显示位置（1，平台首页，2，商城首页',
  `sort` int DEFAULT NULL COMMENT '排序',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '修改时间',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '图片备注信息',
  `state` tinyint unsigned DEFAULT NULL COMMENT '状态（1：发布  2：未发布）',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
  PRIMARY KEY (`picture_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='广告图片';

-- ----------------------------
-- Records of ad_picture
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for ask_answer
-- ----------------------------
DROP TABLE IF EXISTS `ask_answer`;
CREATE TABLE `ask_answer` (
  `ask_answer_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '问答id',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '标题',
  `type` tinyint DEFAULT NULL COMMENT '信息类型（0求购1求租2招标）',
  `send_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '发件人Id',
  `send_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '发件人名称',
  `send_type` tinyint DEFAULT NULL COMMENT '发件人类型（0店铺1用户2平台）',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '内容',
  `relevance_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '关联id',
  `send_date` datetime DEFAULT NULL COMMENT '发件时间',
  `accept_ids` tinyint DEFAULT NULL COMMENT '收件人ids（逗号分割）',
  `state` tinyint DEFAULT NULL COMMENT '状态（0显示1不显示）默认显示',
  `sort` int DEFAULT NULL COMMENT '排序',
  `is_delete` tinyint unsigned DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  `is_file` tinyint DEFAULT NULL COMMENT '是否附件（0否1是）',
  PRIMARY KEY (`ask_answer_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='问答';

-- ----------------------------
-- Records of ask_answer
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for audit_record
-- ----------------------------
DROP TABLE IF EXISTS `audit_record`;
CREATE TABLE `audit_record` (
  `audit_record_id` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '审核id',
  `relevance_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '关联id',
  `relevance_type` tinyint NOT NULL COMMENT '关联类型（1缴费审核）',
  `audit_type` tinyint NOT NULL COMMENT '审核类型（1普通审核2变更审核）',
  `result_type` tinyint NOT NULL COMMENT '结果类型（1通过2未通过）',
  `audit_result` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '审核结果',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
  `sort` int DEFAULT NULL COMMENT '排序',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  PRIMARY KEY (`audit_record_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='审核记录';

-- ----------------------------
-- Records of audit_record
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for bidding_info
-- ----------------------------
DROP TABLE IF EXISTS `bidding_info`;
CREATE TABLE `bidding_info` (
  `bidding_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '招标id',
  `purchasen_company_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '采购公司id',
  `project_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '项目id',
  `earnest_money` decimal(18,2) DEFAULT NULL COMMENT '投标保证金',
  `linkman_ids` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '联系人ids',
  `start_time` datetime DEFAULT NULL COMMENT '报名开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '报名结束时间',
  `bid_limit` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '投标人限定',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
  `sort` tinyint DEFAULT NULL COMMENT '排序',
  `state` tinyint DEFAULT NULL COMMENT '状态：1启用 0停用',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  PRIMARY KEY (`bidding_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='招标详细信息';

-- ----------------------------
-- Records of bidding_info
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for bidding_project_info
-- ----------------------------
DROP TABLE IF EXISTS `bidding_project_info`;
CREATE TABLE `bidding_project_info` (
  `project_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '目录id',
  `bidding_type_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '招标类型id',
  `project_code` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '项目编号',
  `project_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '项目名称',
  `budget_money` decimal(18,2) DEFAULT NULL COMMENT '预算金额',
  `address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '项目地址',
  `organization_way` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '招标组织形式',
  `bidding_way` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '招标方式',
  `apply_way` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '报名方式',
  `examine_way` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '资料审查方式',
  `project_describe` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '项目描述',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `state` tinyint DEFAULT NULL COMMENT '状态：1启用 0停用',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除 1: 删除 0:未删除',
  `sort` int DEFAULT NULL COMMENT '排序',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  PRIMARY KEY (`project_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='招标项目信息';

-- ----------------------------
-- Records of bidding_project_info
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for bidding_type
-- ----------------------------
DROP TABLE IF EXISTS `bidding_type`;
CREATE TABLE `bidding_type` (
  `bidding_type_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类型id',
  `bidding_type_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '类型名称',
  `state` tinyint DEFAULT NULL COMMENT '状态：1启用 0停用',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
  `sort` int DEFAULT NULL COMMENT '排序',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  PRIMARY KEY (`bidding_type_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='招标类型表';

-- ----------------------------
-- Records of bidding_type
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for brand
-- ----------------------------
DROP TABLE IF EXISTS `brand`;
CREATE TABLE `brand` (
  `brand_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '品牌id',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '品牌名',
  `logo` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '品牌logo地址',
  `descript` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '介绍',
  `state` tinyint NOT NULL DEFAULT '0' COMMENT '显示状态[0-不显示；1-显示]',
  `sort` int DEFAULT NULL COMMENT '排序',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `product_type` tinyint DEFAULT NULL COMMENT '商品类型：0物资 1设备 2周材（物资） 3周材（设备） 4二手设备 5租赁设备',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  `class_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '分类id',
  `class_name` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '分类名称',
  PRIMARY KEY (`brand_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='品牌';

-- ----------------------------
-- Records of brand
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for category_brand_relation
-- ----------------------------
DROP TABLE IF EXISTS `category_brand_relation`;
CREATE TABLE `category_brand_relation` (
  `category_brand_relation_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '关联id',
  `brand_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '品牌id',
  `class_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '分类id',
  `brand_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '品牌名称',
  `class_name` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '分类名称',
  `is_delete` tinyint unsigned DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `sort` int DEFAULT NULL COMMENT '排序',
  `state` tinyint DEFAULT NULL COMMENT '状态',
  `product_type` tinyint DEFAULT NULL COMMENT '商品类型：0物资 1设备 2周材（物资） 3周材（设备） 4二手设备 5租赁设备 ',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  PRIMARY KEY (`category_brand_relation_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='品牌分类关联';

-- ----------------------------
-- Records of category_brand_relation
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for category_columns
-- ----------------------------
DROP TABLE IF EXISTS `category_columns`;
CREATE TABLE `category_columns` (
  `column_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '栏目id',
  `column_number` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '栏目编号',
  `column_name` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '栏目名称',
  `sort` int DEFAULT NULL COMMENT '排序值',
  `state` tinyint DEFAULT NULL COMMENT '栏目状态（1：启用 0：停用）',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '修改时间',
  `gmt_release` datetime DEFAULT NULL COMMENT '发布时间',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注信息',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除（-1：删除  0：未删除）',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型（0：物资 1：装备）',
  PRIMARY KEY (`column_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Records of category_columns
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for column_floor
-- ----------------------------
DROP TABLE IF EXISTS `column_floor`;
CREATE TABLE `column_floor` (
  `id` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '栏目-楼层id',
  `column_id` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '栏目id',
  `floor` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '楼层id',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `sort` int DEFAULT NULL COMMENT '排序',
  `state` tinyint DEFAULT NULL COMMENT '楼层状态（1：显示  0：不显示）',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Records of column_floor
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for content
-- ----------------------------
DROP TABLE IF EXISTS `content`;
CREATE TABLE `content` (
  `content_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '内容id',
  `file_name` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '文件名',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '内容',
  `source` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '源码',
  `state` tinyint unsigned DEFAULT NULL COMMENT '状态',
  `comments` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '评论',
  `sort` int DEFAULT NULL COMMENT '排序',
  `default_picture_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '默认图片路径',
  `big_picture_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '大图片路径',
  `small_picture_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '小图片路径',
  `tiny_picture_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '微小图片路径',
  `home` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '主页',
  `top` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '顶部',
  `title` varchar(400) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '标题',
  `info_type` tinyint unsigned DEFAULT NULL COMMENT '信息类型',
  `subtitle` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '副标题',
  `author` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '作者',
  `file_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '文件路径',
  `home_picture_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '主图片路径',
  `view_count` int DEFAULT NULL COMMENT '浏览次数',
  `programa_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '栏目id',
  `programa_key` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '栏目Key',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `gmt_release` datetime DEFAULT NULL COMMENT '发布时间',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
  `summary` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '概述',
  `agreement_type` varchar(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '协议类型（1.个人，2个体户，3企业）',
  PRIMARY KEY (`content_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='内容';

-- ----------------------------
-- Records of content
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for content_catalogue
-- ----------------------------
DROP TABLE IF EXISTS `content_catalogue`;
CREATE TABLE `content_catalogue` (
  `catalogue_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '内容目录id',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '名称',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '类型',
  `state` tinyint unsigned DEFAULT NULL COMMENT '状态',
  `comments` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '注解',
  `sort` int DEFAULT NULL COMMENT '排序',
  `programa_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '程序代码',
  `Is_home` tinyint unsigned DEFAULT NULL COMMENT '是否为主页',
  `Is_navigation` tinyint unsigned DEFAULT NULL COMMENT '是否为导航',
  `page_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '页面地址',
  `parent_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '父页id',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  PRIMARY KEY (`catalogue_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='内容目录表';

-- ----------------------------
-- Records of content_catalogue
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for contract
-- ----------------------------
DROP TABLE IF EXISTS `contract`;
CREATE TABLE `contract` (
  `contract_id` char(36) NOT NULL COMMENT '合同id',
  `contract_no` char(50) NOT NULL COMMENT '合同编号',
  `name` varchar(255) DEFAULT NULL COMMENT '合同名称',
  `type` tinyint DEFAULT NULL COMMENT '合同类型（1入驻合同2续费合同）',
  `party_a_org_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '甲方机构id',
  `party_a_org_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '甲方机构名称',
  `party_b_org_id` char(36) DEFAULT NULL COMMENT '乙方机构id',
  `party_b_org_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '乙方机构名称',
  `sort` int DEFAULT NULL COMMENT '排序',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `state` tinyint DEFAULT NULL COMMENT '状态',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  `contract_json` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '合同JSON完整数据',
  `contract_year` int DEFAULT NULL COMMENT '合同签订年份',
  `contract_year_index` int(4) unsigned zerofill DEFAULT NULL COMMENT '当年合同签订序号',
  PRIMARY KEY (`contract_id`),
  UNIQUE KEY `uk_contract_no` (`contract_no`) USING BTREE COMMENT '合同编号唯一索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='合同';

-- ----------------------------
-- Records of contract
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for deposit_bank
-- ----------------------------
DROP TABLE IF EXISTS `deposit_bank`;
CREATE TABLE `deposit_bank` (
  `bank_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '开户银行id',
  `bank_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '银行名称',
  `bank_count` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '银行账号',
  `phone` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '联系电话',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `enterprise_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '企业id',
  `is_default` tinyint unsigned DEFAULT NULL COMMENT '是否是该企业默认银行账户 1: 是 0:否',
  `is_delete` tinyint unsigned DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
  `state` tinyint DEFAULT NULL COMMENT '状态',
  `sort` int DEFAULT NULL COMMENT '排序',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  PRIMARY KEY (`bank_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='开户银行信息表';

-- ----------------------------
-- Records of deposit_bank
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for device_demand
-- ----------------------------
DROP TABLE IF EXISTS `device_demand`;
CREATE TABLE `device_demand` (
  `demand_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '需求id',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '名称',
  `spec` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '规格型号',
  `keyword` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '关键字',
  `relevance_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '关联id（根据发布类型存储不同id）',
  `class_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '分类id',
  `class_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '分类名称',
  `brand_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '品牌id',
  `brand_name` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '品牌名称',
  `province` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '省份',
  `city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '城市',
  `county` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '县区',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '地址',
  `longitude` decimal(11,7) DEFAULT NULL COMMENT '经度',
  `latitude` decimal(11,7) DEFAULT NULL COMMENT '纬度',
  `num` int DEFAULT NULL COMMENT '数量',
  `demand_type` tinyint DEFAULT NULL COMMENT '需求类型（1租赁设备2二手设备）',
  `num_unit` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '计量单位',
  `duration` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '租赁时长',
  `enter_date` datetime DEFAULT NULL COMMENT '进场时间',
  `linkman` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '联系人',
  `linkman_phone` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '联系电话',
  `release_date` datetime DEFAULT NULL COMMENT '发布时间',
  `stop_date` datetime DEFAULT NULL COMMENT '截止时间',
  `budget_amount` decimal(18,2) DEFAULT NULL COMMENT '预算金额',
  `sort` int DEFAULT NULL COMMENT '排序',
  `state` tinyint DEFAULT NULL COMMENT '需求状态（0待审核1已审核2询价中3报价中）',
  `check_state` tinyint DEFAULT NULL COMMENT '审核状态（0待审核1通过2不通过）',
  `is_delete` tinyint unsigned DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  `release_type` tinyint DEFAULT NULL COMMENT '发布类型（0店铺1用户2平台）',
  `class_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '分类路径（/分割）',
  `fail_reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '审核失败原因',
  `launch` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '发起方(企业)',
  `start_address` varchar(255) DEFAULT NULL COMMENT '出发地',
  `end_address` varchar(255) DEFAULT NULL COMMENT '目的地',
  PRIMARY KEY (`demand_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备需求';

-- ----------------------------
-- Records of device_demand
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for enterprise_info
-- ----------------------------
DROP TABLE IF EXISTS `enterprise_info`;
CREATE TABLE `enterprise_info` (
  `enterprise_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '企业id',
  `enterprise_number` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '企业编号',
  `enterprise_name` varchar(125) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '企业名称（公司名称、供应商公司名称 ）',
  `social_credit_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '统一社会信用代码',
  `legal_representative` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '法定代表人',
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '企业邮箱',
  `operator` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '经营者',
  `creation_time` datetime DEFAULT NULL COMMENT '注册日期',
  `place_of_business` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '经营场所',
  `license_term` datetime DEFAULT NULL COMMENT '营业执照有效期',
  `registered_capital` decimal(18,2) DEFAULT NULL COMMENT '注册资本(万)',
  `provinces` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '注册省份',
  `city` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '注册市级',
  `county` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '注册县、区',
  `detailed_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '注册详细地址',
  `main_business` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '经营范围',
  `business_license` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '营业执照（地址）',
  `business_license_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '营业执照（记录id）',
  `card_portrait_face_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '身份证人像面(记录id)',
  `card_portrait_face` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '身份证人像面(存地址)',
  `card_portrait_national_emblem_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '身份证国徽面（记录id）',
  `card_portrait_national_emblem` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '身份证国徽面（存地址）',
  `admin_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '管理员姓名',
  `admin_phone` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '管理员电话',
  `admin_number` varchar(125) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '管理员身份证号',
  `income_call_time` datetime DEFAULT NULL COMMENT '来电时间',
  `income_call_content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '来电内容',
  `processing_departnment` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '处理部门',
  `processing_time` datetime DEFAULT NULL COMMENT '处理时间',
  `handling_result` tinyint DEFAULT NULL COMMENT '办理状态： 0:办理中 1:办理成功',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '修改时间',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `sort` int DEFAULT NULL COMMENT '排序',
  `state` tinyint DEFAULT '1' COMMENT '状态 1：启用  0:停用',
  `is_supplier` tinyint DEFAULT '0' COMMENT '是否为供应商：0 : 否 1 ：待定  2：是',
  `enterprise_type` tinyint DEFAULT NULL COMMENT '企业类型：0：个体户  1：企业  2：个人',
  `enterprise_business_type` tinyint DEFAULT NULL COMMENT '企业营业状态 ：0： 停业 1：营业',
  `mall_type` tinyint unsigned DEFAULT NULL COMMENT '商城类型：0：物资商场, 1：设备商城 ',
  `interior_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '内部企业id',
  `is_material_mall` tinyint NOT NULL DEFAULT '0' COMMENT '是否是物资商城企业',
  `is_device_mall` tinyint NOT NULL DEFAULT '0' COMMENT '是否是装备商城企业',
  `is_no_supplier_audit` tinyint NOT NULL DEFAULT '0' COMMENT '是否未通过供应商审核',
  `import_type` tinyint NOT NULL DEFAULT '0' COMMENT '导入类型：1excel',
  `audit_fail_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '审核失败原因',
  `short_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '机构简码',
  `is_pcwp` tinyint NOT NULL DEFAULT '0' COMMENT '是否为pcwp入库供应商(1.是，0 否)',
  `admin_company` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '管理员公司',
  `admin_department` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '管理员单位',
  `admin_gender` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '管理员性别',
  `admin_user_sn` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '管理员编号',
  `admin_position` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '管理员职位',
  `shu_dao_flag` tinyint DEFAULT '0' COMMENT '是否为蜀道企业(1是，0否)',
  `open_bank_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '开户银行名称',
  `bank_account` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '银行账号',
  `bank_account_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '银行户名',
  `taxpayer_no` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '单位税号',
  `bil_remark` varchar(400) DEFAULT NULL COMMENT '注册开票备注',
  `version` int NOT NULL DEFAULT '1' COMMENT '乐观锁',
  PRIMARY KEY (`enterprise_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='企业附加信息表';

-- ----------------------------
-- Records of enterprise_info
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for file
-- ----------------------------
DROP TABLE IF EXISTS `file`;
CREATE TABLE `file` (
  `file_Id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '附件id',
  `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '附件名称',
  `relevance_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '关联id',
  `relevance_type` tinyint DEFAULT NULL COMMENT '关联类型（1商品2问答3消息4店铺,5内容6企业注册7平台消息8用户注册9开店单据）',
  `url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '附件地址',
  `sort` int DEFAULT NULL COMMENT '排序',
  `is_main` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否主图，1是0否',
  `file_type` tinyint DEFAULT NULL COMMENT '媒体类型 1:图片2视频3附件',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `remarks` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '备注',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  `file_far_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '附件远程id',
  `img_type` tinyint NOT NULL DEFAULT '0' COMMENT '图片类型（0普通图片1小图）',
  `programa_key` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '栏目Key',
  `programa_key_two` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '符栏目key',
  PRIMARY KEY (`file_Id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='通用附件';

-- ----------------------------
-- Records of file
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for file_record
-- ----------------------------
DROP TABLE IF EXISTS `file_record`;
CREATE TABLE `file_record` (
  `record_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '记录id',
  `object_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '对象名称',
  `object_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '对象路径',
  `non_ip_object_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '不含IP/域名的对象路径',
  `bucket_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '桶名称',
  `object_size_kb` decimal(18,2) DEFAULT NULL COMMENT '对象大小kb',
  `object_size_mb` decimal(18,2) DEFAULT NULL COMMENT '对象大小mb',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间	',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间	',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `sort` int DEFAULT NULL COMMENT '排序',
  `state` tinyint DEFAULT NULL COMMENT '状态',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除：（ -1: 删除  0:未删除）',
  PRIMARY KEY (`record_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件上传记录信息';

-- ----------------------------
-- Records of file_record
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for file_record_delete
-- ----------------------------
DROP TABLE IF EXISTS `file_record_delete`;
CREATE TABLE `file_record_delete` (
  `file_record_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '记录id',
  `record_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '删除记录id',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  PRIMARY KEY (`file_record_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='附件删除表';

-- ----------------------------
-- Records of file_record_delete
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for financial_products
-- ----------------------------
DROP TABLE IF EXISTS `financial_products`;
CREATE TABLE `financial_products` (
  `financial_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '金融产品id',
  `financial_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '金融产品名称',
  `picture_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '图片路径',
  `products_type` tinyint unsigned DEFAULT NULL COMMENT '产品类型',
  `total_amount` decimal(18,2) DEFAULT NULL COMMENT '订单总价格',
  `interest_rate` decimal(18,2) DEFAULT NULL COMMENT '产品利率',
  `financing_period` char(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '融资期限',
  `repayment_type` tinyint unsigned DEFAULT NULL COMMENT '还款方式',
  `Lending_duration` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '放款时长',
  `application_materials` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '申请材料',
  `application_conditions` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '申请条件',
  `product_introduction` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '产品简介',
  `use_area` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '使用区域',
  `product_features` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '产品特色',
  `application_scenario` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '应用场景',
  `shop_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '店铺id',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除： -1: 删除 0:未删除',
  `state` tinyint DEFAULT NULL COMMENT '0:已发布 1:未发布',
  `sort` int DEFAULT NULL COMMENT '排序',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城  ',
  PRIMARY KEY (`financial_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='金融产品';

-- ----------------------------
-- Records of financial_products
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for floor
-- ----------------------------
DROP TABLE IF EXISTS `floor`;
CREATE TABLE `floor` (
  `floor_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '楼层id',
  `column_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '栏目id',
  `column_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '栏目名称',
  `floor_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '楼层名称',
  `floor_name_text` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '楼层名称后小字',
  `img_url_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '主图id',
  `img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '楼层主图链接',
  `main_img_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '点击主图链接地址',
  `floor_product_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '商品类别',
  `use_page` tinyint unsigned DEFAULT NULL COMMENT '使用页面（枚举类型）待讨论',
  `gmt_release` datetime DEFAULT NULL COMMENT '发布时间',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `state` tinyint DEFAULT NULL COMMENT '楼层状态（1：显示  0：不显示）',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `sort` int DEFAULT NULL COMMENT '排序',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  PRIMARY KEY (`floor_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='客户端商品展示楼层';

-- ----------------------------
-- Records of floor
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for floor_goods
-- ----------------------------
DROP TABLE IF EXISTS `floor_goods`;
CREATE TABLE `floor_goods` (
  `floor_goods_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '楼层商品id',
  `order_value` int DEFAULT NULL COMMENT '商品排序值',
  `floor_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '显示楼层id',
  `goods_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '显示的商品id',
  `goods_picture_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '图片地址',
  `gmt_release` datetime DEFAULT NULL COMMENT '发布时间',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '修改时间',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人id',
  `founder_name` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `state` tinyint DEFAULT NULL COMMENT '商品状态（1：发布（显示）  0：未发布（不显示））',
  `sort` int DEFAULT NULL COMMENT '排序',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  PRIMARY KEY (`floor_goods_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='楼层显示的商品';

-- ----------------------------
-- Records of floor_goods
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for import_shudao_enterprise
-- ----------------------------
DROP TABLE IF EXISTS `import_shudao_enterprise`;
CREATE TABLE `import_shudao_enterprise` (
  `import_shudao_enterprise_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `state` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '结果',
  `fail` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '失败原因',
  `enterprise_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '公司名称',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  PRIMARY KEY (`import_shudao_enterprise_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='导入供应商失败的结果';

-- ----------------------------
-- Records of import_shudao_enterprise
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for import_suppler
-- ----------------------------
DROP TABLE IF EXISTS `import_suppler`;
CREATE TABLE `import_suppler` (
  `import_suppler_id` char(36) NOT NULL,
  `enterprise_name` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '供应商名称',
  `social_credit_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '统一社会信用代码',
  `legal_representative` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '法定代表人',
  `admin_phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '联系电话',
  `state` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '结果',
  `fail` varchar(5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '失败原因',
  PRIMARY KEY (`import_suppler_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='导入供应商失败的结果';

-- ----------------------------
-- Records of import_suppler
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for insurance_products
-- ----------------------------
DROP TABLE IF EXISTS `insurance_products`;
CREATE TABLE `insurance_products` (
  `insurance_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '保险id',
  `describes` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '描述',
  `type` tinyint DEFAULT NULL COMMENT '0:交强险 1:车损险 2:三者险',
  `details` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '详情',
  `company` tinyint DEFAULT NULL COMMENT '0:阳光产险 1:长安保险 2:天安财险 3:亚太财险 4:国任保险 5:浙商保险 6:中国人寿保险 7:阳光信保',
  `shop_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '店铺id',
  `project_area` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '项目地区',
  `sort` tinyint DEFAULT NULL COMMENT '排序',
  `state` tinyint DEFAULT NULL COMMENT '状态:1发布 0未发布',
  `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '保险名称',
  `insure` tinyint DEFAULT NULL COMMENT '0:已投 1:未投',
  `years` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '保险年限',
  `age` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '承保年龄',
  `occupation` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '承保职业',
  `notice` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '投保须知',
  `common_problem` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '常见问题',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `gmt_release` datetime DEFAULT NULL COMMENT '发布时间',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型（0物资1设备）',
  PRIMARY KEY (`insurance_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='保险产品';

-- ----------------------------
-- Records of insurance_products
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for interface_logs
-- ----------------------------
DROP TABLE IF EXISTS `interface_logs`;
CREATE TABLE `interface_logs` (
  `log_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '日志id',
  `secret_key` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '秘钥唯一key',
  `business_flag` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '业务标识（当多业务相同key，此字段用于区分开业务）(情况少)',
  `method_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '请求方法',
  `class_package` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '类路径',
  `log_type` tinyint DEFAULT NULL COMMENT '日志类型（1请求远程2请求远程回滚3请求本地4请求本地回滚）',
  `update_before` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '修改前的数据（json）',
  `local_arguments` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '本地方法请求参数（json）',
  `far_arguments` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '请求远程接口参数（json）',
  `is_success` tinyint DEFAULT NULL COMMENT '是否成功（0否1是）',
  `result` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '返回结果（可选）（json）',
  `error_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '错误信息（可选）',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id	',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `remarks` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '备注（记录描述信息）',
  `execute_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '执行时间',
  PRIMARY KEY (`log_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Records of interface_logs
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for invoice
-- ----------------------------
DROP TABLE IF EXISTS `invoice`;
CREATE TABLE `invoice` (
  `invoice_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '发票id',
  `order_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '订单id',
  `product_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '商品id',
  `user_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '用户id',
  `shop_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '店铺id',
  `invoice_state` tinyint DEFAULT NULL COMMENT '0:已申请 1:已开票',
  `invoice_type` tinyint DEFAULT NULL COMMENT '0：增值税发票 1:普通发票',
  `invoice_content` tinyint DEFAULT NULL COMMENT '0:商品明细 1:商品类别',
  `rise_type` tinyint(3) unsigned zerofill DEFAULT NULL COMMENT '（抬头类型）0:个人 1:单位',
  `invoice_title` varchar(255) DEFAULT NULL COMMENT '发票抬头',
  `user_name` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '收票人姓名',
  `user_address` varchar(125) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '收票人地址',
  `user_phone` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '收票人联系电话',
  `email` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '收票人邮箱',
  `company` varchar(125) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '单位名称',
  `duty_paragraph` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '发票税号',
  `register_address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '注册地址',
  `register_phone` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '注册电话',
  `bank` varchar(125) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '开户银行',
  `bank_account` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '银行账号',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '修改时间',
  `gmt_apply` datetime DEFAULT NULL COMMENT '通过时间',
  `gmt_adopt` datetime DEFAULT NULL COMMENT '申请时间',
  `Invoice_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '发票单号',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `sort` int DEFAULT NULL COMMENT '排序',
  `state` tinyint DEFAULT NULL COMMENT '状态',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城  ',
  `order_sn` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '订单号',
  `detail_addr` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '详细地址',
  `order_item_id` varchar(233) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '订单项',
  `amount` decimal(10,0) DEFAULT NULL COMMENT '发票金额',
  PRIMARY KEY (`invoice_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='发票';

-- ----------------------------
-- Records of invoice
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for lbb
-- ----------------------------
DROP TABLE IF EXISTS `lbb`;
CREATE TABLE `lbb` (
  `lbb` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

-- ----------------------------
-- Records of lbb
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for links
-- ----------------------------
DROP TABLE IF EXISTS `links`;
CREATE TABLE `links` (
  `link_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '链接id',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '链接名',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '链接url',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除 -1删除 0未删除',
  `state` tinyint DEFAULT NULL COMMENT '状态 1:发布 0:未发布',
  `sort` int DEFAULT NULL COMMENT '排序',
  `display` tinyint DEFAULT NULL COMMENT '链接显示 0:首页显示 1:首页未显示',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id	',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  PRIMARY KEY (`link_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='友情链接';

-- ----------------------------
-- Records of links
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for material_account_statement
-- ----------------------------
DROP TABLE IF EXISTS `material_account_statement`;
CREATE TABLE `material_account_statement` (
  `bill_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '对账单id',
  `bill_no` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '对账单编号',
  `org_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '所属机构id',
  `org_name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '所属机构名称',
  `site_receiving_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '收料单id',
  `site_receiving_name` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '收料单编号',
  `receiving_personnel_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '收料人员id',
  `receiving_personnel_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '收料人员名称',
  `supplier_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '供应商id',
  `supplier_name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '供应商名称',
  `personnel_information_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '供应商经办人id',
  `personnel_information_name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '供应商经办人名称',
  `total_amount` decimal(18,4) DEFAULT NULL COMMENT '金额合计',
  `account_date` datetime DEFAULT NULL COMMENT '对账日期',
  `state` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '状态',
  `remarks` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '备注',
  `source_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '源单id',
  `source_number` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '源单编号',
  `work_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '流程id',
  `supplier_return_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '退货单id',
  `supplier_return_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '退货单编号',
  `warehouse_keeper_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '库管人员id',
  `warehouse_keeper_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '库管人员名称',
  `generation_date` datetime DEFAULT NULL COMMENT '退货/收料日期',
  `account_source` char(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '对账单来源（1.收料 2.退货）',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `credit_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '统一社会信用代码',
  PRIMARY KEY (`bill_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='pcwp2对账单';

-- ----------------------------
-- Records of material_account_statement
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for material_master_plan
-- ----------------------------
DROP TABLE IF EXISTS `material_master_plan`;
CREATE TABLE `material_master_plan` (
  `bill_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '物资总计划id',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `founder_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人名称',
  `work_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '流程id',
  `bill_no` char(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '计划编号',
  `plan_date` datetime DEFAULT NULL COMMENT '计划日期',
  `project_plan_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '工程计划Id',
  `amount` decimal(18,4) DEFAULT NULL COMMENT '计划金额(元)',
  `tax_rate` decimal(18,4) DEFAULT NULL COMMENT '税率',
  `tax_amount` decimal(18,4) DEFAULT NULL COMMENT '税额',
  `total_amount` decimal(18,4) DEFAULT NULL COMMENT '税价合计',
  `preparer_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '填报人Id',
  `preparer` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '填报人名称',
  `currency_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '币种Id',
  `currency` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '币种名称',
  `purchase_type` int DEFAULT NULL COMMENT '采购方式',
  `org_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '单据机构Id',
  `org_name` varchar(250) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '单据机构名称',
  `remarks` varchar(250) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注信息',
  `base_cur_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '本位币id',
  `base_cur_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '本位币名称',
  `base_cur_rate` decimal(18,4) DEFAULT NULL COMMENT '本位币汇率',
  `base_cur_amount` decimal(18,4) DEFAULT NULL COMMENT '计划金额(本位币)',
  `base_cur_tax_amount` decimal(18,4) DEFAULT NULL COMMENT '税额(本位币)',
  `base_cur_total_amount` decimal(18,4) DEFAULT NULL COMMENT '税价合计(本位币)c',
  `rmb_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '人民币id',
  `rmb_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '人民币名称',
  `rmb_rate` decimal(18,4) DEFAULT NULL COMMENT '人民币汇率',
  `rmb_amount` decimal(18,4) DEFAULT NULL COMMENT '计划金额(人民币)',
  `rmb_tax_amount` decimal(18,4) DEFAULT NULL COMMENT '税额(人民币)',
  `rmb_total_amount` decimal(18,4) DEFAULT NULL COMMENT '税价合计(人民币)',
  `order_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '订单id',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
  `product_type` tinyint DEFAULT NULL COMMENT '商品类型：0物资 1设备 2周材（物资） 3周材（设备）',
  `sort` int DEFAULT NULL COMMENT '排序',
  `state` tinyint DEFAULT NULL COMMENT '状态',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  PRIMARY KEY (`bill_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='物资总计划';

-- ----------------------------
-- Records of material_master_plan
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for material_master_plan_dtl
-- ----------------------------
DROP TABLE IF EXISTS `material_master_plan_dtl`;
CREATE TABLE `material_master_plan_dtl` (
  `dtl_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '物资总计划明细id',
  `bill_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '物资总计划id',
  `project_plan_dtl_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '工程计划明细id',
  `material_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '物资id',
  `material_name` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '物资名称',
  `material_class_id` varchar(150) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '物资类别id(1级类别id/2级类别id/..)',
  `material_class_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '物资类别名称(1级类别名称/2级类别名称/..)',
  `spec` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '规格型号',
  `texture` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '材质',
  `unit` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '计量单位',
  `plan_quantity` decimal(18,4) DEFAULT NULL COMMENT '计划数量',
  `design_quantity` decimal(18,4) DEFAULT NULL COMMENT '设计数量',
  `budget_price` decimal(18,4) DEFAULT NULL COMMENT '预算单价(元)',
  `budget_amount` decimal(18,4) DEFAULT NULL COMMENT '预算金额(元)',
  `market_price` decimal(18,4) DEFAULT NULL COMMENT '市场单价(元)',
  `market_amount` decimal(18,4) DEFAULT NULL COMMENT '市场金额(元)',
  `purchasing_unit_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '采购单位id',
  `purchasing_unit_name` varchar(250) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '采购单位名称',
  `purchase_type` int DEFAULT NULL COMMENT '采购方式',
  `received_quantity` decimal(18,4) DEFAULT NULL COMMENT '已收料数量',
  `summarized_quantity` decimal(18,4) DEFAULT NULL COMMENT '已汇总数量',
  `used_quantity_tender` decimal(18,4) DEFAULT NULL COMMENT '已使用数量(招标)',
  `used_quantity_contract` decimal(18,4) DEFAULT NULL COMMENT '已使用数量(合同)',
  `is_summarized` int DEFAULT NULL COMMENT '是否已汇总(0:否;1:是)',
  `order_item_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '订单项id',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `founder_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人名称',
  `remarks` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT '备注',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
  `product_type` tinyint DEFAULT NULL COMMENT '商品类型：0物资 1设备 2周材（物资） 3周材（设备） 4二手设备 5租赁设备',
  `sort` int DEFAULT NULL COMMENT '排序',
  `state` tinyint DEFAULT NULL COMMENT '状态',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  PRIMARY KEY (`dtl_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='物资总计划明细';

-- ----------------------------
-- Records of material_master_plan_dtl
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for message_informations
-- ----------------------------
DROP TABLE IF EXISTS `message_informations`;
CREATE TABLE `message_informations` (
  `message_informations_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '留言信息id',
  `message_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '留言人id',
  `message_name` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '留言人名称',
  `respond_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '回复人id',
  `respond_name` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '回复人名称',
  `messag_title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '留言标题(总结你的反馈）',
  `messag_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '留言内容(详细描述)',
  `messag_type` tinyint DEFAULT NULL COMMENT '留言类型（1：问题，2：建议）',
  `is_file` tinyint DEFAULT NULL COMMENT '留言是否有附件(0否1是)',
  `messag_date` datetime DEFAULT NULL COMMENT '留言发送时间',
  `respond_title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '回复标题',
  `respond_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '回复内容',
  `respond_date` datetime DEFAULT NULL COMMENT '回复时间',
  `state` tinyint DEFAULT NULL COMMENT '处理状态（0：未处理 1：已处理）',
  `is_delete` tinyint DEFAULT '0' COMMENT '逻辑删除 -1: 删除 0:未删除',
  `sort` int DEFAULT NULL COMMENT '排序',
  `mall_type` tinyint DEFAULT '1' COMMENT '商城类型：0物资商场, 1设备商城 ',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `founder_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `public_display` tinyint DEFAULT '0' COMMENT '公开展示留言（0：不展示 1：展示）',
  `enterprise_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '留言人所属企业名称',
  `shop_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '留言人所属店铺名称',
  `is_interior` tinyint DEFAULT NULL COMMENT '留言人是否是内部用户（0否1是）',
  PRIMARY KEY (`message_informations_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Records of message_informations
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for month_plan
-- ----------------------------
DROP TABLE IF EXISTS `month_plan`;
CREATE TABLE `month_plan` (
  `bill_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '物资月度计划id',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `work_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '流程id',
  `bill_no` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '计划编号',
  `plan_date` datetime DEFAULT NULL COMMENT '计划日期',
  `year` int DEFAULT NULL COMMENT '计划年度',
  `month` int DEFAULT NULL COMMENT '计划月份',
  `year_plan_bill_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '物资年度计划id',
  `year_plan_bill_no` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '物资年度计划编号',
  `amount` decimal(18,4) DEFAULT NULL COMMENT '计划金额',
  `tax_rate` decimal(18,4) DEFAULT NULL COMMENT '税率',
  `tax_amount` decimal(18,4) DEFAULT NULL COMMENT '税额',
  `total_amount` decimal(18,4) DEFAULT NULL COMMENT '税价合计',
  `state` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '状态',
  `preparer_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '填报人id',
  `preparer` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '填报人名称',
  `currency_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '币种id',
  `currency` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '币种名称',
  `org_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '单据机构id',
  `org_name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '单据机构名称',
  `remarks` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '备注信息',
  `base_cur_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '本位币id',
  `base_cur_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '本位币名称',
  `base_cur_rate` decimal(18,4) DEFAULT NULL COMMENT '本位币汇率',
  `base_cur_amount` decimal(18,4) DEFAULT NULL COMMENT '计划金额(本位币)',
  `base_cur_tax_amount` decimal(18,4) DEFAULT NULL COMMENT '税额(本位币)',
  `base_cur_total_amount` decimal(18,4) DEFAULT NULL COMMENT '税价合计(本位币)c',
  `rmb_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '人民币id',
  `rmb_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '人民币名称',
  `rmb_rate` decimal(18,4) DEFAULT NULL COMMENT '人民币汇率',
  `rmb_amount` decimal(18,4) DEFAULT NULL COMMENT '计划金额(人民币)',
  `rmb_tax_amount` decimal(18,4) DEFAULT NULL COMMENT '税额(人民币)',
  `rmb_total_amount` decimal(18,4) DEFAULT NULL COMMENT '税价合计(人民币)',
  `nullify_reason` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '作废原因',
  `nullify_description` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '作废描述',
  `nullify_creator_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '作废人id',
  `nullify_creator` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '作废人',
  `nullify_created` datetime DEFAULT NULL COMMENT '作废时间',
  `last_change_bill_state` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '最新变更单状态',
  `business_type` int DEFAULT NULL COMMENT '业务类型',
  `source_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '源单id',
  `source_number` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '源单编号',
  `supplier_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '供应商id',
  `supplier_name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '供应商名称',
  `credit_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '统一社会信用代码',
  `source_name` varchar(255) DEFAULT NULL COMMENT '合同名称',
  `is_all_send` enum('0','1') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '0' COMMENT '是否全部发送',
  `supplier_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '0内部，1：外部（供应商）',
  `plan_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '计划类型（0要货，发货）',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型（0：物资 1：装备）',
  PRIMARY KEY (`bill_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='pcwp2物资月度计划';

-- ----------------------------
-- Records of month_plan
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for month_plan_dtl
-- ----------------------------
DROP TABLE IF EXISTS `month_plan_dtl`;
CREATE TABLE `month_plan_dtl` (
  `dtl_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '物资月度计划明细id',
  `bill_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '物资月度计划id',
  `year_plan_dtl_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '物资年度计划明细id',
  `material_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '物资id',
  `material_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '物资名称',
  `material_class_id` char(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '物资类别id(1级类别id/2级类别id/..)',
  `material_class_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '物资类别名称(1级类别名称/2级类别名称/..)',
  `spec` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '规格型号',
  `texture` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '材质',
  `unit` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '计量单位',
  `quantity` decimal(18,3) DEFAULT NULL COMMENT '数量',
  `price` decimal(18,4) DEFAULT NULL COMMENT '单价(元)',
  `amount` decimal(18,4) DEFAULT NULL COMMENT '金额(元)',
  `purchase_type` int DEFAULT NULL COMMENT '采购方式',
  `purchasing_unit_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '采购单位id',
  `purchasing_unit_name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '采购单位名称',
  `purchased_quantity` decimal(18,3) DEFAULT NULL COMMENT '已采购数量',
  `used_quantity_tender` decimal(18,4) DEFAULT NULL COMMENT '已使用数量(招标)',
  `used_quantity_contract` decimal(18,4) DEFAULT NULL COMMENT '已使用数量(合同)',
  `is_send` enum('1','0') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '0' COMMENT '是否生成发货计划',
  PRIMARY KEY (`dtl_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='pcwp2物资月度计划明细';

-- ----------------------------
-- Records of month_plan_dtl
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for month_send_plan
-- ----------------------------
DROP TABLE IF EXISTS `month_send_plan`;
CREATE TABLE `month_send_plan` (
  `bill_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '物资月度计划id',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `work_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '流程id',
  `bill_no` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '计划编号',
  `plan_date` datetime DEFAULT NULL COMMENT '计划日期',
  `year` int DEFAULT NULL COMMENT '计划年度',
  `month` int DEFAULT NULL COMMENT '计划月份',
  `year_plan_bill_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '物资年度计划id',
  `year_plan_bill_no` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '物资年度计划编号',
  `amount` decimal(18,4) DEFAULT NULL COMMENT '计划金额',
  `tax_rate` decimal(18,4) DEFAULT NULL COMMENT '税率',
  `tax_amount` decimal(18,4) DEFAULT NULL COMMENT '税额',
  `total_amount` decimal(18,4) DEFAULT NULL COMMENT '税价合计',
  `state` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '状态',
  `preparer_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '填报人id',
  `preparer` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '填报人名称',
  `currency_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '币种id',
  `currency` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '币种名称',
  `org_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '单据机构id',
  `org_name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '单据机构名称',
  `remarks` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '备注信息',
  `base_cur_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '本位币id',
  `base_cur_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '本位币名称',
  `base_cur_rate` decimal(18,4) DEFAULT NULL COMMENT '本位币汇率',
  `base_cur_amount` decimal(18,4) DEFAULT NULL COMMENT '计划金额(本位币)',
  `base_cur_tax_amount` decimal(18,4) DEFAULT NULL COMMENT '税额(本位币)',
  `base_cur_total_amount` decimal(18,4) DEFAULT NULL COMMENT '税价合计(本位币)c',
  `rmb_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '人民币id',
  `rmb_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '人民币名称',
  `rmb_rate` decimal(18,4) DEFAULT NULL COMMENT '人民币汇率',
  `rmb_amount` decimal(18,4) DEFAULT NULL COMMENT '计划金额(人民币)',
  `rmb_tax_amount` decimal(18,4) DEFAULT NULL COMMENT '税额(人民币)',
  `rmb_total_amount` decimal(18,4) DEFAULT NULL COMMENT '税价合计(人民币)',
  `nullify_reason` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '作废原因',
  `nullify_description` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '作废描述',
  `nullify_creator_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '作废人id',
  `nullify_creator` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '作废人',
  `nullify_created` datetime DEFAULT NULL COMMENT '作废时间',
  `last_change_bill_state` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '最新变更单状态',
  `business_type` int DEFAULT NULL COMMENT '业务类型',
  `source_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '源单id',
  `source_number` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '源单编号',
  `supplier_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '供应商id',
  `supplier_name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '供应商名称',
  `credit_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '统一社会信用代码',
  `source_name` varchar(255) DEFAULT NULL COMMENT '合同名称',
  PRIMARY KEY (`bill_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='pcwp2物资月度发送计划';

-- ----------------------------
-- Records of month_send_plan
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for month_send_plan_dtl
-- ----------------------------
DROP TABLE IF EXISTS `month_send_plan_dtl`;
CREATE TABLE `month_send_plan_dtl` (
  `dtl_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '物资月度计划明细id',
  `bill_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '物资月度计划id',
  `year_plan_dtl_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '物资年度计划明细id',
  `material_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '物资id',
  `material_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '物资名称',
  `material_class_id` char(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '物资类别id(1级类别id/2级类别id/..)',
  `material_class_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '物资类别名称(1级类别名称/2级类别名称/..)',
  `spec` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '规格型号',
  `texture` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '材质',
  `unit` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '计量单位',
  `quantity` decimal(18,3) DEFAULT NULL COMMENT '数量',
  `price` decimal(18,4) DEFAULT NULL COMMENT '单价(元)',
  `amount` decimal(18,4) DEFAULT NULL COMMENT '金额(元)',
  `purchase_type` int DEFAULT NULL COMMENT '采购方式',
  `purchasing_unit_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '采购单位id',
  `purchasing_unit_name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '采购单位名称',
  `purchased_quantity` decimal(18,3) DEFAULT NULL COMMENT '已采购数量',
  `used_quantity_tender` decimal(18,4) DEFAULT NULL COMMENT '已使用数量(招标)',
  `used_quantity_contract` decimal(18,4) DEFAULT NULL COMMENT '已使用数量(合同)',
  PRIMARY KEY (`dtl_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='pcwp2物资月度发货计划明细';

-- ----------------------------
-- Records of month_send_plan_dtl
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for negotiated_price
-- ----------------------------
DROP TABLE IF EXISTS `negotiated_price`;
CREATE TABLE `negotiated_price` (
  `negotiated_price_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '报价id',
  `enquiry_amount` decimal(18,2) NOT NULL COMMENT '报价金额',
  `relevance_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '关联id',
  `type` tinyint NOT NULL COMMENT '报价类型（0求购2求租3招标）',
  `illustrate` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '说明',
  `state` tinyint DEFAULT NULL COMMENT '状态',
  `sort` int DEFAULT NULL COMMENT '排序',
  `is_delete` tinyint unsigned DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  PRIMARY KEY (`negotiated_price_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='询价';

-- ----------------------------
-- Records of negotiated_price
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for order_contact
-- ----------------------------
DROP TABLE IF EXISTS `order_contact`;
CREATE TABLE `order_contact` (
  `order_contact_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '订单合同id',
  `order_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '订单id',
  `order_sn` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '订单编号',
  `order_item_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '订单项id',
  `contact_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '合同id',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间	',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间	',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `sort` int DEFAULT NULL COMMENT '排序',
  `state` tinyint DEFAULT NULL COMMENT '状态',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：（ 0物资商场  1设备商城 ）',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除：（ -1: 删除  0:未删除）',
  PRIMARY KEY (`order_contact_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='订单合同关联信息';

-- ----------------------------
-- Records of order_contact
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for order_item
-- ----------------------------
DROP TABLE IF EXISTS `order_item`;
CREATE TABLE `order_item` (
  `order_item_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '订单项id',
  `order_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '订单id',
  `order_sn` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '订单号',
  `product_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '商品id',
  `product_sn` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '商品编号',
  `product_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '商品名称',
  `product_img` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '商品图片',
  `sku_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'skuid',
  `sku_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'sku名称',
  `cost_price` decimal(18,2) DEFAULT NULL COMMENT '商品成本价',
  `product_price` decimal(18,2) DEFAULT NULL COMMENT '商品价格',
  `buy_counts` decimal(18,3) DEFAULT NULL COMMENT '购买数量',
  `total_amount` decimal(18,2) DEFAULT NULL COMMENT '商品小计金额',
  `is_comment` tinyint NOT NULL DEFAULT '0' COMMENT '评论状态： 0 未评价  1 已评价',
  `buy_time` datetime DEFAULT NULL COMMENT '购买时间',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `product_type` tinyint DEFAULT NULL COMMENT '商品类型：0物资 1设备 2周材（设备租赁） 3周材（设备） 4二手设备 5租赁设备',
  `sort` int DEFAULT NULL COMMENT '排序',
  `state` tinyint DEFAULT NULL COMMENT '状态',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  `version` int NOT NULL DEFAULT '1' COMMENT '乐观锁',
  `bill_id` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '关联计划id（逗号分隔）',
  `dtl_id` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '关联计划明细id（逗号分割）',
  `relevance_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '关联名称（用于关联外部的名称唯一不修改）',
  `lease_num` decimal(18,3) NOT NULL DEFAULT '1.000' COMMENT '租赁时长',
  `lease_unit` char(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '租赁单位（天月年）',
  `return_state` tinyint DEFAULT NULL COMMENT '退货状态 0:申请退货，1:审核中 2:审核成功 3:审核失败 4:退货完成',
  `invoice_state` tinyint(3) unsigned zerofill DEFAULT NULL COMMENT '是否开票（0：未申请，2.已申请）',
  `profit_price` decimal(10,2) DEFAULT NULL COMMENT '利润',
  `ship_counts` decimal(18,0) DEFAULT NULL COMMENT '发货数量',
  `unit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '计量单位',
  `class_path_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '分类路径名称（xxx/xxx/xx）',
  `pcwp_lease_num` decimal(18,4) DEFAULT NULL COMMENT 'pcwp租赁时长',
  `pcwp_contract_price` decimal(18,2) DEFAULT NULL COMMENT 'pcwp合同单价',
  `pcwp_contract_amount` decimal(18,2) DEFAULT NULL COMMENT 'pcwp合同金额',
  `pcwp_lease_unit` tinyint DEFAULT NULL COMMENT 'pcwp租赁单位（0台班1小时2月3天4立方5吨6平方7车8公里）',
  `return_count` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '退货数量',
  PRIMARY KEY (`order_item_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='订单项';

-- ----------------------------
-- Records of order_item
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for order_payment_info
-- ----------------------------
DROP TABLE IF EXISTS `order_payment_info`;
CREATE TABLE `order_payment_info` (
  `payment_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '支付号',
  `order_sn` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '订单号（对外业务号）',
  `order_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '订单id',
  `alipay_trade_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '交易流水号',
  `total_amount` decimal(18,2) DEFAULT NULL COMMENT '支付总金额',
  `subject` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '交易内容',
  `payment_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '支付状态',
  `callback_content` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '回调内容',
  `callback_time` datetime DEFAULT NULL COMMENT '回调时间',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '备注',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
  `product_type` tinyint DEFAULT NULL COMMENT '商品类型：0物资 1设备 2周材（物资） 3周材（设备）',
  `sort` int DEFAULT NULL COMMENT '排序',
  `state` tinyint DEFAULT NULL COMMENT '状态',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  PRIMARY KEY (`payment_id`) USING BTREE,
  UNIQUE KEY `order_sn` (`order_sn`) USING BTREE,
  UNIQUE KEY `alipay_trade_no` (`alipay_trade_no`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='支付信息表';

-- ----------------------------
-- Records of order_payment_info
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for order_return
-- ----------------------------
DROP TABLE IF EXISTS `order_return`;
CREATE TABLE `order_return` (
  `order_return_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '退货id',
  `order_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '订单id',
  `order_sn` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '订单编号',
  `order_item_id` varchar(233) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '订单项',
  `order_return_no` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '退货编号',
  `product_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '商品名称',
  `brand_name` varchar(233) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '品牌名称',
  `brand_id` varchar(244) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '品牌id',
  `remarks` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '问题描述',
  `submit_reason` varchar(23) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '退货原因（1 ：7天无理由退货 2不想要了 3.物流太慢）',
  `user_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '退货人id',
  `count` int DEFAULT NULL COMMENT '退货数量',
  `return_method` int DEFAULT NULL COMMENT '退货方式（1上门取件 2快递至卖家）',
  `state` tinyint DEFAULT NULL COMMENT '退货状态 1:审核中 2:审核成功 3:审核失败 4:退货完成',
  `delivery_flow_id` varchar(244) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '物流单号',
  `delivery_type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '配送方式1(上门取件 2快递至卖家）',
  `flish_time` datetime DEFAULT NULL COMMENT '退货成功时间',
  `receiver_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '收货人姓名',
  `receiver_mobile` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '收货人电话',
  `receiver_address` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '收货人地址',
  `address` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '详细地址',
  `gmt_create` datetime DEFAULT NULL COMMENT '退货时间',
  `founder_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人姓名',
  `founder_id` bigint DEFAULT NULL COMMENT '创建人id',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  `is_delete` tinyint DEFAULT NULL COMMENT '是否删除',
  `sort` int DEFAULT NULL COMMENT '排序值',
  `total_amount` decimal(10,0) DEFAULT NULL COMMENT '退款金额',
  `fail_reason` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '失败原因',
  `send_address` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '发货人地址',
  `send_mobile` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '发货人电话',
  `send_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '发货人姓名',
  `logistics_company` varchar(255) DEFAULT NULL COMMENT '物流公司',
  `no_rate_amount` decimal(10,0) DEFAULT NULL COMMENT '退款金额',
  `shop_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '点铺id',
  `enterprise_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '退货企业名称',
  `enterprise_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '退货企业id',
  PRIMARY KEY (`order_return_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='退货表';

-- ----------------------------
-- Records of order_return
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for order_return_item
-- ----------------------------
DROP TABLE IF EXISTS `order_return_item`;
CREATE TABLE `order_return_item` (
  `order_return_item_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '退货项id',
  `order_return_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '退货id',
  `order_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '订单id',
  `order_sn` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '订单号',
  `other_order_sn` char(255) DEFAULT NULL COMMENT '二级订单号',
  `order_return_no` varchar(255) DEFAULT NULL COMMENT '退货单编号',
  `other_order_item_id` char(255) DEFAULT NULL COMMENT '二级订单项Id',
  `order_item_id` char(36) DEFAULT NULL COMMENT '订单项Id',
  `product_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '商品id',
  `product_sn` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '商品编号',
  `product_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '商品名称',
  `product_img` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '商品图片',
  `sku_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'skuid',
  `sku_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'sku名称',
  `cost_price` decimal(18,2) DEFAULT NULL COMMENT '商品成本价',
  `product_price` decimal(18,2) DEFAULT NULL COMMENT '商品价格',
  `count` decimal(18,3) NOT NULL DEFAULT '0.000' COMMENT '退货数量',
  `flish_time` datetime DEFAULT NULL COMMENT '退货成功时间',
  `is_delete` tinyint DEFAULT '0' COMMENT '逻辑删除 -1: 删除 0:未删除',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `version` int NOT NULL DEFAULT '1' COMMENT '乐观锁',
  `no_rate_price` decimal(10,2) DEFAULT NULL COMMENT '不含税单价',
  `no_rate_amount` decimal(18,2) DEFAULT NULL COMMENT '不含税总金额',
  `rate_amount` decimal(18,2) DEFAULT NULL COMMENT '税总金额',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  `brand_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '品牌id',
  `brand_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '品牌名称',
  `other_order_id` char(36) DEFAULT NULL COMMENT '二级订单Id',
  `total_amount` decimal(18,2) DEFAULT NULL COMMENT '总金额',
  `other_product_price` decimal(18,2) DEFAULT NULL COMMENT '二级订单单价',
  `other_total_amount` decimal(18,2) DEFAULT NULL COMMENT '二级订单总金额',
  `sort` int DEFAULT NULL COMMENT '排序',
  `buy_counts` decimal(18,3) NOT NULL COMMENT '购买数量',
  `is_out` tinyint DEFAULT NULL COMMENT '退货类型',
  `other_no_rate_amount` decimal(18,2) DEFAULT NULL COMMENT '二级不含税金额',
  `other_rate_amount` decimal(18,2) DEFAULT NULL COMMENT '二级含税金额',
  `is_reconciliation` tinyint NOT NULL DEFAULT '0' COMMENT '是否对账',
  PRIMARY KEY (`order_return_item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='退货项表';

-- ----------------------------
-- Records of order_return_item
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for order_select_contact
-- ----------------------------
DROP TABLE IF EXISTS `order_select_contact`;
CREATE TABLE `order_select_contact` (
  `order_select_contact_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '订单选择合同id',
  `order_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '订单id',
  `order_sn` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '订单编号',
  `order_item_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '订单项id',
  `dtl_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '合同设备明细id',
  `item_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '设备名称',
  `count` decimal(10,4) NOT NULL COMMENT '已选数量',
  `bill_no` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '合同编号',
  `bill_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '合同id',
  `size` varchar(255) DEFAULT NULL COMMENT '合同设备型号',
  `bill_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '合同名称',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间	',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间	',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `sort` int DEFAULT NULL COMMENT '排序',
  `state` tinyint DEFAULT NULL COMMENT '状态',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：（ 0物资商场  1设备商城 ）',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除：（ -1: 删除  0:未删除）',
  `product_type` tinyint DEFAULT NULL COMMENT '商品类型：0物资 1设备 2周材（物资） 3周材（设备） 4二手设备 5租赁设备 6维修服务 7金融服务 8保险服务',
  `org_id` char(36) DEFAULT NULL COMMENT '机构id',
  `pcwp` tinyint NOT NULL DEFAULT '2' COMMENT '1 pcwp2  2 :pcwp1',
  PRIMARY KEY (`order_select_contact_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='订单关联合同';

-- ----------------------------
-- Records of order_select_contact
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for order_select_plan
-- ----------------------------
DROP TABLE IF EXISTS `order_select_plan`;
CREATE TABLE `order_select_plan` (
  `order_select_plan_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '订单已选择计划id',
  `order_sn` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '订单编号',
  `order_item_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '订单项id',
  `dtl_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '计划明细id',
  `equipment_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '设备名称',
  `count` decimal(10,3) DEFAULT NULL COMMENT '已选数量',
  `bill_no` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '计划编号',
  `bill_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '计划id',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间	',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间	',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `sort` int DEFAULT NULL COMMENT '排序',
  `state` tinyint DEFAULT NULL COMMENT '状态',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：（ 0物资商场  1设备商城 ）',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除：（ -1: 删除  0:未删除）',
  `product_type` tinyint DEFAULT NULL COMMENT '商品类型：0物资 1设备 2周材（物资） 3周材（设备） 4二手设备 5租赁设备 6维修服务 7金融服务 8保险服务',
  `org_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '机构id(采购员的orgId)',
  `price` decimal(18,2) DEFAULT NULL COMMENT '计划的单价',
  `account` decimal(18,2) DEFAULT NULL COMMENT '计划的总金额',
  `storage_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '供应商id',
  `storage_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '供应商名称',
  `short_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '机构简码',
  `credit_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '信用代码',
  `supplier_type` tinyint DEFAULT NULL COMMENT '供应闪类型（1外部供应闪2内部供应商）',
  `storage_org_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '供应商的机构id',
  PRIMARY KEY (`order_select_plan_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='选择计划清单';

-- ----------------------------
-- Records of order_select_plan
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for order_ship
-- ----------------------------
DROP TABLE IF EXISTS `order_ship`;
CREATE TABLE `order_ship` (
  `bill_Id` varchar(233) NOT NULL COMMENT '单据id',
  `order_id` varchar(233) NOT NULL COMMENT '订单id',
  `bill_sn` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '单据编号',
  `ship_data` datetime DEFAULT NULL COMMENT '发货时间',
  `ship_user_id` varchar(33) NOT NULL COMMENT '发货人id',
  `suplier_id` varchar(44) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '供应商id',
  `supplier_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '供应商姓名',
  `receive_id` varchar(44) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '收料人员Id',
  `receive_name` varchar(255) DEFAULT NULL COMMENT '收料员名称',
  `receive_org_id` varchar(44) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '收料人员单位id',
  `receive_org_name` varchar(255) DEFAULT NULL COMMENT '收料单位名称',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `totol_price` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '总价格',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '发货单状态（0未发货。1发货中，2已收货，）',
  `ship_address` varchar(255) DEFAULT NULL COMMENT '发货地址',
  `org_name` varchar(255) DEFAULT NULL COMMENT '机构名称',
  `delivery_flow_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '物流单号',
  `logistics_company` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '物流公司',
  `shop_id` varchar(233) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '店铺',
  `order_sn` varchar(255) DEFAULT NULL COMMENT '订单编号',
  `shop_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '店铺名称',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `founder_name` varchar(255) DEFAULT NULL COMMENT '当前用户',
  `founder_id` varchar(222) DEFAULT NULL COMMENT '操作人id',
  `is_delete` int DEFAULT NULL COMMENT '是否删除',
  `suplier_org_Id` int DEFAULT NULL COMMENT '供应商组织机构Id',
  `sort` int DEFAULT NULL COMMENT '排序',
  `remarks` varchar(255) DEFAULT NULL COMMENT '备注',
  `org_id` varchar(233) DEFAULT NULL COMMENT '收料员组织机构Id',
  `flish_time` datetime DEFAULT NULL COMMENT '下单时间',
  `suplier_code` varchar(255) DEFAULT NULL COMMENT '供应商社会编码',
  `confirm_time` datetime DEFAULT NULL COMMENT '收料员确认收料时间',
  `plan_name` varchar(255) DEFAULT NULL COMMENT '计划名称',
  `plan_number` varchar(233) DEFAULT NULL COMMENT '计划编号',
  PRIMARY KEY (`bill_Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='发货单表';

-- ----------------------------
-- Records of order_ship
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for order_ship_dtl
-- ----------------------------
DROP TABLE IF EXISTS `order_ship_dtl`;
CREATE TABLE `order_ship_dtl` (
  `dtl_id` varchar(233) NOT NULL,
  `order_Id` varchar(233) DEFAULT NULL COMMENT '订单id',
  `order_item_id` varchar(233) DEFAULT NULL COMMENT '订单项id',
  `order_sn` varchar(255) DEFAULT NULL COMMENT '订单编号',
  `product_name` varchar(255) DEFAULT NULL COMMENT '商品名称',
  `product_sn` varchar(255) DEFAULT NULL COMMENT '商品编号',
  `product_price` decimal(10,2) DEFAULT NULL COMMENT '商品价格',
  `send_time` datetime DEFAULT NULL COMMENT '发货时间',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `price` decimal(10,2) DEFAULT NULL COMMENT '单价',
  `ship_num` int DEFAULT NULL COMMENT '已收货数量',
  `total_price` decimal(10,2) DEFAULT NULL COMMENT '总价格',
  `total_sum` varchar(255) DEFAULT NULL COMMENT '总数量',
  `bill_id` varchar(233) DEFAULT NULL COMMENT '发货单编号',
  `product_category_id` varchar(233) DEFAULT NULL COMMENT '商品分类id',
  `product_category_name` varchar(255) DEFAULT NULL COMMENT '商品跟类名称',
  `ship_counts` decimal(16,0) DEFAULT NULL COMMENT '发货数量',
  `bill_sn` varchar(255) DEFAULT NULL COMMENT '发货单编号',
  `is_delete` int DEFAULT NULL COMMENT '是否删除',
  `founder_id` varchar(222) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '操作人id',
  `founder_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '当前用户',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `sort` int DEFAULT NULL COMMENT '排序',
  `remarks` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '备注',
  `sku_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'sku名称',
  `receive_time` datetime DEFAULT NULL COMMENT '收货时间',
  `unit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '计量单位',
  PRIMARY KEY (`dtl_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='发货单项表';

-- ----------------------------
-- Records of order_ship_dtl
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for orders
-- ----------------------------
DROP TABLE IF EXISTS `orders`;
CREATE TABLE `orders` (
  `order_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '订单id',
  `order_sn` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '订单号',
  `shop_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '店铺名称',
  `shop_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '店铺id',
  `user_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '用户id',
  `untitled` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '产品名称（多个产品用,隔开）',
  `receiver_name` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '收货人快照',
  `receiver_mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '收货人手机号快照',
  `receiver_address` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '收货地址快照',
  `total_amount` decimal(18,2) DEFAULT NULL COMMENT '订单总价格',
  `actual_amount` decimal(18,2) DEFAULT NULL COMMENT '实际支付总价格',
  `deal_type` tinyint unsigned DEFAULT NULL COMMENT '交易类型，店铺对用户：1外->外 2内-外 3外-内 4内-内',
  `pay_type` tinyint unsigned DEFAULT NULL COMMENT '线上支付方式 1:微信 2:支付宝 3:银联 ',
  `pay_way` tinyint DEFAULT NULL COMMENT '支付方式 1线上支付 2内部结算3线下转账',
  `order_remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '订单备注',
  `state` tinyint unsigned DEFAULT NULL COMMENT '订单状态：0草稿 1已提交 2待确认 3已确认 4待签订合同 5已签合同 6待发货 7已关闭 8 已发货 9待收货 10 已完成',
  `order_bill_state` tinyint DEFAULT NULL COMMENT '是否开发票 0初始 1已申请 2已开票',
  `invoice_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '发票类型（1.电子发票，2 普通发票）',
  `delivery_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '配送方式（0:普通物流）',
  `delivery_flow_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '物流单号',
  `logistics_company` varchar(255) DEFAULT NULL COMMENT '物流公司',
  `order_freight` decimal(18,2) DEFAULT NULL COMMENT '订单运费 默认可以为零，代表包邮',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
  `pay_time` datetime DEFAULT NULL COMMENT '付款时间',
  `delivery_time` datetime DEFAULT NULL COMMENT '发货时间',
  `flish_time` datetime DEFAULT NULL COMMENT '确认时间',
  `cancel_time` datetime DEFAULT NULL COMMENT '取消时间',
  `close_type` tinyint unsigned DEFAULT NULL COMMENT '订单关闭类型1-超时未支付 2-退款关闭 4-买家取消 15-已通过货到付款交易',
  `trade_no` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '支付交易号',
  `product_type` tinyint unsigned DEFAULT NULL COMMENT '商品类型：0物资 1设备 2周材（设备租赁） 3周材（设备） 4二手设备 5租赁设备',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `remarks` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `sort` int DEFAULT NULL COMMENT '排序',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  `version` int NOT NULL DEFAULT '1' COMMENT '乐观锁',
  `cost_price_total` decimal(20,0) DEFAULT NULL COMMENT '总成本价',
  `profit_price_total` decimal(30,0) DEFAULT NULL COMMENT ' 总利润',
  `success_date` datetime DEFAULT NULL COMMENT '完成时间',
  `org_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '企业id',
  `enterprise_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '本地机构id',
  `pcwp_contract_amount` decimal(18,2) DEFAULT NULL COMMENT 'pcwp合同金额',
  PRIMARY KEY (`order_id`) USING BTREE,
  UNIQUE KEY `uniq_order_sn` (`order_sn`) USING BTREE COMMENT '订单号唯一索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='商品订单';

-- ----------------------------
-- Records of orders
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for page_info
-- ----------------------------
DROP TABLE IF EXISTS `page_info`;
CREATE TABLE `page_info` (
  `page_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '页面id',
  `page_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '页面名称',
  `product_category_ids` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '产品分类ids',
  `product_category_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '商品分类楼层名称',
  `enterprise_ids` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '企业ids',
  `enterprise_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '企业楼层名称',
  `floor_ids` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '楼层ids',
  `floor_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '商品楼层名称',
  `
gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
  `sort` int DEFAULT NULL COMMENT '排序',
  `state` tinyint DEFAULT NULL COMMENT '状态',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  PRIMARY KEY (`page_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Records of page_info
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for pay_invoice
-- ----------------------------
DROP TABLE IF EXISTS `pay_invoice`;
CREATE TABLE `pay_invoice` (
  `invoice_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '发票id',
  `user_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '用户id',
  `shop_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '店铺id',
  `shop_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '店铺名称',
  `invoice_state` tinyint DEFAULT NULL COMMENT '0:已申请 1:已开票',
  `invoice_type` tinyint DEFAULT NULL COMMENT '0：增值税发票 1:普通发票 2: 电子专用发票',
  `rise_type` tinyint DEFAULT NULL COMMENT '（抬头类型）0:个人 1:单位',
  `invoice_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '发票抬头',
  `user_name` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '收票人姓名',
  `user_address` varchar(125) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '收票人地址',
  `user_phone` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '收票人联系电话',
  `email` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '收票人邮箱',
  `company` varchar(125) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '单位名称',
  `duty_paragraph` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '发票税号(对应企业的税号taxpayerNo)',
  `register_address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '注册地址',
  `register_phone` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '注册电话',
  `bank` varchar(125) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '开户银行',
  `bank_account` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '银行账号',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '修改时间',
  `gmt_apply` datetime DEFAULT NULL COMMENT '通过时间',
  `gmt_adopt` datetime DEFAULT NULL COMMENT '申请时间',
  `Invoice_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '发票单号',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `sort` int DEFAULT NULL COMMENT '排序',
  `state` tinyint DEFAULT NULL COMMENT '状态',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城  ',
  `detail_addr` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '详细地址',
  `amount` decimal(10,0) DEFAULT NULL COMMENT '发票金额',
  `service_content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '存储缴费的发票服务内容(集采、租赁、物流)',
  `pay_item_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '缴费记录明细ID',
  `type` int DEFAULT '0' COMMENT '入驻0 续费1',
  `version` int NOT NULL DEFAULT '1' COMMENT '乐观锁',
  PRIMARY KEY (`invoice_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Records of pay_invoice
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for private_key_supplier
-- ----------------------------
DROP TABLE IF EXISTS `private_key_supplier`;
CREATE TABLE `private_key_supplier` (
  `private_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '私有id',
  `supplier_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '供应商id',
  `supplier_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '供应商名称',
  `supplier_type` tinyint DEFAULT NULL COMMENT '供应商类型',
  `private_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '秘钥key',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`private_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='供应商秘钥';

-- ----------------------------
-- Records of private_key_supplier
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for procurement_info
-- ----------------------------
DROP TABLE IF EXISTS `procurement_info`;
CREATE TABLE `procurement_info` (
  `bill_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'id',
  `contract_number` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `contract_name` char(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `list_type` tinyint DEFAULT NULL COMMENT '清单类型：1 计划 2应急抢险 3基础库 4 招标',
  `server_unit_amount` decimal(18,4) DEFAULT NULL COMMENT '服务单位费用',
  `transport_tax_rate` decimal(18,4) DEFAULT NULL COMMENT '服务费税率',
  `tax_excluded_amount` decimal(18,4) DEFAULT NULL COMMENT '不含税金额',
  `tax_rate` decimal(18,2) DEFAULT NULL COMMENT '税率',
  `tax_amount` decimal(18,2) DEFAULT NULL COMMENT '税额',
  `contract_amount` decimal(18,4) DEFAULT NULL COMMENT '合同金额',
  `bond_state` bit(1) DEFAULT NULL COMMENT '是否需要缴纳履约保证金: 1是 0 否',
  `bond_amount` decimal(18,2) DEFAULT NULL COMMENT '应交履约保证金',
  `currency_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '币种id',
  `currency_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '币种名称',
  `advance_charge` decimal(18,2) DEFAULT NULL COMMENT '预付款',
  `write_off_advance_charge` decimal(18,2) NOT NULL COMMENT '已冲销预付款',
  `signing_date` date DEFAULT NULL COMMENT '签订日期',
  `signing_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '签约人id',
  `signing_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '签约人',
  `signing_start_time` date DEFAULT NULL COMMENT '签约有效期开始时间',
  `signing_end_time` date DEFAULT NULL COMMENT '签约有效期结束时间',
  `address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '地址',
  `contract_state` tinyint DEFAULT NULL COMMENT '合同状态',
  `financial_sharing` tinyint NOT NULL DEFAULT '0' COMMENT '传输财务共享状态',
  `contract_context` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '主要合同内容',
  `period` tinyint unsigned DEFAULT NULL COMMENT '变更次数',
  `state` tinyint DEFAULT NULL COMMENT '申请状态',
  `work_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '流程Id',
  `gmt_create` datetime NOT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人名称',
  `org_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '单据机构id',
  `org_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '单据机构名称',
  `nullify_reason` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '作废原因',
  `nullify_description` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '作废描述',
  `nullify_creator_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '作废人id',
  `nullify_creator` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '作废人',
  `nullify_created` datetime DEFAULT NULL COMMENT '作废时间',
  `base_cur_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '本位币id',
  `base_cur_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '本位币名称',
  `base_cur_rate` decimal(18,4) DEFAULT NULL COMMENT '本位币汇率',
  `rmb_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '人民币名称',
  `rmb_rate` decimal(18,4) DEFAULT NULL COMMENT '人民币汇率',
  `settlement_amount` decimal(18,4) NOT NULL COMMENT '结算金额',
  `contract_state_before_settlement` tinyint DEFAULT NULL COMMENT '终期结算前合同状态(用于终期结算单据作废或撤销后还原状态)',
  `is_move` bit(1) NOT NULL DEFAULT b'0' COMMENT '迁移状态',
  `rmb_amount` decimal(18,4) DEFAULT NULL COMMENT '人名币金额',
  `base_cur_amount` decimal(18,4) DEFAULT NULL COMMENT '本位币金额',
  PRIMARY KEY (`bill_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='pcwp2物资采购合同基本信息';

-- ----------------------------
-- Records of procurement_info
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for procurement_info_dtl
-- ----------------------------
DROP TABLE IF EXISTS `procurement_info_dtl`;
CREATE TABLE `procurement_info_dtl` (
  `bill_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `source_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '源单id',
  `ori_bill_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '引入的清单id',
  `one_level_type_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '一级类别id',
  `one_level_type_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '一级类别名称',
  `plan_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '计划id',
  `plan_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '计划名称',
  `plan_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '计划编号',
  `plan_type` tinyint DEFAULT NULL COMMENT '计划类型',
  `type_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '物资类别id',
  `type_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '物资类别名称',
  `material_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '物资类别id',
  `material_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `specification_model` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `texture_of_material` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `unit` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '计量单位',
  `quantity` decimal(18,4) DEFAULT NULL COMMENT '数量',
  `ori_quantity` decimal(18,4) DEFAULT NULL COMMENT '原数量',
  `unit_price` decimal(18,4) DEFAULT NULL COMMENT '单价',
  `ori_unit_price` decimal(18,4) DEFAULT NULL COMMENT '原单价',
  `amount` decimal(18,4) DEFAULT NULL COMMENT '金额',
  `settled_quantity` decimal(18,4) DEFAULT NULL COMMENT '已收料数量',
  `warehousing_quantity` decimal(18,4) DEFAULT NULL COMMENT '已入库数量',
  `acceptance_quantity` decimal(18,4) DEFAULT NULL COMMENT '已验收数量',
  `is_move` bit(1) NOT NULL DEFAULT b'0' COMMENT '迁移状态',
  PRIMARY KEY (`bill_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='pcwp2物资采购合同清单(物资)';

-- ----------------------------
-- Records of procurement_info_dtl
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for product
-- ----------------------------
DROP TABLE IF EXISTS `product`;
CREATE TABLE `product` (
  `product_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '商品id',
  `serial_num` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '商品编码',
  `product_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '商品名称',
  `product_intro` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '商品简介',
  `shop_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '店铺id',
  `class_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '分类id',
  `product_describe` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '商品描述',
  `state` tinyint unsigned NOT NULL COMMENT '商品状态（0待上架 1已上架 2已下架）',
  `product_type` tinyint unsigned NOT NULL COMMENT '商品类型：0物资 1设备 2周材（设备租赁） 3周材（设备） 4二手设备 5租赁设备 6维修服务 7金融服务 8保险服务',
  `product_keyword` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '商品关键字（,分隔）',
  `product_transport_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '商品运费类型（0商家包邮）',
  `product_min_price` decimal(18,2) DEFAULT NULL COMMENT '商品的最低价',
  `is_delete` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除 -1: 删除 0:未删除',
  `sort` int DEFAULT NULL COMMENT '平台排序',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `product_inventory_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '商品库id',
  `relevance_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '关联外部id',
  `brand_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '品牌id',
  `brand_name` varchar(255) DEFAULT NULL COMMENT '品牌名称冗余',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `mall_type` tinyint NOT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  `putaway_date` datetime DEFAULT NULL COMMENT '上架时间',
  `sold_num` decimal(18,3) DEFAULT NULL COMMENT '销量',
  `product_visit_num` int DEFAULT NULL COMMENT '商品访问量',
  `province` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '省',
  `city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '市',
  `county` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '县、区',
  `detailed_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '详细地址',
  `longitude` decimal(10,7) DEFAULT NULL COMMENT '经度',
  `latitude` decimal(10,7) DEFAULT NULL COMMENT '纬度',
  `shop_sort` int DEFAULT NULL COMMENT '店铺排序',
  `synthesis_sort` int DEFAULT NULL COMMENT '综合排序',
  `product_min_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '商品小图',
  `class_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '分类路径（/分割）',
  `relevance_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '关联名称（用于关联外部的名称唯一不修改）',
  `relevance_no` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '关联编号',
  `is_completion` tinyint DEFAULT NULL COMMENT '是否完成商品编辑（0否1是）',
  `leave_factory` datetime DEFAULT NULL COMMENT '出厂日期',
  `quality` tinyint unsigned DEFAULT NULL COMMENT '质量、成色',
  `is_week_materials` tinyint NOT NULL DEFAULT '0' COMMENT '是否周材',
  `fail_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '失败原因',
  `is_open_import` tinyint NOT NULL DEFAULT '0' COMMENT '是否外部导入商品',
  `supper_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '供应商冗余',
  `is_maintain` tinyint DEFAULT NULL COMMENT '是否维修',
  `sku_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '规格型号冗余',
  `factory_use_year` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '规定使用年限',
  PRIMARY KEY (`product_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='店铺商品信息';

-- ----------------------------
-- Records of product
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for product_attribute
-- ----------------------------
DROP TABLE IF EXISTS `product_attribute`;
CREATE TABLE `product_attribute` (
  `attribute_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '属性id',
  `attribute_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '属性名称',
  `attribute_value` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '可选值列表[用逗号分隔]',
  `attribute_type` tinyint unsigned DEFAULT NULL COMMENT '属性类型[0-销售属性，1-基本属性]',
  `sort` int DEFAULT NULL COMMENT '排序',
  `state` tinyint unsigned DEFAULT NULL COMMENT '启用状态[0 - 停用，1 - 启用]',
  `parent_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '父级id',
  `class_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '分类id',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `product_type` tinyint DEFAULT NULL COMMENT '商品类型：0物资 1设备 2周材（物资） 3周材（设备） 4二手设备 5租赁设备',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  PRIMARY KEY (`attribute_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='商品属性';

-- ----------------------------
-- Records of product_attribute
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for product_attribute_value
-- ----------------------------
DROP TABLE IF EXISTS `product_attribute_value`;
CREATE TABLE `product_attribute_value` (
  `sku_attribute_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '商品属性id',
  `product_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '商品id',
  `attribute_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '属性id',
  `attribute_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '属性名',
  `attribute_value` varchar(1500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '属性值',
  `sort` int DEFAULT NULL COMMENT '排序',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `product_type` tinyint DEFAULT NULL COMMENT '商品类型：0物资 1设备 2周材（物资） 3周材（设备） 4二手设备 5租赁设备',
  `state` tinyint DEFAULT NULL COMMENT '状态',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  PRIMARY KEY (`sku_attribute_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='商品属性值';

-- ----------------------------
-- Records of product_attribute_value
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for product_category
-- ----------------------------
DROP TABLE IF EXISTS `product_category`;
CREATE TABLE `product_category` (
  `class_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '分类id',
  `class_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '分类名称',
  `class_keyword` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '分类关键字',
  `class_level` tinyint unsigned DEFAULT NULL COMMENT '分类层级 1:一级大分类 2:二级分类 3:三级小分类',
  `parent_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '父层级id',
  `class_icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '图标 logo',
  `class_bg_color` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '背景颜色',
  `sort` int DEFAULT NULL COMMENT '排序',
  `class_describe` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '分类的描述',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `state` tinyint DEFAULT NULL COMMENT '状态（1启用0停用）',
  `product_type` tinyint DEFAULT NULL COMMENT '商品类型：0物资 1设备 2周材（物资） 3周材（设备） 4二手设备 5租赁设备 6维修服务',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  `is_exhibition` tinyint DEFAULT NULL COMMENT '是否展示(1是，0否)',
  `is_have_product` tinyint DEFAULT NULL COMMENT '是否有商品(1有，0没有)',
  PRIMARY KEY (`class_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='商品分类';

-- ----------------------------
-- Records of product_category
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for product_collect
-- ----------------------------
DROP TABLE IF EXISTS `product_collect`;
CREATE TABLE `product_collect` (
  `collect_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '收藏id',
  `user_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '用户id',
  `product_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '关注id（商品id,店铺id）',
  `collect_type` tinyint DEFAULT NULL COMMENT '关注类型 ( 1:商品  2：店铺  3:需求)',
  `product_type` tinyint unsigned DEFAULT NULL COMMENT '商品类型：0物资 1设备 2周材（物资） 3周材（设备） 4二手设备 5租赁设备',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间	',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间	',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `sort` int DEFAULT NULL COMMENT '排序',
  `state` tinyint DEFAULT NULL COMMENT '状态（1：启用 0： 停用）',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：（ 0物资商场  1设备商城 ）',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除：（ -1: 删除  0:未删除）',
  PRIMARY KEY (`collect_id`) USING BTREE,
  KEY `product_id` (`product_id`) USING BTREE,
  KEY `user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='个人商品收藏';

-- ----------------------------
-- Records of product_collect
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for product_comment
-- ----------------------------
DROP TABLE IF EXISTS `product_comment`;
CREATE TABLE `product_comment` (
  `comment_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '评论id',
  `product_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '商品id',
  `user_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '评论用户id',
  `parent_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '父评论id',
  `order_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '订单id',
  `order_item_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '订单项id',
  `product_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '商品名称',
  `is_anonymous` tinyint unsigned DEFAULT NULL COMMENT '是否匿名（1:是，0:否）',
  `comment_type` tinyint unsigned DEFAULT NULL COMMENT '评价类型（1好评，2中评，3差评）',
  `comment_level` tinyint unsigned DEFAULT NULL COMMENT '评价分数',
  `comment_content` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '评价内容',
  `comment_imgs` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '评价晒图(JSON {img1:url1,img2:url2}  )',
  `evaluate_time` datetime DEFAULT NULL COMMENT '评价时间',
  `is_reply` tinyint unsigned DEFAULT NULL COMMENT '是否回复（0:未回复，1:已回复）',
  `reply_content` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '回复内容',
  `reply_time` datetime DEFAULT NULL COMMENT '回复时间',
  `is_show` tinyint unsigned DEFAULT NULL COMMENT '是否显示（1:是，0:否）',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `product_type` tinyint DEFAULT NULL COMMENT '商品类型：0物资 1设备 2周材（物资） 3周材（设备） 4二手设备 5租赁设备',
  `sort` int DEFAULT NULL COMMENT '排序',
  `state` tinyint DEFAULT NULL COMMENT '状态',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  PRIMARY KEY (`comment_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='商品评价';

-- ----------------------------
-- Records of product_comment
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for product_inventory
-- ----------------------------
DROP TABLE IF EXISTS `product_inventory`;
CREATE TABLE `product_inventory` (
  `product_inventory_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '商品库存id',
  `product_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '商品名称',
  `class_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '类别id',
  `class_name` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '类别名称',
  `class_name_path` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '类别名称路径',
  `product_describe` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '商品描述',
  `product_type` tinyint unsigned DEFAULT NULL COMMENT '商品类型：0物资 1设备 2周材（物资） 3周材（设备） 4二手设备 5租赁设备',
  `spec` char(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '规格',
  `unit` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '计量单位',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `relevance_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '关联外部id',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `sort` int DEFAULT NULL COMMENT '排序',
  `state` tinyint DEFAULT NULL COMMENT '状态（1启用0停用）',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  `relevance_no` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '关联外部编号',
  `product_title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '商品标题',
  `spec_title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '规格',
  PRIMARY KEY (`product_inventory_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='商品库';

-- ----------------------------
-- Records of product_inventory
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for product_sku
-- ----------------------------
DROP TABLE IF EXISTS `product_sku`;
CREATE TABLE `product_sku` (
  `sku_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'skuid',
  `product_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '商品id',
  `sku_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'sku名称',
  `sku_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'sku图片',
  `cost_price` decimal(18,2) DEFAULT NULL COMMENT '成本价',
  `original_price` decimal(18,2) DEFAULT NULL COMMENT '原价',
  `sell_price` decimal(18,2) DEFAULT NULL COMMENT '销售价格',
  `discounts` decimal(18,2) DEFAULT NULL COMMENT '折扣力度',
  `stock` decimal(18,3) DEFAULT NULL COMMENT '库存',
  `state` tinyint unsigned DEFAULT NULL COMMENT 'sku状态(1启用，0停用)',
  `unit` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'sku单位',
  `sold_num` decimal(18,3) DEFAULT NULL COMMENT '销量',
  `sku_title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'sku标题',
  `sku_subtitle` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'sku副标题',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
  `brand_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '品牌id',
  `sort` int DEFAULT NULL COMMENT '排序',
  `product_type` tinyint DEFAULT NULL COMMENT '商品类型：0物资 1设备 2周材（物资） 3周材（设备） 4二手设备 5租赁设备',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  `lease_unit` char(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '租赁单位（天月年）',
  `settle_price` decimal(18,2) DEFAULT NULL COMMENT '结算价',
  PRIMARY KEY (`sku_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='商品sku';

-- ----------------------------
-- Records of product_sku
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for role
-- ----------------------------
DROP TABLE IF EXISTS `role`;
CREATE TABLE `role` (
  `role_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类型id',
  `role_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '用于查询',
  `role_name` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '角色名称',
  `is_selective` tinyint unsigned DEFAULT NULL COMMENT '是否可选   1：可选 0：不可选',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
  `sort` int DEFAULT NULL COMMENT '排序',
  `state` tinyint DEFAULT NULL COMMENT '状态',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  PRIMARY KEY (`role_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='商城角色表';

-- ----------------------------
-- Records of role
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for shop
-- ----------------------------
DROP TABLE IF EXISTS `shop`;
CREATE TABLE `shop` (
  `shop_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '店铺id',
  `shop_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '店铺名称',
  `shop_balance` decimal(18,2) DEFAULT NULL COMMENT '店铺余额',
  `shop_freeze_money` decimal(18,2) DEFAULT NULL COMMENT '店铺冻结的资金',
  `shop_type` tinyint unsigned DEFAULT NULL COMMENT '店铺类型 0：个体户  1：企业  2：个人',
  `ad_Img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '广告图',
  `shop_img` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '店铺log',
  `province` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '省',
  `city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '市',
  `county` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '县、区',
  `detailed_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '详细地址',
  `longitude` decimal(10,7) DEFAULT NULL COMMENT '经度',
  `latitude` decimal(10,7) DEFAULT NULL COMMENT '纬度',
  `state` tinyint unsigned DEFAULT NULL COMMENT '店铺状态 1:启用 0停用',
  `shop_describle` varchar(2500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '店铺简介',
  `enterprise_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '企业id',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除： -1: 删除 0:未删除',
  `is_business` tinyint NOT NULL DEFAULT '0' COMMENT '是否自营：1：是  0：否（默认：0）',
  `is_supplier` tinyint DEFAULT NULL COMMENT '是否为供应商：1： 是  0：否',
  `is_internal_shop` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否内部店铺：1：是  0：否',
  `is_internal_settlement` tinyint NOT NULL DEFAULT '1' COMMENT '是否支持内部结算：1：是  0：否',
  `audit_status` tinyint unsigned DEFAULT NULL COMMENT '店铺审核状态： 1：审核通过  2：未审核  3：审核未通过 ',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `sort` int DEFAULT NULL COMMENT '排序',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  `main_business` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '主营业务',
  `open_date` datetime DEFAULT NULL COMMENT '开店时间（审核通过时间）',
  `serial_num` char(36) CHARACTER SET koi8u COLLATE koi8u_general_ci DEFAULT NULL COMMENT '序列号',
  `link_man` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '联系人',
  `initial` char(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '店铺名称首字母',
  `contact_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '联系电话',
  `synthesis_sort` int DEFAULT NULL COMMENT '综合排序值',
  `fail_reason` varchar(2500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '审核失败原因',
  `is_index_show` tinyint NOT NULL DEFAULT '0' COMMENT '是否首页显示',
  `is_other_auth` varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '其他服务权限（JSON）',
  `return_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '退货地址',
  `return_relation_name` varchar(255) DEFAULT NULL COMMENT '退货负责人',
  `return_relation_number` varchar(255) DEFAULT NULL COMMENT '退货联系电话',
  `auth_list` varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '权限列表字符串\n{\n isTrade: 0, // 集采贸易 1有0无\n isLease: 0, // 设备租赁 \nisRepair: 0, // 维修服务\nisFinance: 0, //金融服务\n isInsurance: 0 //保险服务\n}\n',
  `audit_pass_time` datetime DEFAULT NULL COMMENT '店铺审核通过时间',
  `shu_dao_flag` tinyint DEFAULT '0' COMMENT '是否为蜀道企业或者内部(1是，0否)',
  `version` int DEFAULT '1' COMMENT '乐观锁',
  PRIMARY KEY (`shop_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='店铺';

-- ----------------------------
-- Records of shop
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for shop_duration
-- ----------------------------
DROP TABLE IF EXISTS `shop_duration`;
CREATE TABLE `shop_duration` (
  `duration_id` char(36) NOT NULL COMMENT '店铺时长记录表',
  `shop_id` char(36) NOT NULL COMMENT '店铺ID',
  `payment_date` datetime DEFAULT NULL COMMENT '缴费日期',
  `shop_type` tinyint DEFAULT NULL COMMENT '服务类型（0集采模块、1租赁模块、2物流模块、3其他模块）',
  `start_date` datetime DEFAULT NULL COMMENT '服务开始时间',
  `end_date` datetime DEFAULT NULL COMMENT '服务到期时间',
  `status` tinyint DEFAULT NULL COMMENT '状态（0未激活、-1已过期、1正常）',
  `enterprise_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '企业id',
  `enterprise_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '企业名称',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `sort` int DEFAULT NULL COMMENT '排序',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除： -1: 删除 0:未删除',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  `shop_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '店铺名称',
  `shu_dao_flag` tinyint DEFAULT '0' COMMENT '是否为蜀道企业(1是，0否)',
  `payment_record_item_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '缴费记录id',
  PRIMARY KEY (`duration_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='店铺授权服务时间表';

-- ----------------------------
-- Records of shop_duration
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for shop_duration_history
-- ----------------------------
DROP TABLE IF EXISTS `shop_duration_history`;
CREATE TABLE `shop_duration_history` (
  `duration_history_id` varchar(36) NOT NULL COMMENT '店铺服务历史记录ID',
  `duration_id` varchar(36) NOT NULL COMMENT '店铺时长表',
  `old_start_date` datetime DEFAULT NULL COMMENT '更新前开始时间',
  `old_end_date` datetime DEFAULT NULL COMMENT '更新前结束时间',
  `payment_duration` int DEFAULT NULL COMMENT '修改时长',
  `payment_type` char(16) DEFAULT NULL COMMENT '修改时长单位',
  `new_start_date` datetime DEFAULT NULL COMMENT '更新后开始时间',
  `new_end_date` datetime DEFAULT NULL COMMENT '更新后结束时间',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `sort` int DEFAULT NULL COMMENT '排序',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除： -1: 删除 0:未删除',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  `shop_type` tinyint DEFAULT NULL COMMENT '服务类型（0集采模块、1租赁模块、2物流模块、3其他模块）',
  `editPerson_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人Id',
  `editPerson_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人名称',
  `payment_record_item_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '缴费记录id',
  `shu_dao_flag` tinyint DEFAULT '0' COMMENT '是否为蜀道企业(1是，0否)',
  `shop_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '店铺ID',
  PRIMARY KEY (`duration_history_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='店铺服务时长修改历史记录，保存主表修改记录';

-- ----------------------------
-- Records of shop_duration_history
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for shop_payment_record
-- ----------------------------
DROP TABLE IF EXISTS `shop_payment_record`;
CREATE TABLE `shop_payment_record` (
  `payment_record_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '缴费记录id',
  `shop_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '店铺id',
  `shop_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '店铺名称',
  `shop_type` tinyint DEFAULT NULL COMMENT '服务类型（0集采模块、1租赁模块、2物流模块、3其他模块）',
  `enterprise_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '企业id',
  `enterprise_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '企业名称',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间	',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
  `sort` int DEFAULT NULL COMMENT '排序',
  `state` tinyint DEFAULT NULL COMMENT '状态',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  PRIMARY KEY (`payment_record_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='店铺缴费记录主表';

-- ----------------------------
-- Records of shop_payment_record
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for shop_payment_record_item
-- ----------------------------
DROP TABLE IF EXISTS `shop_payment_record_item`;
CREATE TABLE `shop_payment_record_item` (
  `payment_record_item_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '缴费记录id',
  `payment_record_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '缴费id',
  `shop_type` char(36) DEFAULT NULL COMMENT '服务类型（0集采模块、1租赁模块、2物流模块、3其他模块）',
  `enterprise_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '企业id',
  `enterprise_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '企业名称',
  `payment_date` datetime DEFAULT NULL COMMENT '缴费时间',
  `payment_amount` decimal(18,2) DEFAULT NULL COMMENT '缴费金额',
  `payment_duration` int DEFAULT NULL COMMENT '缴费时长',
  `payment_duration_type` char(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '缴费时长类型（单位）（0天/1月/2年）',
  `approval_date` datetime DEFAULT NULL COMMENT '审核日期',
  `status` tinyint DEFAULT NULL COMMENT '(0草稿1待审核，2审核通过，3审核未通过)',
  `fail_reason` varchar(255) DEFAULT NULL COMMENT '审核失败原因',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `sort` int DEFAULT NULL COMMENT '排序',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除： -1: 删除 0:未删除',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `approval_person` varchar(18) DEFAULT NULL COMMENT '审核人员',
  `shop_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '店铺id',
  `shop_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '店铺名称',
  `pay_status` tinyint DEFAULT NULL COMMENT '店铺缴费状态（0 入驻费用，1 续费）',
  `pay_state` tinyint DEFAULT NULL COMMENT '是否缴费（1已经缴费，0未缴费）',
  `create_type` tinyint DEFAULT '1' COMMENT '缴费方式（0系统，1店铺）',
  `shu_dao_flag` tinyint DEFAULT '0' COMMENT '是否为蜀道企业(1是，0否)',
  `pay_bill_state` tinyint DEFAULT '0' COMMENT '是否开发票 0初始 1已申请 2已开票',
  `contract_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '合同编号',
  `contract_status` tinyint DEFAULT '0' COMMENT '合同状态（1已下载，0未下载）',
  `contract_date` datetime DEFAULT NULL COMMENT '合同首次下载时间',
  `contact` varchar(255) DEFAULT NULL COMMENT '商家联系人',
  `contact_phone` varchar(255) DEFAULT NULL COMMENT '商家联系电话',
  `mail_address` varchar(255) DEFAULT NULL COMMENT '商家详细联系地址',
  `old_contract_no` varchar(255) DEFAULT NULL COMMENT '旧的合同编号（跨年情况才会存在值）',
  `transfer_date` datetime DEFAULT NULL COMMENT '转账日期（可修改，默认为审核日期）',
  `version` int NOT NULL DEFAULT '1' COMMENT '乐观锁',
  PRIMARY KEY (`payment_record_item_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Records of shop_payment_record_item
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for shop_role_association
-- ----------------------------
DROP TABLE IF EXISTS `shop_role_association`;
CREATE TABLE `shop_role_association` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '店铺角色关联id',
  `shop_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '店铺id',
  `role_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '角色id',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间	',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
  `sort` int DEFAULT NULL COMMENT '排序',
  `state` tinyint DEFAULT NULL COMMENT '状态',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `shop_id` (`shop_id`) USING BTREE,
  KEY `role_id` (`role_id`) USING BTREE,
  CONSTRAINT `role_id` FOREIGN KEY (`role_id`) REFERENCES `role` (`role_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `shop_id` FOREIGN KEY (`shop_id`) REFERENCES `shop` (`shop_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=4873 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='店铺—角色类型关联表';

-- ----------------------------
-- Records of shop_role_association
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for shopping_cart
-- ----------------------------
DROP TABLE IF EXISTS `shopping_cart`;
CREATE TABLE `shopping_cart` (
  `cart_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '购物车id',
  `shop_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '店铺id',
  `product_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '商品id',
  `sku_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'skuid',
  `user_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户id',
  `cart_num` decimal(18,3) DEFAULT NULL COMMENT '购物车商品数量',
  `cart_time` datetime DEFAULT NULL COMMENT '添加购物车时间',
  `product_price` decimal(18,2) NOT NULL COMMENT '添加购物车时商品价格',
  `sku_props` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '选择的规格属性',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除 1: 删除 0:未删除',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `product_type` tinyint unsigned DEFAULT NULL COMMENT '商品类型：0物资 1设备 2周材（物资） 3周材（设备） 4二手设备 5租赁设备',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `sort` int DEFAULT NULL COMMENT '排序',
  `state` tinyint DEFAULT NULL COMMENT '状态',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  `cart_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '购物车图片',
  `lease_num` decimal(18,3) NOT NULL DEFAULT '1.000' COMMENT '租赁时长',
  `lease_unit` char(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '租赁单位（天月年）',
  `checked` tinyint DEFAULT '1' COMMENT '是否选中，1选中0不选中',
  PRIMARY KEY (`cart_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='购物车';

-- ----------------------------
-- Records of shopping_cart
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for shudao_enterprise
-- ----------------------------
DROP TABLE IF EXISTS `shudao_enterprise`;
CREATE TABLE `shudao_enterprise` (
  `shudao_enterprise_id` char(36) NOT NULL COMMENT '蜀道企业ID',
  `affiliation_enterprise` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '隶属企业',
  `enterprise_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '公司名称',
  `enterprise_category` tinyint DEFAULT NULL COMMENT '企业类别(1:一类 2:二类 3:三类)',
  `adjust` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '调整',
  `enterprise_nature` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '企业性质',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间	',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间	',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `sort` int DEFAULT NULL COMMENT '排序',
  `state` tinyint DEFAULT NULL COMMENT '状态',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型',
  `is_delete` tinyint DEFAULT '0' COMMENT '逻辑删除：（ -1: 删除  0:未删除）',
  PRIMARY KEY (`shudao_enterprise_id`) USING BTREE,
  UNIQUE KEY `1` (`enterprise_name`) USING BTREE COMMENT '企业名称不重复'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Records of shudao_enterprise
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for sku_sale_attribute_value
-- ----------------------------
DROP TABLE IF EXISTS `sku_sale_attribute_value`;
CREATE TABLE `sku_sale_attribute_value` (
  `sku_sale_attribute_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'sku销售id',
  `sku_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'sku_id',
  `attribute_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '属性id',
  `attribute_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '销售属性名',
  `attribute_value` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '销售属性值',
  `sort` int DEFAULT NULL COMMENT '排序',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
  `state` tinyint DEFAULT NULL COMMENT '状态',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `product_type` tinyint DEFAULT NULL COMMENT '商品类型：0物资 1设备 2周材（物资） 3周材（设备） 4二手设备 5租赁设备',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  PRIMARY KEY (`sku_sale_attribute_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='sku销售属性&值';

-- ----------------------------
-- Records of sku_sale_attribute_value
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for smport_suppler_res
-- ----------------------------
DROP TABLE IF EXISTS `smport_suppler_res`;
CREATE TABLE `smport_suppler_res` (
  `import_suppler_id` int NOT NULL,
  PRIMARY KEY (`import_suppler_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Records of smport_suppler_res
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for station_message
-- ----------------------------
DROP TABLE IF EXISTS `station_message`;
CREATE TABLE `station_message` (
  `station_message_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '消息id',
  `send_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '发件人id',
  `send_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '发件人名称',
  `send_type` tinyint DEFAULT NULL COMMENT '发件人类型（0店铺1用户2平台）',
  `send_code` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '发件人消息账号',
  `all_read` tinyint DEFAULT NULL COMMENT '是否全部已读(0否1是)',
  `is_file` tinyint DEFAULT NULL COMMENT '是否有附件(0否1是)',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '内容',
  `send_date` datetime DEFAULT NULL COMMENT '发送时间',
  `message_type` tinyint DEFAULT NULL COMMENT '消息类型',
  `state` tinyint DEFAULT NULL COMMENT '状态',
  `sort` int DEFAULT NULL COMMENT '排序',
  `is_delete` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除 -1: 删除 0:未删除',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  `remind` tinyint DEFAULT '0' COMMENT '是否弹窗提醒(0否，1是)',
  PRIMARY KEY (`station_message_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='站点消息';

-- ----------------------------
-- Records of station_message
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for station_message_receive
-- ----------------------------
DROP TABLE IF EXISTS `station_message_receive`;
CREATE TABLE `station_message_receive` (
  `station_message_receive_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '消息接收id',
  `receive_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '收件人id',
  `receive_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '收件人名称',
  `receive_type` tinyint DEFAULT NULL COMMENT '收件人类型（0店铺1用户2平台）',
  `receive_code` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '收件人消息账号',
  `is_read` tinyint DEFAULT NULL COMMENT '是否已读（0否1是）',
  `read_date` datetime DEFAULT NULL COMMENT '已读时间',
  `sort` int DEFAULT NULL COMMENT '排序',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  `station_message_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '消息id',
  PRIMARY KEY (`station_message_receive_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='站点接收消息';

-- ----------------------------
-- Records of station_message_receive
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for statistical_num
-- ----------------------------
DROP TABLE IF EXISTS `statistical_num`;
CREATE TABLE `statistical_num` (
  `id` char(1) NOT NULL,
  `statistical_Date` datetime DEFAULT NULL COMMENT '统计时间',
  `enterprise_num` int DEFAULT NULL COMMENT '注册商家数',
  `shop_num` int DEFAULT NULL COMMENT '店铺数',
  `in_enterPrise` int DEFAULT NULL COMMENT '内部企业数',
  `product_num` int DEFAULT NULL COMMENT '商品总数',
  `up_product_num` int DEFAULT NULL COMMENT '商品上架总数',
  `user_num` int DEFAULT NULL COMMENT '用户数',
  `equipment` int DEFAULT NULL COMMENT '装备数',
  `two_equipment` int DEFAULT NULL COMMENT '二手装备数',
  `lease_count` int DEFAULT NULL COMMENT '租赁装备数装备数',
  `purchase` int DEFAULT NULL COMMENT '求购数',
  `lease_orders` int DEFAULT NULL COMMENT '租赁装备订单',
  `equipment_orders` int DEFAULT NULL COMMENT '装备订单',
  `lease_orders_amount` decimal(18,3) DEFAULT NULL COMMENT '租赁装备订单',
  `equipment_orders_amount` decimal(18,3) DEFAULT NULL COMMENT '租赁装备订单',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='物资数据统计';

-- ----------------------------
-- Records of statistical_num
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for sys_oper_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_oper_log`;
CREATE TABLE `sys_oper_log` (
  `id` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `oper_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '主机地址',
  `operator_type` int DEFAULT '0' COMMENT '操作类别：0商城 1后台用户 2小程序用户',
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '模块标题',
  `business_type` int DEFAULT '0' COMMENT '业务类型:0=其它,1=新增,2=修改,3=删除,4=上传,5=导出,6=导入',
  `method` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '方法名称',
  `request_method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '请求方式',
  `oper_param` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '请求参数',
  `json_result` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '返回参数',
  `status` int DEFAULT '0' COMMENT '操作状态（0正常 1异常）',
  `error_msg` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '错误消息',
  `oper_time` datetime DEFAULT NULL COMMENT '操作时间',
  `cost_time` bigint DEFAULT '0' COMMENT '消耗时间',
  `oper_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '操作人员',
  `org_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '单位/企业名称',
  `oper_location` varchar(255) DEFAULT '',
  `oper_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '请求URL',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Records of sys_oper_log
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for sys_task
-- ----------------------------
DROP TABLE IF EXISTS `sys_task`;
CREATE TABLE `sys_task` (
  `task_id` char(36) NOT NULL COMMENT '任务id',
  `task_title` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '任务标题',
  `task_val1` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '任务值1',
  `task_val2` varchar(2000) DEFAULT NULL COMMENT '任务值2',
  `task_type` tinyint DEFAULT NULL COMMENT '任务类型（）',
  `is_dispose` tinyint NOT NULL DEFAULT '0' COMMENT '是否处理（0否1是默认0）',
  `is_succeed` tinyint DEFAULT NULL COMMENT '是否成功（0否1是）',
  `assignee` varchar(255) DEFAULT NULL COMMENT '负责人姓名',
  `succeed_time` datetime DEFAULT NULL COMMENT '成功时间',
  `error_count` int DEFAULT '0' COMMENT '失败次数',
  `priority` tinyint DEFAULT NULL COMMENT '优先级（1低2中3高4最高）',
  `due_time` datetime DEFAULT NULL COMMENT '截止时间',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间	',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
  `sort` int DEFAULT NULL COMMENT '排序',
  `state` tinyint DEFAULT NULL COMMENT '状态',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城',
  PRIMARY KEY (`task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='任务表';

-- ----------------------------
-- Records of sys_task
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for system_param
-- ----------------------------
DROP TABLE IF EXISTS `system_param`;
CREATE TABLE `system_param` (
  `system_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '系统参数表id',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '名称',
  `code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'code',
  `key_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '值',
  `key_value2` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '值2',
  `remarks` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '说明',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '修改时间',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城',
  `maintain` tinyint DEFAULT NULL COMMENT '维护: 0不可维护 1可维护',
  `sort` int DEFAULT NULL COMMENT '排序',
  `type` tinyint DEFAULT NULL COMMENT '0:参数 1:字典',
  `is_delete` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除 -1: 删除 0:未删除',
  PRIMARY KEY (`system_id`) USING BTREE,
  KEY `code_index` (`code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='系统参数表';

-- ----------------------------
-- Records of system_param
-- ----------------------------
BEGIN;
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('12868836828763798378839', '图片前缀', 'imgUrlPrefixAdd', 'http://192.168.91.16:9024', NULL, NULL, '2024-06-06 10:44:40', NULL, 1, 1, NULL, 0, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('128688368287637983788890', '图片删除前缀', 'imgUrlPrefixDelete', 'http://192.168.91.11:9000', NULL, NULL, '2024-06-06 10:44:40', NULL, 1, 1, NULL, 0, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1614323234923432343', '装备结算比例', 'deviceRatio', '100.00%', NULL, '装备结算比例', NULL, NULL, 1, 1, NULL, 0, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1614323455575757825', '交易总金额', 'transactionAmount', '178206', NULL, '交易总金额', NULL, '2023-03-07 17:07:26', 1, 1, 5, 0, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1614323825234000239', '已发布需求', 'publishRequireTotal', '893', NULL, '已发布需求', NULL, '2023-03-07 17:07:12', 1, 1, 1, 0, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1614323825820392943', '交易项目数', 'transactionProjectTotal', '1144', '', '交易项目数', NULL, '2022-12-21 14:51:31', 1, 1, 3, 0, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1614323828381238288', '物资服务热线号码', 'materialIndexPageServiceNumber', '400-0000-001', NULL, '服务热线号码', NULL, NULL, 0, 1, NULL, 0, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1614323828385757825', '入驻商家数', 'merchantsSettledTotal', '3877', '', '入驻商家数', NULL, '2022-12-21 14:51:31', 1, 1, NULL, 0, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1614323828385782539', '内容管理', 'sincerityManagement', '400-0000-001', NULL, '服务热线号码', NULL, NULL, 1, 1, NULL, 0, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1614323828385782831', '装备服务热线号码', 'deviceIndexPageServiceNumber', '400-0000-001', NULL, '服务热线号码', NULL, NULL, 1, 1, NULL, 0, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1614433840121057281', '城市', 'materialCity', '成都市', '成都市', NULL, '2023-01-15 09:26:37', '2023-01-15 09:26:37', 0, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1614433953375653890', '城市', 'materialCity', '达州市', '达州市', NULL, '2023-01-15 09:27:04', '2023-01-15 09:27:04', 0, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1614433995335471106', '城市', 'materialCity', '巴中市', '巴中市', NULL, '2023-01-15 09:27:14', '2023-01-15 09:27:14', 0, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1614434319165100033', '城市', 'materialCity', '乐山市', '乐山市', NULL, '2023-01-15 09:28:31', '2023-01-15 09:28:31', 0, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1614448170052919298', '城市', 'deviceCity', '成都市', '成都市', NULL, '2023-01-15 10:23:33', '2023-01-15 10:23:33', 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1614448204903391233', '城市', 'deviceCity', '巴中市', '巴中市', NULL, '2023-01-15 10:23:42', '2023-01-15 10:23:42', 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1614448242199142402', '城市', 'deviceCity', '达州市', '达州市', NULL, '2023-01-15 10:23:51', '2023-01-15 10:23:51', 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1614459866217709570', '城市', 'deviceCity', '自贡市', '自贡市', NULL, '2023-01-15 11:10:02', '2023-01-15 11:10:02', 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1614459990616571905', '城市', 'deviceCity', '泸州市', '泸州市', NULL, '2023-01-15 11:10:32', '2023-01-15 11:10:32', 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1614460046597947394', '城市', 'deviceCity', '内江市', '内江市', '3', '2023-01-15 11:10:45', '2023-01-17 13:51:21', 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1614502312972722177', '二手装备成色', 'deviceQuality', '5成新', '5', NULL, '2023-01-15 13:58:42', '2023-01-18 11:30:21', 1, 1, 1, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1614502395097194498', '二手装备成色', 'deviceQuality', '6成新', '6', '1', '2023-01-15 13:59:02', '2023-01-18 11:29:59', 1, 1, 2, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1614502457181282305', '二手装备成色', 'deviceQuality', '7成新', '7', NULL, '2023-01-15 13:59:16', '2023-01-18 11:29:59', 1, 1, 3, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1614502499870908418', '二手装备成色', 'deviceQuality', '8.8成新', '88', NULL, '2023-01-15 13:59:27', '2023-01-18 11:30:21', 1, 1, 2, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1614502605575757825', '二手装备成色', 'deviceQuality', '9成新', '9', NULL, '2023-01-15 13:59:52', '2023-03-20 15:35:32', 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1625056224301645825', '金融公司', 'financeFirm', '深建设工程max', '234234234-1e28', NULL, '2023-02-13 16:56:11', '2023-02-13 16:56:11', 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1627504719369351170', '保险公司', 'insuranceFirm', '深建设工程max', '234234234-1e28', '13123', '2023-02-20 11:05:37', '2023-03-20 15:33:03', 1, 1, 122131, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1676894848129495042', '城市', 'deviceCity', '甘孜藏族自治州', '甘孜藏族自治州', NULL, '2023-07-06 18:04:22', '2023-07-06 18:04:32', 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1676894982884093953', '城市', 'deviceCity', '凉山彝族自治州', '凉山彝族自治州', NULL, '2023-07-06 18:04:54', '2023-07-06 18:04:54', 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1676895049384783874', '城市', 'deviceCity', '阿坝藏族羌族自治州', '阿坝藏族羌族自治州', NULL, '2023-07-06 18:05:10', '2023-07-06 18:05:10', 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1676895222806671362', '城市', 'deviceCity', '攀枝花市', '攀枝花市', NULL, '2023-07-06 18:05:51', '2023-07-06 18:05:51', 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1676897906729218050', '城市', 'deviceCity', '德阳市', '德阳市', NULL, '2023-07-06 18:16:31', '2023-07-06 18:16:31', 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1676898335257063425', '城市', 'deviceCity', '绵阳市', '绵阳市', NULL, '2023-07-06 18:18:13', '2023-07-06 18:18:13', 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1676900830771474434', '城市', 'deviceCity', '广元市', '广元市', NULL, '2023-07-06 18:28:08', '2023-07-06 18:28:08', 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1676900887247777793', '城市', 'deviceCity', '遂宁市', '遂宁市', NULL, '2023-07-06 18:28:21', '2023-07-06 18:28:21', 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1676901004734427137', '城市', 'deviceCity', '乐山市', '乐山市', NULL, '2023-07-06 18:28:49', '2023-07-06 18:28:49', 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1676901069137965058', '城市', 'deviceCity', '南充市', '南充市', NULL, '2023-07-06 18:29:05', '2023-07-06 18:29:05', 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1676901135353442305', '城市', 'deviceCity', '眉山市', '眉山市', NULL, '2023-07-06 18:29:21', '2023-07-06 18:29:21', 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1676901191431286786', '城市', 'deviceCity', '宜宾市', '宜宾市', NULL, '2023-07-06 18:29:34', '2023-07-06 18:29:34', 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1676901237509910529', '城市', 'deviceCity', '广安市', '广安市', NULL, '2023-07-06 18:29:45', '2023-07-06 18:29:45', 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1676901334696128513', '城市', 'deviceCity', '雅安市', '雅安市', NULL, '2023-07-06 18:30:08', '2023-07-06 18:30:08', 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1676901451696238593', '城市', 'deviceCity', '资阳市', '资阳市', NULL, '2023-07-06 18:30:36', '2023-07-06 18:30:36', 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1844919034851176449', '计量单位', 'deviceUnit', 'test', '<script>console.log(\'2324234\')<script>', '<script>console.log(\'2324234\')<script>', '2024-10-12 09:52:30', '2024-10-12 09:53:05', 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1853335275275567106', '二手装备成色', 'deviceQuality', '11121213', '121312321', NULL, '2024-11-04 15:15:38', '2024-11-04 15:15:38', 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1853336047656751105', '二手装备成色', 'deviceQuality', '67890', '6789', NULL, '2024-11-04 15:18:42', '2024-11-04 15:18:42', 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1853336281048797186', '二手装备成色', 'deviceQuality', '999999', '889989998u我', NULL, '2024-11-04 15:19:37', '2024-11-04 15:20:13', 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1853336486892654593', '二手装备成色', 'deviceQuality', '12313123123123', '213123123', NULL, '2024-11-04 15:20:26', '2024-11-04 15:20:26', 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1853338424094158850', '金融公司', 'financeFirm', '太平洋', '1', NULL, '2024-11-04 15:28:08', '2024-11-04 15:28:08', 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1853338680739426306', '保险公司', 'insuranceFirm', '中国平安', '1', NULL, '2024-11-04 15:29:10', '2024-11-04 15:29:10', 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1853338842866053122', '保险公司', 'insuranceFirm', '太平洋2', '太平洋2', NULL, '2024-11-04 15:29:48', '2024-11-04 15:29:48', 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1853345589551472641', '金融公司', 'financeFirm', '中正', '证券', NULL, '2024-11-04 15:56:37', '2024-11-04 15:56:37', 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1853346706150703105', '保险公司', 'insuranceFirm', '112', '213', NULL, '2024-11-04 16:01:03', '2024-11-04 16:01:03', 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1853347223459381250', '保险公司', 'insuranceFirm', '1111111', '111', NULL, '2024-11-04 16:03:06', '2024-11-04 16:03:06', 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('1853349347769532418', '保险公司', 'insuranceFirm', '322424234', '32423', NULL, '2024-11-04 16:11:33', '2024-11-04 16:11:33', 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('213213234234324324324324234234234', '合同编号失效时间', 'contractInvalidTime', '3', NULL, '单位是（月）', '2024-01-19 09:23:38', NULL, 1, 1, NULL, 0, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe7eeed-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '台', '台', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe7ef90-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '套', '套', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe7efa1-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '辆', '辆', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe7f2be-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '个', '个', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe7f2e6-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '艘', '艘', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe7f388-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '座', '座', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe7f4f7-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '间', '间', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe7f530-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '栋', '栋', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe7f558-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '部', '部', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe7f6e5-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '节', '节', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe7f6f5-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '片', '片', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe7f84a-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '把', '把', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe7f853-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '付', '付', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe7f85b-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '根', '根', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe7f873-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '组', '组', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe7f883-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '对', '对', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe7fa42-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '副', '副', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe7fa79-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '元/小时', '元/小时', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe7fa9d-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '月', '月', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe7faa6-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '小时', '小时', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe7faf2-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '叙古A3分部', '叙古A3分部', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe7fc1b-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '吨', '吨', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe7fc4f-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '台套', '台套', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe7fd2f-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '太', '太', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe7fd9f-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '方', '方', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe7fdce-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', 'm3', 'm3', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe7fe0b-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', 'm&sup3;', 'm&sup3;', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe7fe36-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '天', '天', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe7ffbc-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '台*套', '台*套', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe7ffd5-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '米', '米', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe8001e-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '条', '条', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe80084-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '巴南广高速公路TJ2-10分部', '巴南广高速公路TJ2-10分部', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe80091-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '巴南广TJ2-10分部', '巴南广TJ2-10分部', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe800ef-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '项', '项', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe8023d-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '日', '日', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe802ea-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '1', '1', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe80341-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '趟', '趟', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe80352-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '次', '次', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe80371-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '件', '件', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe803b5-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '合计', '合计', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe803c5-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '元', '元', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe8041b-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '千克', '千克', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe80479-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '批', '批', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe80489-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '元/t*km', '元/t*km', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe804e2-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '元/人', '元/人', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe804ea-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '元/节', '元/节', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe804f3-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '元/道', '元/道', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe804fb-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '元/台', '元/台', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe806a1-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '梁', '梁', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe80714-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', 'm³', 'm³', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe80788-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '台班', '台班', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe807fd-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '吨.公里', '吨.公里', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe80834-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '车', '车', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe80a0d-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '元/月', '元/月', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe80a29-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '只', '只', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe80a9d-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '位', '位', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe80aac-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '张', '张', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe80ae8-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '升', '升', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe80b9d-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '块', '块', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe80c1d-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '111', '111', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe80c2c-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', 'kg', 'kg', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe80cfd-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '无', '无', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe80d1a-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '元/吨', '元/吨', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe80df4-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '吨/公路', '吨/公路', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe80e89-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '7000T', '7000T', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe80e99-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '550T', '550T', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe81106-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '颗', '颗', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe8114c-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '支', '支', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe814b7-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', 'm²', 'm²', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe81502-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '趟次', '趟次', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe8162a-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', '本', '本', NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
INSERT INTO `system_param` (`system_id`, `name`, `code`, `key_value`, `key_value2`, `remarks`, `gmt_create`, `gmt_modified`, `mall_type`, `maintain`, `sort`, `type`, `is_delete`) VALUES ('bfe8176b-cd1b-11ed-83d4-7cd30a96cb9b', '计量单位', 'deviceUnit', NULL, NULL, NULL, '2023-03-28 11:50:58', NULL, 1, 1, NULL, 1, 0);
COMMIT;

-- ----------------------------
-- Table structure for undo_log
-- ----------------------------
DROP TABLE IF EXISTS `undo_log`;
CREATE TABLE `undo_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `branch_id` bigint NOT NULL,
  `xid` varchar(100) NOT NULL,
  `context` varchar(128) NOT NULL,
  `rollback_info` longblob NOT NULL,
  `log_status` int NOT NULL,
  `log_created` datetime NOT NULL,
  `log_modified` datetime NOT NULL,
  `ext` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `ux_undo_log` (`xid`,`branch_id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb3;

-- ----------------------------
-- Records of undo_log
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user` (
  `user_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户id',
  `interior_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '内部id',
  `user_number` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '用户编号',
  `account` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '账号',
  `password` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '用户密码',
  `user_mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '手机号码',
  `email` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '邮箱',
  `user_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '头像(图片地址)',
  `user_img_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '图片记录id',
  `real_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '真实姓名',
  `nick_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '昵称',
  `gender` tinyint unsigned DEFAULT NULL COMMENT '性别 1:男 0: 女',
  `province` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '省份',
  `city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '城市',
  `county` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '区县',
  `detail_address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '详细地址',
  `birthday` date DEFAULT NULL COMMENT '生日',
  `device_state` tinyint NOT NULL DEFAULT '1' COMMENT '装备状态（0停用1启用）',
  `material_state` tinyint NOT NULL DEFAULT '1' COMMENT '物资状态（0停用1启用）',
  `state` tinyint unsigned DEFAULT NULL COMMENT '用户状态 0：初始（默认） 1：启用  2:禁用 ',
  `is_admin` tinyint unsigned DEFAULT NULL COMMENT '是否为管理员  1：是  0：不是',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `gmt_login` datetime DEFAULT NULL COMMENT '上次登录时间',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `is_material` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否为物资商城注册   1：是   0：否',
  `is_device` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否为设备商城注册   1：是   0：否',
  `enterprise_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '企业附加信息id',
  `is_internal_user` tinyint NOT NULL DEFAULT '0' COMMENT '是否为内部用户   1：是   0：否（默认：0）',
  `platform_admin` tinyint DEFAULT NULL COMMENT '是否为平台管理员  1：是  0：不是（默认：0）',
  `sort` int DEFAULT NULL COMMENT '排序',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
  `wx_open_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '微信的openId',
  `login_count` int unsigned NOT NULL DEFAULT '0' COMMENT '登陆次数',
  `locked_state` int DEFAULT '0' COMMENT '锁定状态(0正常 1锁定)',
  `pwd_change_date` datetime DEFAULT NULL COMMENT '上次密码修改时间',
  `password_err_times` int DEFAULT '0' COMMENT '密码错误次数',
  `lock_cause` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '锁定原因',
  `last_failed_login_time` datetime DEFAULT NULL COMMENT '上一次密码错误时间',
  `attr_one` tinyint DEFAULT NULL COMMENT 'dfasdfs',
  PRIMARY KEY (`user_id`) USING BTREE,
  KEY `interior_id` (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户';

-- ----------------------------
-- Records of user
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for user_activity
-- ----------------------------
DROP TABLE IF EXISTS `user_activity`;
CREATE TABLE `user_activity` (
  `user_activity_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户行为id',
  `user_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '用户id',
  `user_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '用户名称',
  `login_time` datetime DEFAULT NULL COMMENT '登陆时间',
  `org_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '机构id',
  `org_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '机构名称',
  `org_type` tinyint DEFAULT NULL COMMENT '机构类型：0：个体户  1：企业  2：个人',
  `login_ip` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '登陆ip',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '备注信息',
  `login_type` varchar(255) DEFAULT NULL COMMENT '登录方式',
  `user_mobile` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '用户手机号',
  `bus_type` tinyint NOT NULL DEFAULT '0' COMMENT '行为类型（0登陆1发送验证码）',
  PRIMARY KEY (`user_activity_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='用户行为表';

-- ----------------------------
-- Records of user_activity
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for user_address
-- ----------------------------
DROP TABLE IF EXISTS `user_address`;
CREATE TABLE `user_address` (
  `address_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '地址id',
  `user_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '用户id',
  `alias_address` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '地址别名',
  `receiver_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '收件人姓名',
  `receiver_mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '收件人手机号',
  `fixed_telephone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '固定电话',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '邮箱',
  `province` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '省份',
  `city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '城市',
  `county` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '区县',
  `detail_address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '详细地址',
  `post_code` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '邮编',
  `state` tinyint unsigned DEFAULT NULL COMMENT '状态,1正常，0无效',
  `is_default_address` tinyint unsigned DEFAULT NULL COMMENT '是否默认地址  1:是  0:否',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `sort` int DEFAULT NULL COMMENT '排序',
  `mall_type` tinyint DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  `is_delete` tinyint DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
  PRIMARY KEY (`address_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户地址';

-- ----------------------------
-- Records of user_address
-- ----------------------------
BEGIN;
COMMIT;

SET FOREIGN_KEY_CHECKS = 1;
