package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.entity.OrderSelectContact;
import scrbg.meplat.mall.service.OrderSelectContactService;

import java.util.List;

/**
 * @描述：订单关联合同控制类
 * @作者: ye
 * @日期: 2023-04-23
 */
@RestController
@RequestMapping("/orderSelectContact")
@Api(tags = "订单关联合同")
public class OrderSelectContactController{

@Autowired
public OrderSelectContactService orderSelectContactService;

@PostMapping("/listByEntity")
@ApiOperation(value = "根据实体属性分页查询")
@DynamicParameters(name = "根据实体属性分页查询", properties = {
        @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
        @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
})
public PageR<OrderSelectContact> listByEntity(@RequestBody JSONObject jsonObject){
        PageUtils page= orderSelectContactService.queryPage(jsonObject,new LambdaQueryWrapper<OrderSelectContact>());
        return PageR.success(page);
        }

@GetMapping("/findById")
@ApiOperation(value = "根据主键查询")
@ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "ID", required = true,
                dataType = "String", paramType = "query")
})
public R<OrderSelectContact> findById(String id){
    OrderSelectContact orderSelectContact = orderSelectContactService.getById(id);
        return R.success(orderSelectContact);
        }

@PostMapping("/create")
@ApiOperation(value = "新增")
public R save(@RequestBody OrderSelectContact orderSelectContact){
    orderSelectContactService.create(orderSelectContact);
        return R.success();
        }

@PostMapping("/update")
@ApiOperation(value = "修改")
public R update(@RequestBody OrderSelectContact orderSelectContact){
    orderSelectContactService.update(orderSelectContact);
        return R.success();
        }

@GetMapping("/delete")
@ApiOperation(value = "根据主键删除")
@ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "ID", required = true,
                dataType = "String", paramType = "query")
})
public R delete(String id){
    orderSelectContactService.delete(id);
        return R.success();
        }


@PostMapping("/deleteBatch")
@ApiOperation(value = "根据主键批量删除")
public R deleteBatch(@RequestBody List<String> ids){
    orderSelectContactService.removeByIds(ids);
        return R.success();
        }
        }

