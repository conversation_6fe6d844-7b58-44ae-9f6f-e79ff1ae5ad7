package scrbg.meplat.mall.dto.reconciliation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @描述：供应商计划对账查询DTO
 * @作者: tanfei
 * @日期: 2025-06-19
 */
@ApiModel(value = "供应商计划对账查询DTO")
@Data
public class SupplierPlanReconciliationQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "收料开始时间")
    private String startTime;

    @ApiModelProperty(value = "收料结束时间")
    private String endTime;

    @ApiModelProperty(value = "计划编号")
    private String billNo;

    @ApiModelProperty(value = "订单编号（支持多个，用逗号隔开）")
    private String orderSn;

    @ApiModelProperty(value = "关键字（支持计划编号、订单编号、收货单位名称模糊查询）")
    private String keyWord;

    @ApiModelProperty(value = "当前页数")
    private Integer page;

    @ApiModelProperty(value = "每页显示条数")
    private Integer limit;

    @ApiModelProperty(value = "供应商id")
    private String supplierId;

    @ApiModelProperty(value = "业务类型(新类型) 1、零星采购 2、大宗临购 3、周转材料 老类型：10、零星采购 12、大宗临购")
    private Integer productType;

    @ApiModelProperty(value = "1浮动价格对账单2固定价格对账单")
    private String type;

}
