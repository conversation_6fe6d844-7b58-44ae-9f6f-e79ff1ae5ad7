package scrbg.meplat.mall.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import scrbg.meplat.mall.entity.Shop;
import scrbg.meplat.mall.vo.product.website.material.WMaterialVO;

import java.util.List;
import java.util.Map;

/**
 * @描述：店铺 Mapper 接口
 * @作者: y
 * @日期: 2022-11-02
 */
@Mapper
@Repository
public interface ShopMapper extends BaseMapper<Shop> {

    @Delete("DELETE FROM shop WHERE shop=#{id}")
    void removeById(@Param("id")String id);

    /**
     * 查询店铺对应企业id
     * @param
     * @return
     */

    @Select("Select enterprise_id FROM shop WHERE shop_id = #{id}")
    List<Shop> findId(@Param("id")String id);

    List<Shop> getIndexSupplierList(Page<Shop> pages, @Param("dto") Map<String, Object> innerMap);

    int getIndexSupplierListCount(@Param("dto") Map<String, Object> innerMap);

    List<Shop> findPublicShop(Page<Shop> pages, @Param("dto") Map<String, Object> innerMap);

    /**
     * 根据企业id获取店铺
     * @param
     * @return
     */
    @Select("Select * FROM shop WHERE enterprise_id = #{id} and is_delete = 0" )
    Shop findByEnterpriseId(@Param("id")String id);


}
