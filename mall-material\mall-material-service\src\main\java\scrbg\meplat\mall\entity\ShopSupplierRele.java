package scrbg.meplat.mall.entity;

import java.io.Serializable;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

/**
 * @描述：店铺供方关联表
 * @作者: ye
 * @日期: 2023-06-05
 */
@ApiModel(value = "店铺供方关联表")
@Data
@TableName("shop_supplier_rele")
public class ShopSupplierRele extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "店铺供应商关联表id")
    private String shopSupplierReleId;

    @ApiModelProperty(value = "店铺id")
    private String shopId;

    @ApiModelProperty(value = "供应商id（本地机构id）")
    private String supplierId;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "供应商类型")
    private String supplierType;

    @ApiModelProperty(value = "是否采用定制化模式入库")
    private Boolean templateWarehousedIs;

    @ApiModelProperty(value = "入库类型")
    private String warehousedType;

    @ApiModelProperty(value = "入库描述")
    private String warehousedDesc;

    @ApiModelProperty(value = "附件")
    private String warehousedFile;

    @ApiModelProperty(value = "供应商分组")
    private String supplierGroup;

    @TableField(exist = false)
    @ApiModelProperty(value = "供应商税率")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "上架商品权限")
    private String listPermissions;

//    @ApiModelProperty(value = "上架商品权限类别")
//    private String listPermissionsType;

    @ApiModelProperty(value = "大宗商品权限")
    private String permissionsCommodities;

    @ApiModelProperty(value = "低值易耗商品权限(0无权限，1有权限)")
    private String permissionsLowValue;

    @ApiModelProperty(value = "周转材料(0无权限，1有权限)")
    private String permissionsTurnover;

    @ApiModelProperty(value = "审核状态(0 无，1 待审核 2 审核成功 3 驳回)")
    private int auditStatus;

    @ApiModelProperty(value = "店铺名称冗余")
    @TableField(exist = false)
    private String shopName;

    @TableField(exist = false)
    private List<String> ids;

    @TableField(exist = false)
    private List<File> files;

//    @ApiModelProperty(value = "上架商品权限类别冗余")
//    @TableField(exist = false)
//    private List<String>   listPermissionsTypeList;


}
