package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.entity.DepositBank;
import scrbg.meplat.mall.service.DepositBankService;

import java.util.List;

/**
 * @描述：开户银行信息表控制类
 * @作者: y
 * @日期: 2022-11-13
 */
@RestController
@RequestMapping("/depositBank")
@Api(tags = "开户银行信息表")
public class DepositBankController {

    @Autowired
    public DepositBankService depositBankService;

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<DepositBank> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = depositBankService.queryPage(jsonObject, new LambdaQueryWrapper<DepositBank>());
        return PageR.success(page);
    }

    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<DepositBank> findById(String id) {
        DepositBank depositBank = depositBankService.getById(id);
        return R.success(depositBank);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody DepositBank depositBank) {
        depositBankService.create(depositBank);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody DepositBank depositBank) {
        depositBankService.update(depositBank);
        return R.success();
    }

    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        depositBankService.delete(id);
        return R.success();
    }


    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")

    public R deleteBatch(@RequestBody List<String> ids) {
        depositBankService.removeByIds(ids);
        return R.success();
    }
}

