package scrbg.meplat.mall.dto.thirdapi;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @描述：物资类别库
 * @作者: fengyongji
 * @日期: 2021-10-21
 */
@ApiModel(value="物资类别库")
@Data
public class CategoryLibraryDTO {

    @ApiModelProperty(value = "物资类别id")
    private String billId;
    @ApiModelProperty(value = "上级类别id")
    private String parentClassId;
    @ApiModelProperty(value = "上级类别名称")
    private String parentClassName;
//    @ApiModelProperty(value = "类别编号")
//    private String billNo;
    @ApiModelProperty(value = "类别名称")
    private String className;
    @ApiModelProperty(value = "单位")
    private String unit;
    @ApiModelProperty(value = "是否启用(0：停用;1：启用)")
    private Integer isEnable;
    @ApiModelProperty(value = "物资类型(0：一般材料;1:：周转材料)")
    private Integer materialType;

    @ApiModelProperty(value = "远程机构Id")
    private String orgId;

    @ApiModelProperty(value = "远程机构名称")
    private String orgName;

}
