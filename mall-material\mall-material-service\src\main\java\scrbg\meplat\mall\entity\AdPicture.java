package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;
/**
 * @描述：广告图片
 * @作者: y
 * @日期: 2022-11-29
 */
@ApiModel(value="广告图片")
@Data
@TableName("ad_picture")
public class AdPicture extends MustBaseEntity implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "图片id")
    private String pictureId;

    @ApiModelProperty(value = "图片记录id")
    private String pictureUrlId;


    @ApiModelProperty(value = "存放地址")
    private String pictureUrl;


    @ApiModelProperty(value = "链接类型（1：无  2：内部链接地址 3：外部链接地址）")
    private Integer pictureType;


    @ApiModelProperty(value = "点击图片链接地址")
    private String pictureLinkAddress;

//    /**
//     *  { value: 0, label: '首页' },
//                { value: 1, label: '轮播图' },
//                { value: 2, label: '底部' },
//                { value: 3, label: '中部' },
//     */
    private Integer usePositioning;


    @ApiModelProperty(value = "面显示位置 平台首页1，商城首页2")
    private Integer useType;


    @ApiModelProperty(value = "状态（1：发布  2：未发布）")
    private Integer state;

    @ApiModelProperty(value = "排序方式")
    @TableField(
            exist = false
    )
    private Integer orderBy;

}
