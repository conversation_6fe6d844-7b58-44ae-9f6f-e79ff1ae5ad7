package scrbg.meplat.mall.dto.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023-12-21 14:32
 */
@Data
public class MallSysMenu {
    @ApiModelProperty(value = "菜单id")
    private String menuId;

    @ApiModelProperty(value = "菜单编号")
    private String code;


    @ApiModelProperty(value = "菜单名称")
    private String title;


    @ApiModelProperty(value = "菜单类型（1菜单2按钮3资源）")
    private String type;


    @ApiModelProperty(value = "权限标识（可选）")
    private String authLabel;


    @ApiModelProperty(value = "菜单图标")
    private String icon;


    @ApiModelProperty(value = "路由地址")
    private String pathUrl;

    @ApiModelProperty(value = "所属平台（1后台管理平台2普通供应商3履约管理）")
    private Integer classCode;


    @ApiModelProperty(value = "父级菜单id（1级为null）")
    private String parentMenuId;


    @ApiModelProperty(value = "菜单层级（0子系统 1：一级，2：二级，3：二级）")
    private Integer level;
}
