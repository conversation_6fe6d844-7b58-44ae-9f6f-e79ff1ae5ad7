package scrbg.meplat.mall.dto.mail;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.File;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-02-21 14:46
 */
@Data
public class ShopInfoUpdateDTO {

    @ApiModelProperty(value = "店铺id")
    private String shopId;

    @ApiModelProperty(value = "序列号")
    private String serialNum;

    @ApiModelProperty(value = "店铺名称")

    private String shopName;


    @ApiModelProperty(value = "店铺余额")

    private BigDecimal shopBalance;


    @ApiModelProperty(value = "店铺冻结的资金")

    private BigDecimal shopFreezeMoney;


    @ApiModelProperty(value = "店铺类型 0：个体户  1：企业  2：个人")

    private Integer shopType;


    @ApiModelProperty(value = "店铺log")

    private String shopImg;

    @ApiModelProperty(value = "广告图")

    private String adImg;


    @ApiModelProperty(value = "省")

    private String province;


    @ApiModelProperty(value = "市")

    private String city;


    @ApiModelProperty(value = "县、区")

    private String county;


    @ApiModelProperty(value = "详细地址")

    private String detailedAddress;


    @ApiModelProperty(value = "经度")

    private BigDecimal longitude;


    @ApiModelProperty(value = "纬度")

    private BigDecimal latitude;


    @ApiModelProperty(value = "店铺状态 1:启用 0停用")

    private Integer state;


    @ApiModelProperty(value = "店铺简介")

    private String shopDescrible;


    @ApiModelProperty(value = "企业id")

    private String enterpriseId;


    @ApiModelProperty(value = "主营业务")

    private String mainBusiness;


    @ApiModelProperty(value = "是否自营：1：是  0：否（默认：0）")

    private Integer isBusiness;


    @ApiModelProperty(value = "是否为供应商：1： 是  0：否")

    private Integer isSupplier;


    @ApiModelProperty(value = "是否内部店铺：1：是  0：否")

    private Integer isInternalShop;


    @ApiModelProperty(value = "是否支持内部结算：1：是  0：否")

    private Integer isInternalSettlement;


    @ApiModelProperty(value = "店铺审核状态： 1：审核通过  2：未审核  3：审核未通过 ")

    private Integer auditStatus;

    @ApiModelProperty(value = "开店日期：  ")
    private Date openDate;


    @ApiModelProperty(value = "联系人")

    private String linkMan;

    @ApiModelProperty(value = "联系电话")

    private String contactNumber;


    @ApiModelProperty(value = "首字母")

    private String initial;
    @ApiModelProperty(value = "退货地址")

    private String returnAddress;

    @ApiModelProperty(value = "营业执照")
    private String businessLicense;
    @ApiModelProperty(value = "营业执照id")
    private String businessLicenseId;
    /**
     * file
     */
    @ApiModelProperty(value = "广告图")
    private List<File> adminFile;

    @ApiModelProperty(value = "店铺小图")
    private List<File> minFile;
    @ApiModelProperty(value = "退货联系人姓名")
    private String returnRelationName;
    @ApiModelProperty(value = "退货联系人电话姓名")
    private String returnRelationNumber;

}
