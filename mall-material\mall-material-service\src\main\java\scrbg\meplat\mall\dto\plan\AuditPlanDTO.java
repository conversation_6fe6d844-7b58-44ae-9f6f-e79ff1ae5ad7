package scrbg.meplat.mall.dto.plan;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-06-30 16:15
 */
@Data
public class AuditPlanDTO {


    @ApiModelProperty(value = "计划id")
    private String planId;

    @ApiModelProperty(value = "是否通过（1是0否）")
    private Integer isOpen;

    @ApiModelProperty(value = "未通过原因")
    private String auditResult;

}
