package scrbg.meplat.mall.dto.process;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.vo.processConfig.ProcessConfigDtlItemVO;

/**
 * <AUTHOR>
 * @date: 2025年6月24日 下午5:27:04
 */
@Data
public class ProcessConfigDtlDTO {

    @ApiModelProperty(value = "流程ID")
    private String processId;

    @ApiModelProperty(value = "流程名称")
    private String processName;

    @ApiModelProperty(value = "系统名称")
    private String systemName;

    @ApiModelProperty(value = "系统编号")
    private String systemNo;

    @ApiModelProperty(value = "备注")
    private String remark;
    
    @ApiModelProperty(value = "商品列表信息")
    private List<ProcessConfigDtlItemVO> processConfigDtlItemVOs;


}
