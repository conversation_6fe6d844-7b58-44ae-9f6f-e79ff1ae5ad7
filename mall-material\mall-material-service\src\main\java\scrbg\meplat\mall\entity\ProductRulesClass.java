package scrbg.meplat.mall.entity;

import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mall.entity.parent.BaseEntity;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
/**
 * @描述：商品预警规则和分类关联表
 * @作者: ye
 * @日期: 2024-03-20
 */
@ApiModel(value="商品预警规则和分类关联表")
@Data
@TableName("product_rules_class")
public class ProductRulesClass extends MustBaseEntity implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private String ruleClassId;

    @ApiModelProperty(value = "规则id")

    private String warningRuleId;


    @ApiModelProperty(value = "分类id")

    private String classId;


    @ApiModelProperty(value = "分类名称")

    private String classPathName;


    @ApiModelProperty(value = "品牌名称")

    private String brandName;


    @ApiModelProperty(value = "物资id")

    private String materialId;


    @ApiModelProperty(value = "物资名称")

    private String materialName;


    @ApiModelProperty(value = "规格名称")

    private String skuName;







}