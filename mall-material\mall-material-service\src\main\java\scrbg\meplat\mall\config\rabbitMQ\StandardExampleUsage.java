package scrbg.meplat.mall.config.rabbitMQ;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import scrbg.meplat.mall.config.rabbitMQ.TTToDoRabbitMQUtil;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.ArrayList;

/**
 * 按照TT标准示例数据格式的使用方法
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
@Slf4j
@Service
public class StandardExampleUsage {

    @Resource
    private TTToDoRabbitMQUtil ttToDoRabbitMQUtil;

    /**
     * ========== 实时待办示例（按照标准格式） ==========
     */

    /**
     * 示例1：物资采购订单审核待办（支持不同产品类型）
     * @param orderId 订单ID
     * @param orderSn 订单编号
     * @param productType 产品类型：0-零星采购，1-大宗临购，2-周转材料
     */
    public void createMaterialOrderToDo(String orderId, String orderSn, Integer productType) {
        log.info("创建物资采购订单审核待办 - 订单ID: {}, 产品类型: {}", orderId, productType);

        ToDoMessageBody todo = new ToDoMessageBody();

        // 根据产品类型设置不同的参数
        switch (productType) {
            case 0: // 零星采购
                todo.setToDoId("LXCG" + orderId + "MA6CQYXG4H");
                todo.setModule("物资采购-零星采购");
                todo.setEmployeeNumber("036529");  // 指定账号
                todo.setTodoType("零星采购审核");
                todo.setTitle("零星采购订单审核");
                todo.setDescription("订单编号：" + orderSn + "，零星采购订单。请尽快审核。");
                todo.setWebUrl("https://wzgp.scrbg.com/retail-order/approve?id=" + orderId + "&type=approval");
                break;
            case 1: // 大宗临购
                todo.setToDoId("WZGP" + orderId + "MA6CQYXG4H");
                todo.setModule("物资采购-大宗临购审核");
                todo.setEmployeeNumber("036529");  // 指定账号
                todo.setTodoType("物资采购中心审核");
                todo.setTitle("大宗临购订单审核");
                todo.setDescription("订单编号：" + orderSn + "，大宗临购订单。请尽快审核。");
                todo.setWebUrl("https://wzgp.scrbg.com/bulk-order/approve?id=" + orderId + "&type=approval");
                break;
            case 2: // 周转材料
                todo.setToDoId("ZZCL" + orderId + "MA6CQYXG4H");
                todo.setModule("物资采购-周转材料");
                todo.setEmployeeNumber("036529");  // 指定账号
                todo.setTodoType("周转材料审核");
                todo.setTitle("周转材料订单审核");
                todo.setDescription("订单编号：" + orderSn + "，周转材料订单。请尽快审核。");
                todo.setWebUrl("https://wzgp.scrbg.com/turnover-material/approve?id=" + orderId + "&type=approval");
                break;
            default:
                throw new IllegalArgumentException("不支持的产品类型: " + productType);
        }

        // 通用字段设置
        todo.setOrginSystem("物资采购平台");
        todo.setUserId("391E2FB8-F295-4045-8CDC-340AD3DE6700");   // TT系统用户ID (指定账号)
        todo.setStatus(0);                                          // 0=待办
        todo.setLastupdateTime(getCurrentTime());                   // 标准时间格式
        todo.setAppId("wzgp.scrbg.com");                           // 应用ID
        todo.setUwpUrl("");                                        // 桌面端为空
        todo.setIosUrl("");                                        // iOS为空
        todo.setAndroidUrl("");                                    // Android为空

        // 发送实时待办
        ttToDoRabbitMQUtil.sendIncrementToDo(Arrays.asList(todo));

        log.info("物资采购订单审核待办推送成功 - 类型: {}", getProductTypeName(productType));
    }

    /**
     * ========== 实时已办示例（按照标准格式） ==========
     */

    /**
     * 示例1：订单审核完成（支持不同产品类型）
     * @param toDoId 待办ID
     * @param employeeNumber 员工号
     * @param userId 用户ID
     * @param result 审核结果
     * @param productType 产品类型：0-零星采购，1-大宗临购，2-周转材料
     */
    public void completeOrderApproval(String toDoId, String employeeNumber, String userId, String result, Integer productType) {
        log.info("完成订单审核 - 待办ID: {}, 结果: {}, 产品类型: {}", toDoId, result, productType);

        ToDoMessageBody doneTodo = new ToDoMessageBody();
        doneTodo.setToDoId(toDoId);                                 // 必须与待办时的ID完全一致
        doneTodo.setOrginSystem("物资采购平台");
        doneTodo.setEmployeeNumber(employeeNumber);                 // 必须与待办时一致
        doneTodo.setUserId(userId);                                 // 必须与待办时一致

        // 根据产品类型设置不同的参数
        switch (productType) {
            case 0: // 零星采购
                doneTodo.setModule("物资采购-零星采购");
                doneTodo.setTodoType("零星采购审核");
                doneTodo.setTitle("零星采购订单审核已完成");
                break;
            case 1: // 大宗临购
                doneTodo.setModule("物资采购-大宗临购");
                doneTodo.setTodoType("物资采购中心审核");
                doneTodo.setTitle("大宗临购订单审核已完成");
                break;
            case 2: // 周转材料
                doneTodo.setModule("物资采购-周转材料");
                doneTodo.setTodoType("周转材料审核");
                doneTodo.setTitle("周转材料订单审核已完成");
                break;
            default:
                throw new IllegalArgumentException("不支持的产品类型: " + productType);
        }

        doneTodo.setDescription("审核结果：" + result + "。审核已完成。");
        doneTodo.setStatus(1);                                      // 1=已完成
        doneTodo.setLastupdateTime(getCurrentTime());               // 更新为完成时间
        doneTodo.setAppId("wzgp.scrbg.com");
        doneTodo.setWebUrl("");                                     // 已完成可以为空
        doneTodo.setUwpUrl("");
        doneTodo.setIosUrl("");
        doneTodo.setAndroidUrl("");

        // 发送实时已办
        ttToDoRabbitMQUtil.sendIncrementDone(Arrays.asList(doneTodo));

        log.info("订单审核已办推送成功 - 类型: {}", getProductTypeName(productType));
    }

    /**
     * ========== 全量数据示例 ==========
     */

    /**
     * 全量同步待办（模拟完整示例数据格式）
     */
    public void fullSyncAllToDos() {
        log.info("全量同步所有待办数据");

        List<ToDoMessageBody> allToDos = new ArrayList<>();

        // 构建完整的示例数据格式
        // 待办1：项目经理入库审核（完全按照示例）
        ToDoMessageBody todo1 = new ToDoMessageBody();
        todo1.setToDoId("12310112MA6CQYXG4H");
        todo1.setOrginSystem("物资采购平台");
        todo1.setModule("物资采购-大宗临购");
        todo1.setEmployeeNumber("036529");  // 指定账号
        todo1.setUserId("391E2FB8-F295-4045-8CDC-340AD3DE6700");  // 指定账号用户ID
        todo1.setTodoType("物资采购中心审核");
        todo1.setTitle("大宗临购订单审核");
        todo1.setDescription("大宗临购订单，大额采购审核。请尽快审核。");
        todo1.setStatus(0);
        todo1.setLastupdateTime("2025-06-24T14:30:00");
        todo1.setAppId("wzgp.scrbg.com");
        todo1.setWebUrl("https://wzgp.scrbg.com/weburl/bulk-order-asdfak-23masd2-1");
        todo1.setUwpUrl("");
        todo1.setIosUrl("");
        todo1.setAndroidUrl("");
        allToDos.add(todo1);

        // 待办2：对账单审核（完全按照示例）
        ToDoMessageBody todo2 = new ToDoMessageBody();
        todo2.setToDoId("23310112MA6CQYXG4H");
        todo2.setOrginSystem("物资采购平台");
        todo2.setModule("物资采购-对账单审核");
        todo2.setEmployeeNumber("036529");  // 指定账号
        todo2.setUserId("391E2FB8-F295-4045-8CDC-340AD3DE6700");  // 指定账号用户ID
        todo2.setTodoType("财务审核");
        todo2.setTitle("供应商对账单审核");
        todo2.setDescription("供应商对账单，财务审核。请尽快审核。");
        todo2.setStatus(0);
        todo2.setLastupdateTime("2025-06-24T14:30:00");
        todo2.setAppId("wzgp.scrbg.com");
        todo2.setWebUrl("https://wzgp.scrbg.com/weburl/statement-23ka9434jn23-2");
        todo2.setUwpUrl("");
        todo2.setIosUrl("");
        todo2.setAndroidUrl("");
        allToDos.add(todo2);

        // 发送全量待办
        ttToDoRabbitMQUtil.sendFullToDo(allToDos);

        log.info("全量待办同步完成，数量: {}", allToDos.size());
    }

    /**
     * ========== 工具方法 ==========
     */

    /**
     * 获取标准格式时间
     */
    private String getCurrentTime() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"));
    }

    /**
     * 生成标准格式的待办ID
     */
    private String generateStandardToDoId(String prefix, String businessId) {
        // 模拟示例中的ID格式：12310112MA6CQYXG4H
        return prefix + businessId + "MA6CQYXG4H";
    }

    /**
     * 获取产品类型名称
     * @param productType 产品类型：0-零星采购，1-大宗临购，2-周转材料
     * @return 产品类型名称
     */
    public String getProductTypeName(Integer productType) {
        switch (productType) {
            case 0:
                return "零星采购";
            case 1:
                return "大宗临购";
            case 2:
                return "周转材料";
            default:
                return "未知类型";
        }
    }
}

/**
 * 实际业务调用示例
 */
@Service
@Slf4j
class BusinessCallExample {

    @Resource
    private StandardExampleUsage standardExampleUsage;

    /**
     * 实际业务场景1：订单提交审核（支持不同产品类型）
     * @param orderId 订单ID
     * @param orderSn 订单编号
     * @param productType 产品类型：0-零星采购，1-大宗临购，2-周转材料
     */
    public void submitOrderForApproval(String orderId, String orderSn, Integer productType) {
        try {
            log.info("开始处理订单审核流程 - 订单ID: {}, 产品类型: {}", orderId, productType);

            // 1. 订单业务逻辑
            updateOrderStatus(orderId, "PENDING_APPROVAL");

            // 2. 推送TT待办（按照标准格式）
            standardExampleUsage.createMaterialOrderToDo(orderId, orderSn, productType);

            log.info("订单审核流程启动成功 - 类型: {}", standardExampleUsage.getProductTypeName(productType));

        } catch (Exception e) {
            log.error("订单审核流程启动失败", e);
            throw e;
        }
    }

    /**
     * 实际业务场景2：审核完成（支持不同产品类型）
     * @param orderId 订单ID
     * @param approvalResult 审核结果
     * @param productType 产品类型：0-零星采购，1-大宗临购，2-周转材料
     */
    public void completeOrderApproval(String orderId, String approvalResult, Integer productType) {
        try {
            log.info("开始处理审核完成流程 - 订单ID: {}, 产品类型: {}", orderId, productType);

            // 1. 更新订单状态
            updateOrderStatus(orderId, "APPROVED");

            // 2. 根据产品类型构建待办ID和员工号
            String toDoIdPrefix;
            switch (productType) {
                case 0:
                    toDoIdPrefix = "LXCG"; // 零星采购
                    break;
                case 1:
                    toDoIdPrefix = "WZGP"; // 大宗临购
                    break;
                case 2:
                    toDoIdPrefix = "ZZCL"; // 周转材料
                    break;
                default:
                    throw new IllegalArgumentException("不支持的产品类型: " + productType);
            }
            String toDoId = toDoIdPrefix + orderId + "MA6CQYXG4H";
            String employeeNumber = getEmployeeNumberByProductType(productType);

            // 3. 推送TT已办
            standardExampleUsage.completeOrderApproval(
                    toDoId,
                    employeeNumber,
                    "391E2FB8-F295-4045-8CDC-340AD3DE6700",  // 指定账号用户ID
                    approvalResult,
                    productType
            );

            log.info("审核完成流程处理成功 - 类型: {}", standardExampleUsage.getProductTypeName(productType));

        } catch (Exception e) {
            log.error("审核完成流程处理失败", e);
            throw e;
        }
    }

    /**
     * 根据产品类型获取对应的员工号
     * @param productType 产品类型：0-零星采购，1-大宗临购，2-周转材料
     * @return 员工号
     */
    private String getEmployeeNumberByProductType(Integer productType) {
        switch (productType) {
            case 0:
                return "036529"; // 指定账号 - 零星采购审核人
            case 1:
                return "036529"; // 指定账号 - 大宗临购审核人
            case 2:
                return "036529"; // 指定账号 - 周转材料审核人
            default:
                throw new IllegalArgumentException("不支持的产品类型: " + productType);
        }
    }

    /**
     * 模拟更新订单状态
     */
    private void updateOrderStatus(String orderId, String status) {
        log.info("更新订单状态 - 订单ID: {}, 状态: {}", orderId, status);
        // 实际的数据库更新操作
    }

    /**
     * 验证消息是否符合TT标准格式
     */
    public boolean validateMessageFormat(String jsonMessage) {
        try {
            // 这里可以添加JSON格式验证逻辑
            // 验证必填字段、数据类型等

            log.debug("消息格式验证通过: {}", jsonMessage.substring(0, Math.min(100, jsonMessage.length())));
            return true;

        } catch (Exception e) {
            log.error("消息格式验证失败: {}", e.getMessage());
            return false;
        }
    }
}