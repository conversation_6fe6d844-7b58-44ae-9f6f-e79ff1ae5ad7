package scrbg.meplat.mall.entity;

import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
/**
 * @描述：商品分类
 * @作者: ye
 * @日期: 2023-09-27
 */
@ApiModel(value="商品分类")
@Data
@TableName("product_category1")
public class ProductCategory1 extends MustBaseEntity implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "分类id")
    private String classId;

    @ApiModelProperty(value = "分类名称")

    private String className;


    @ApiModelProperty(value = "分类关键字")

    private String classKeyword;


    @ApiModelProperty(value = "分类层级 1:一级大分类 2:二级分类 3:三级小分类")

    private Integer classLevel;


    @ApiModelProperty(value = "父层级id")

    private String parentId;


    @ApiModelProperty(value = "图标 logo")

    private String classIcon;


    @ApiModelProperty(value = "背景颜色")

    private String classBgColor;




    @ApiModelProperty(value = "分类的描述")

    private String classDescribe;












    @ApiModelProperty(value = "状态（1启用0停用）")

    private Integer state;


    @ApiModelProperty(value = "商品类型：0物资 1设备 2周材（物资） 3周材（设备） 4二手设备 5租赁设备 6维修服务")

    private Integer productType;






    @ApiModelProperty(value = "是否展示(1是，0否)")

    private Integer isExhibition;


    @ApiModelProperty(value = "是否有商品(1有，0没有)")

    private Integer isHaveProduct;


    @ApiModelProperty(value = "分类路径")

    private String classPath;


    @ApiModelProperty(value = "分类编号")

    private String classNo;



}