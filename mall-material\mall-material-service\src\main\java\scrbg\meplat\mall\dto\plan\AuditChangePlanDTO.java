package scrbg.meplat.mall.dto.plan;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023-06-30 16:15
 */
@Data
public class AuditChangePlanDTO {


    @ApiModelProperty(value = "变更计划id")
    private String planChangeId;

    @ApiModelProperty(value = "是否通过（1是0否）")
    private Integer isOpen;

    @ApiModelProperty(value = "未通过原因")
    private String auditResult;

}
