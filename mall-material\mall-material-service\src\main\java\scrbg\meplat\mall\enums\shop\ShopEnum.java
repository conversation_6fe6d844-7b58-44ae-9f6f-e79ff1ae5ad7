package scrbg.meplat.mall.enums.shop;

/**
 * @package: scrbg.meplat.mall.enums.user
 * @author: 胡原武
 * @date: 2022.11.08
 */
public enum ShopEnum {


    IS_BUSINESS_YES(1,"自营店铺"),
    IS_BUSINESS_NO(1,"非自营店铺"),

    IS_INTERNAL_SHOP_YES(1,"内部店铺"),
    IS_INTERNAL_SHOP_NO(0,"不是内部店铺"),


    IS_INTERNAL_SETTLEMENT_YES(1,"支持内部结算"),
    IS_INTERNAL_SETTLEMENT_NO(1,"支持内部结算"),

    IS_SUPPLIER_YES(1,"是供应商"),
    IS_SUPPLIER_NO(0,"不是供应商");

    /**
     * 编码
     */
    private int code;

    /**
     * 说明
     */
    private String remark;

    ShopEnum() {
    }

    ShopEnum(int code, String remark) {
        this.code = code;
        this.remark = remark;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

}