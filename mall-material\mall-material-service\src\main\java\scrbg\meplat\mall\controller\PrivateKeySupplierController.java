package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.entity.PrivateKeySupplier;
import scrbg.meplat.mall.service.PrivateKeySupplierService;

import java.util.List;

/**
 * @描述：供应商秘钥控制类
 * @作者: ye
 * @日期: 2023-03-27
 */
@RestController
@RequestMapping("/privateKeySupplier")
@Api(tags = "供应商秘钥")
public class PrivateKeySupplierController{

@Autowired
public PrivateKeySupplierService privateKeySupplierService;

@PostMapping("/listByEntity")
@ApiOperation(value = "根据实体属性分页查询")
@DynamicParameters(name = "根据实体属性分页查询", properties = {
        @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
        @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
})
public PageR<PrivateKeySupplier> listByEntity(@RequestBody JSONObject jsonObject){
        PageUtils page= privateKeySupplierService.queryPage(jsonObject,new LambdaQueryWrapper<PrivateKeySupplier>());
        return PageR.success(page);
        }

@GetMapping("/findById")
@ApiOperation(value = "根据主键查询")
@ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "ID", required = true,
                dataType = "String", paramType = "query")
})
public R<PrivateKeySupplier> findById(String id){
    PrivateKeySupplier privateKeySupplier = privateKeySupplierService.getById(id);
        return R.success(privateKeySupplier);
        }

@PostMapping("/create")
@ApiOperation(value = "新增")
public R save(@RequestBody PrivateKeySupplier privateKeySupplier){
    privateKeySupplierService.create(privateKeySupplier);
        return R.success();
        }

@PostMapping("/update")
@ApiOperation(value = "修改")
public R update(@RequestBody PrivateKeySupplier privateKeySupplier){
    privateKeySupplierService.update(privateKeySupplier);
        return R.success();
        }

@GetMapping("/delete")
@ApiOperation(value = "根据主键删除")
@ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "ID", required = true,
                dataType = "String", paramType = "query")
})
public R delete(String id){
    privateKeySupplierService.delete(id);
        return R.success();
        }


@PostMapping("/deleteBatch")
@ApiOperation(value = "根据主键批量删除")
public R deleteBatch(@RequestBody List<String> ids){
    privateKeySupplierService.removeByIds(ids);
        return R.success();
        }
        }

