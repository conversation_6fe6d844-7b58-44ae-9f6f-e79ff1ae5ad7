package scrbg.meplat.mall.entity;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@ApiModel(value="企业-业绩表")
@Data
@TableName("enterprise_performance")
public class EnterprisePerformance implements Serializable {

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "业绩id")
    private String enterprisePerformanceId;

    @ApiModelProperty(value = "所属企业ID")
    private String enterpriseId;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "供应物资品类")
    private String supplyCategory;

    @ApiModelProperty(value = "合同金额（万元）")
    private BigDecimal contractAmount;

    @ApiModelProperty(value = "供货起始时间")
    private Date supplyStartDate;

    @ApiModelProperty(value = "供货结束时间")
    private Date supplyEndDate;

    @ApiModelProperty(value = "业绩证明人")
    private String proofPerson;

    @ApiModelProperty(value = "证明人联系电话")
    private String proofPhone;

    @ApiModelProperty(value = "记录状态 1-有效 0-无效")
    private Integer status;

    @ApiModelProperty(value = "操作人ID")
    private Integer operatorId;

    @ApiModelProperty(value = "逻辑删除 -1: 删除 0:未删除")
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date gmtCreate;

    @ApiModelProperty(value = "修改时间")
    private Date gmtModified;

    @ApiModelProperty(value = "创建人Id")
    private String founderId;

    @ApiModelProperty(value = "创建人名称")
    private String founderName;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "修改人名称")
    private String modifyName;

    @ApiModelProperty(value = "修改人id")
    private String modifyId;

    @ApiModelProperty(value = "乐观锁")
    private Integer version;

    @TableField(exist = false)
    private List<Date> ghdate;

}
