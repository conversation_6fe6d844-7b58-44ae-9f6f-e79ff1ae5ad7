package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.config.auth.IsRole;
import scrbg.meplat.mall.config.auth.RoleEnum;
import scrbg.meplat.mall.config.logRecording.LogRecord;
import scrbg.meplat.mall.config.logRecording.enums.BusinessType;
import scrbg.meplat.mall.config.logRecording.enums.OperatorType;
import scrbg.meplat.mall.config.redis.redisson.NotResubmit;
import scrbg.meplat.mall.dto.AuditDTO;
import scrbg.meplat.mall.dto.order.MaterialReconciliationCancellationDTO;
import scrbg.meplat.mall.dto.outer.login.LoginUser;
import scrbg.meplat.mall.dto.reconciliation.MaterialReconciliationUpdateDTO;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.entity.plan.Plan;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.pcwp.PcwpService;
import scrbg.meplat.mall.service.*;
import scrbg.meplat.mall.service.plan.PlanService;
import scrbg.meplat.mall.util.LogUtil;
import scrbg.meplat.mall.util.RestTemplateUtils;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.baomidou.mybatisplus.core.toolkit.Wrappers.lambdaQuery;

/**
 * @描述：物资验收控制类
 * @作者: ye
 * @日期: 2023-07-26
 */
@Slf4j
@RestController
@RequestMapping("/")
@Api(tags = "物资验收")
public class MaterialReconciliationController {

    @Autowired
    public MaterialReconciliationService materialReconciliationService;

    @Autowired
    public OrdersService ordersService;

    @Autowired
    public OrderItemService orderItemService;

    @Autowired
    public InterfaceLogsService interfaceLogsService;

    @Autowired
    RestTemplateUtils restTemplateUtils;

    @Autowired
    MallConfig mallConfig;

    @Autowired
    PcwpService pcwpService;

    @Autowired
    PlanService planService;

    @Autowired
    MaterialReconciliationDtlService materialReconciliationDtlService;

    // 回滚暂扣数量
    private static final String ROLL_BACK_QUANTITY_DRAFT_URL = "/thirdapi/siteReceiving/rollBackWriteBackBillLockQuantiy?keyId=";

    // 回滚已审核数量
    private static final String ROLL_BACK_LOCK_QUANTITY_DRAFT_URL = "/thirdapi/siteReceiving/rollBackWriteBackBillQuantiy?keyId=";

    // 回滚保存物资验收
    private static final String ROLL_BACK_SAVE_ACCEPTANCE = "/thirdapi/acceptance/rollBackSaveAcceptance?keyId=";

    @GetMapping("materialReconciliation/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "ID", required = true,
            dataType = "String", paramType = "query")
    })
    public R<MaterialReconciliation> findById(String id) {
        MaterialReconciliation materialReconciliation = materialReconciliationService.getById(id);
        return R.success(materialReconciliation);
    }

    @PostMapping("materialReconciliation/update")
    @ApiOperation(value = "修改")
    @LogRecord(title = "对账单管理", businessType = BusinessType.UPDATE, operatorType = OperatorType.MANAGE)
    public R update(@RequestBody MaterialReconciliation materialReconciliation) {
        materialReconciliationService.update(materialReconciliation);
        return R.success();
    }

    @GetMapping("materialReconciliation/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "ID", required = true,
            dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        materialReconciliationService.delete(id);
        return R.success();
    }

    @PostMapping("materialReconciliation/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")
    public R deleteBatch(@RequestBody List<String> ids) {
        materialReconciliationService.removeByIds(ids);
        return R.success();
    }

    @PostMapping("performanceManage/materialReconciliation/listByEntity")
    @ApiOperation(value = "查询对账单列表（采购员）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "type", value = "对账类型（1浮动价格对账单2固定价格对账单）", dataTypeClass = Integer.class),
            @DynamicParameter(name = "businessType", value = "业务类型（1合同2计划3调拨4甲供5暂估）", dataTypeClass = Integer.class),
            @DynamicParameter(name = "createType", value = "新增来源（1采购员新增2供应商新增3pcwp新增）", dataTypeClass = Integer.class),
            @DynamicParameter(name = "state", value = "状态（0草稿1待提交2待审核3审核通过4审核失败7作废）", dataTypeClass = Integer.class),
            @DynamicParameter(name = "orderBy", value = "排序字段(0:创建时间:1开始时间2结束时间", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
            @DynamicParameter(name = "reconciliationNo", value = "对账单编号", dataTypeClass = String.class),
            @DynamicParameter(name = "sourceBillNo", value = "源单编号", dataTypeClass = String.class),
            @DynamicParameter(name = "orderSn", value = "订单号", dataTypeClass = String.class),
            @DynamicParameter(name = "title", value = "标题", dataTypeClass = String.class),
            @DynamicParameter(name = "supplierName", value = "供应商名称", dataTypeClass = String.class),
            @DynamicParameter(name = "purchasingOrgName", value = "采购员名称", dataTypeClass = String.class),
    })
    public PageR<MaterialReconciliation> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = materialReconciliationService.queryPage(jsonObject, new LambdaQueryWrapper<MaterialReconciliation>());
        return PageR.success(page);
    }

    @PostMapping("performanceManage/materialReconciliation/synthesizeTemporaryListByEntity")
    @ApiOperation(value = "查询对大宗临购对账单列表（采购员）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "type", value = "对账类型（1浮动价格对账单2固定价格对账单）", dataTypeClass = Integer.class),
            @DynamicParameter(name = "businessType", value = "业务类型（1合同2计划3调拨4甲供5暂估6大宗临购）", dataTypeClass = Integer.class),
            @DynamicParameter(name = "createType", value = "新增来源（1采购员新增2供应商新增3pcwp新增）", dataTypeClass = Integer.class),
            @DynamicParameter(name = "state", value = "状态（0草稿1待提交2待审核3审核通过4审核失败7作废）", dataTypeClass = Integer.class),
            @DynamicParameter(name = "orderBy", value = "排序字段(0:创建时间:1开始时间2结束时间", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
            @DynamicParameter(name = "reconciliationNo", value = "对账单编号", dataTypeClass = String.class),
            @DynamicParameter(name = "sourceBillNo", value = "源单编号", dataTypeClass = String.class),
            @DynamicParameter(name = "orderSn", value = "订单号", dataTypeClass = String.class),
            @DynamicParameter(name = "title", value = "标题", dataTypeClass = String.class),
            @DynamicParameter(name = "supplierName", value = "供应商名称", dataTypeClass = String.class),
            @DynamicParameter(name = "purchasingOrgName", value = "采购员名称", dataTypeClass = String.class),
    })
    public PageR<MaterialReconciliation> synthesizeTemporaryListByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = materialReconciliationService.queryPage(jsonObject, new LambdaQueryWrapper<MaterialReconciliation>());
        return PageR.success(page);
    }

    @PostMapping("performanceManage/materialReconciliation/supplierSTListByEntity")
    @ApiOperation(value = "查询大宗临购对账单列表（供应商）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "type", value = "对账类型（1浮动价格对账单2固定价格对账单）", dataTypeClass = Integer.class),
            @DynamicParameter(name = "businessType", value = "业务类型（1合同2计划3调拨4甲供5暂估）", dataTypeClass = Integer.class),
            @DynamicParameter(name = "createType", value = "新增来源（1采购员新增2供应商新增3pcwp新增）", dataTypeClass = Integer.class),
            @DynamicParameter(name = "state", value = "状态（0草稿1待提交2待审核3审核通过4审核失败7作废）", dataTypeClass = Integer.class),
            @DynamicParameter(name = "orderBy", value = "排序字段(0:创建时间:1开始时间2结束时间", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
            @DynamicParameter(name = "reconciliationNo", value = "对账单编号", dataTypeClass = String.class),
            @DynamicParameter(name = "sourceBillNo", value = "源单编号", dataTypeClass = String.class),
            @DynamicParameter(name = "orderSn", value = "订单号", dataTypeClass = String.class),
            @DynamicParameter(name = "title", value = "标题", dataTypeClass = String.class),
            @DynamicParameter(name = "supplierName", value = "供应商名称", dataTypeClass = String.class),
            @DynamicParameter(name = "purchasingOrgName", value = "采购员名称", dataTypeClass = String.class),
    })
    public PageR<MaterialReconciliation> supplierSTListByEntity(@RequestBody JSONObject jsonObject) {
        jsonObject.put("businessType", 6);
        PageUtils page = materialReconciliationService.supplierListByEntity(jsonObject, new LambdaQueryWrapper<MaterialReconciliation>());
        return PageR.success(page);
    }

    @PostMapping("performanceManage/materialReconciliation/supplierListByEntity")
    @ApiOperation(value = "查询对账单列表（供应商）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "type", value = "对账类型（1浮动价格对账单2固定价格对账单）", dataTypeClass = Integer.class),
            @DynamicParameter(name = "businessType", value = "业务类型（1合同2计划3调拨4甲供5暂估）", dataTypeClass = Integer.class),
            @DynamicParameter(name = "createType", value = "新增来源（1采购员新增2供应商新增3pcwp新增）", dataTypeClass = Integer.class),
            @DynamicParameter(name = "state", value = "状态（0草稿1待提交2待审核3审核通过4审核失败7作废）", dataTypeClass = Integer.class),
            @DynamicParameter(name = "orderBy", value = "排序字段(0:创建时间:1开始时间2结束时间", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
            @DynamicParameter(name = "reconciliationNo", value = "对账单编号", dataTypeClass = String.class),
            @DynamicParameter(name = "sourceBillNo", value = "源单编号", dataTypeClass = String.class),
            @DynamicParameter(name = "orderSn", value = "订单号", dataTypeClass = String.class),
            @DynamicParameter(name = "title", value = "标题", dataTypeClass = String.class),
            @DynamicParameter(name = "supplierName", value = "供应商名称", dataTypeClass = String.class),
            @DynamicParameter(name = "purchasingOrgName", value = "采购员名称", dataTypeClass = String.class),
    })
    public PageR<MaterialReconciliation> supplierListByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = materialReconciliationService.supplierListByEntity(jsonObject, new LambdaQueryWrapper<MaterialReconciliation>());
        return PageR.success(page);
    }

    @GetMapping("performanceManage/materialReconciliation/outputExcel")
    @ApiOperation(value = "导出账单（采购员）")
    public R outputExcel(String reconciliationId, HttpServletResponse response) {
        materialReconciliationService.outputExcel(reconciliationId, response);
        return R.success();
    }

    @GetMapping("performanceManage/materialReconciliationGetBySn")
    @ApiOperation(value = "根据编号获取明细")
    public R<MaterialReconciliation> materialReconciliationGetBySn(String sn) {
        MaterialReconciliation vo = materialReconciliationService.materialReconciliationGetBySn(sn);
        return R.success(vo);
    }

    @PostMapping("performanceManage/materialReconciliationSubmitCheck")
    @ApiOperation(value = "批量提交审核")
    @IsRole(roleName = RoleEnum.ROLE_10)
    @LogRecord(title = "对账单管理", businessType = BusinessType.UPDATE, operatorType = OperatorType.MANAGE)
    public R materialReconciliationSubmitCheck(@RequestBody List<String> ids) {
        materialReconciliationService.materialReconciliationSubmitCheck(ids);
        return R.success();
    }

    @PostMapping("performanceManage/materialReconciliationSubmit")
    @ApiOperation(value = "批量提交")
    @LogRecord(title = "对账单管理", businessType = BusinessType.UPDATE, operatorType = OperatorType.MANAGE)

    public R materialReconciliationSubmit(@RequestBody List<String> ids) {
        materialReconciliationService.materialReconciliationSubmit(ids);
        return R.success();
    }

    @GetMapping("performanceManage/materialReconciliationAffirm")
    @ApiOperation(value = "采购员确认单据")
    @LogRecord(title = "对账单管理", businessType = BusinessType.UPDATE, operatorType = OperatorType.MANAGE)
    public R materialReconciliationAffirm(String reconciliationId) {
        materialReconciliationService.materialReconciliationAffirm(reconciliationId);
        return R.success();
    }

    @GetMapping("performanceManage/materialReconciliationSupplierAffirm")
    @ApiOperation(value = "供应商确认单据")
    @LogRecord(title = "对账单管理", businessType = BusinessType.UPDATE, operatorType = OperatorType.MANAGE)
    public R materialReconciliationSupplierAffirm(String reconciliationId) {
        materialReconciliationService.materialReconciliationSupplierAffirm(reconciliationId);
        return R.success();
    }

    @GetMapping("materialReconciliation/getWzcsOrderById")
    @ApiOperation(value = "根据订单id获取订单信息(没有推送PCWP的计划，查询物资商城对账单信息)")
    public R getWzcsOrderById(String orderId) {
        if (StringUtils.isBlank(orderId)) {
            throw new BusinessException("订单id不能为空！");
        }
        // 选择了计划
        Orders one = ordersService.lambdaQuery().eq(Orders::getOrderId, orderId).one();
        return R.success(one);
    }

    @GetMapping("materialReconciliation/getOrderById")
    @ApiOperation(value = "根据订单id获取订单信息")
    public R getOrderById(String orderId) {
        if (StringUtils.isBlank(orderId)) {
            throw new BusinessException("订单id不能为空！");
        }
        // 选择了计划
        Orders one = ordersService.lambdaQuery().eq(Orders::getOrderId, orderId).one();
        return R.success(one);
    }

    @PostMapping("materialReconciliation/getOrderItem4Price")
    @ApiOperation(value = "根据pcwp返回的可对账物资信息获取订单明细4种价格")
    public R getOrderItem4Price(@RequestBody List<Map> maps) {
        if (CollectionUtils.isEmpty(maps)) {
            throw new BusinessException("对账明细不存在！");
        }
        for (Map map : maps) {
            String orderItemId = (String) map.get("orderDtlId");
            if (StringUtils.isBlank(orderItemId)) {
                throw new BusinessException("订单明细不存在！");
            }
            OrderItem byId = orderItemService.getById(orderItemId);
            if (byId == null) {
                throw new BusinessException("订单明细不存在！");
            }
            map.put("freightPrice", byId.getNetPrice());
            if (byId.getFixationPrice() == null) {
                map.put("fixationPrice", new BigDecimal(0));
            } else {
                map.put("fixationPrice", byId.getFixationPrice());
            }
            map.put("outFactoryPrice", byId.getOutFactoryPrice());
            if (byId.getTransportPrice() == null) {
                map.put("transportPrice", new BigDecimal(0));
            } else {
                map.put("transportPrice", byId.getTransportPrice());
            }
        }
        return R.success(maps);
    }

    @GetMapping("performanceManage/materialReconciliation/outputSTExcel")
    @ApiOperation(value = "导出大宗临购对账账单")
    public R outputSTExcel(String reconciliationId, HttpServletResponse response) {
        materialReconciliationService.outputSTExcel(reconciliationId, response);
        return R.success();
    }

    @PostMapping("performanceManage/materialReconciliationSTSubmitCheck")
    @ApiOperation(value = "批量提交大宗临购审核")
    @IsRole(roleName = RoleEnum.ROLE_10)
    @LogRecord(title = "对账单管理", businessType = BusinessType.UPDATE, operatorType = OperatorType.MANAGE)
    public R materialReconciliationSTSubmitCheck(@RequestBody List<String> ids) {
        materialReconciliationService.materialReconciliationSTSubmitCheck(ids);
        return R.success();
    }

    @PostMapping("performanceManage/materialReconciliationSTCreate")
    @ApiOperation(value = "大宗临购新增对账单")
    @NotResubmit
    @LogRecord(title = "对账单管理", businessType = BusinessType.INSERT, operatorType = OperatorType.MANAGE)
    public R materialReconciliationSTCreate(@RequestBody MaterialReconciliation dto) {
        String idStr = IdWorker.getIdStr();
        dto.setKeyId(idStr);
        StringBuilder farArg = new StringBuilder();
        String no = null; // 返回的编号，主要保存自动跳转页面使用

        try {
            no = materialReconciliationService.materialReconciliationSTCreate(dto, farArg);
        } catch (Exception e) {
            LogUtil.writeErrorLog2(idStr, "materialReconciliationSTCreate", dto, farArg.toString(), null, e.getMessage(), MaterialReconciliationController.class);
            createAndSaveInterfaceLog(idStr, "materialReconciliationSTCreate", dto, farArg.toString(), e.getMessage());
            // TODO 回滚接口是否一样
            restTemplateUtils.getPCWP2NotParams(mallConfig.prodPcwp2Url02 + ROLL_BACK_QUANTITY_DRAFT_URL + idStr);
            throw new BusinessException(e.getMessage());
        }
        return R.success(no);
    }

    @PostMapping("performanceManage/materialReconciliationSTSupplierCreate")
    @ApiOperation(value = "供应商新增大宗临购对账")
    @NotResubmit
    @LogRecord(title = "对账单管理", businessType = BusinessType.INSERT, operatorType = OperatorType.MANAGE)
    public R materialReconciliationSTSupplierCreate(@RequestBody MaterialReconciliation dto) {

        int isNotPush = checkPlanPushToPCWPStatus(dto.getSourceBillNo());
        String idStr = IdWorker.getIdStr();
        dto.setKeyId(idStr);
        StringBuilder farArg = new StringBuilder();
        String sn = null;
        try {
            sn = materialReconciliationService.materialReconciliationSTSupplierCreate(dto, farArg, isNotPush);
        } catch (Exception e) {
            LogUtil.writeErrorLog2(idStr, "materialReconciliationSTSupplierCreate", dto, farArg.toString(), null, e.getMessage(), MaterialReconciliationController.class);
            createAndSaveInterfaceLog(idStr, "materialReconciliationSTSupplierCreate", dto, farArg.toString(), e.getMessage());
            if (isNotPush == 1) { //计划是否推送PCWP
                // 回滚反写对账单暂扣数量
                pcwpService.rollBackWriteBackBillLockQuantiy(idStr);
            }
//            restTemplateUtils.getPCWP2NotParams(mallConfig.prodPcwp2Url02 + ROLL_BACK_QUANTITY_DRAFT_URL + idStr);
            throw new BusinessException(e.getMessage());
        }
        return R.success(sn);
    }

    @PostMapping("performanceManage/materialReconciliationSTUpdate")
    @ApiOperation(value = "大宗临购修改对账（包括提交）")
    @NotResubmit
    @LogRecord(title = "对账单管理", businessType = BusinessType.UPDATE, operatorType = OperatorType.MANAGE)
    public R materialReconciliationSTUpdate(@RequestBody MaterialReconciliationUpdateDTO dto) {
        UserLogin currentUser = ThreadLocalUtil.getCurrentUser();
        String idStr = IdWorker.getIdStr();
        dto.setKeyId(idStr);
        try {
            materialReconciliationService.materialReconciliationSTUpdate(dto);
        } catch (Exception e) {
            LogUtil.writeErrorLog(idStr, "materialReconciliationSTUpdate", dto, null, null, e.getMessage(), MaterialReconciliationController.class);
            createAndSaveInterfaceLog(idStr, "materialReconciliationSTUpdate", dto, null, e.getMessage());
            if (currentUser.getIsInterior() == 0) { //内部用户
//                restTemplateUtils.getPCWP2NotParams(mallConfig.prodPcwp2Url02 + ROLL_BACK_QUANTITY_DRAFT_URL + idStr);
                pcwpService.rollBackWriteBackBillLockQuantiy(idStr);
            }
            throw new BusinessException(e.getMessage());
        }
        return R.success();
    }

    @PostMapping("performanceManage/materialReconciliationSTSupplierUpdate")
    @ApiOperation(value = "修改大宗临购对账单（供应商）")
    @NotResubmit
    @LogRecord(title = "对账单管理", businessType = BusinessType.UPDATE, operatorType = OperatorType.MANAGE)
    public R materialReconciliationSTSupplierUpdate(@RequestBody MaterialReconciliationUpdateDTO dto) {

        MaterialReconciliation reconciliation = materialReconciliationService.lambdaQuery()
                .eq(MaterialReconciliation::getReconciliationId, dto.getReconciliationId())
                .select(MaterialReconciliation::getReconciliationId, MaterialReconciliation::getIsNotPush).one();
        int isNotPush = reconciliation.getIsNotPush() == null ? 0 : reconciliation.getIsNotPush();//计划推送PCWP=1，未推送PCWP=0
        String idStr = IdWorker.getIdStr();
        dto.setKeyId(idStr);
        try {
            materialReconciliationService.materialReconciliationSTSupplierUpdate(dto,isNotPush);
        } catch (Exception e) {
            LogUtil.writeErrorLog(idStr, "materialReconciliationSTSupplierUpdate", dto, null, null, e.getMessage(), MaterialReconciliationController.class);
            createAndSaveInterfaceLog(idStr, "materialReconciliationSTSupplierUpdate", dto, null, e.getMessage());
            if(isNotPush ==1) {
                pcwpService.rollBackWriteBackBillLockQuantiy(idStr);
//                restTemplateUtils.getPCWP2NotParams(mallConfig.prodPcwp2Url02 + ROLL_BACK_QUANTITY_DRAFT_URL + idStr);
            }
            throw new BusinessException(e.getMessage());
        }
        return R.success();
    }

    @GetMapping("performanceManage/materialReconciliationSTDelete")
    @ApiOperation(value = "删除大宗临购")
    @NotResubmit
    @LogRecord(title = "对账单管理", businessType = BusinessType.DELETE, operatorType = OperatorType.MANAGE)
    public R materialReconciliationSTDelete(String reconciliationId) {
        String idStr = IdWorker.getIdStr();
        MaterialReconciliation reconciliation = materialReconciliationService.lambdaQuery()
                .eq(MaterialReconciliation::getReconciliationId, reconciliationId)
                .select(MaterialReconciliation::getReconciliationId, MaterialReconciliation::getIsNotPush).one();
        int isNotPush = reconciliation.getIsNotPush() == null ? 0 : reconciliation.getIsNotPush();//计划推送PCWP=1，未推送PCWP=0
        try {
            materialReconciliationService.materialReconciliationSTDelete(reconciliationId, idStr);
        } catch (Exception e) {
            LogUtil.writeErrorLog(idStr, "materialReconciliationSTDelete", reconciliationId, null, null, e.getMessage(), MaterialReconciliationController.class);
            createAndSaveInterfaceLog(idStr, "materialReconciliationSTDelete", reconciliationId, null, e.getMessage());
            if(isNotPush ==1) {
                pcwpService.rollBackWriteBackBillLockQuantiy(idStr);
            }
//            restTemplateUtils.getPCWP2NotParams(mallConfig.prodPcwp2Url02 + ROLL_BACK_QUANTITY_DRAFT_URL + idStr);
            throw new BusinessException(e.getMessage());
        }
        return R.success();
    }

    @PostMapping("performanceManage/materialReconciliationSTCancellation")
    @ApiOperation(value = "大宗临购作废单据")
    @NotResubmit
    @LogRecord(title = "对账单管理", businessType = BusinessType.DELETE, operatorType = OperatorType.MANAGE)
    public R materialReconciliationSTCancellation(@RequestBody MaterialReconciliationCancellationDTO dto) {
        String keyId = IdWorker.getIdStr();
        try {
            materialReconciliationService.materialReconciliationSTCancellation(dto.getReconciliationId(), dto.getResult(), keyId);
        } catch (Exception e) {
            LogUtil.writeErrorLog(keyId, "materialReconciliationSTCancellation", dto, null, null, e.getMessage(), MaterialReconciliationController.class);
            createAndSaveInterfaceLog(keyId, "materialReconciliationSTCancellation", dto, null, e.getMessage());
            // 回滚已审核数量
            restTemplateUtils.getPCWP2NotParams(mallConfig.prodPcwp2Url02 + ROLL_BACK_LOCK_QUANTITY_DRAFT_URL + keyId);
            throw new BusinessException(e.getMessage());
        }
        return R.success();
    }

    @PostMapping("performanceManage/materialReconciliationSTAuditPlan")
    @ApiOperation(value = "审核大宗临购对账单")
    @NotResubmit
    @IsRole(roleName = RoleEnum.ROLE_8)
    @LogRecord(title = "对账单管理", businessType = BusinessType.UPDATE, operatorType = OperatorType.MANAGE)
    public R materialReconciliationSTAuditPlan(@RequestBody AuditDTO dto) {
        String keyId = IdWorker.getIdStr();
        Integer isNotPush = null;
        StringBuilder farArg = new StringBuilder();
        try {
            isNotPush = materialReconciliationService.materialReconciliationSTAuditPlan(dto, keyId, farArg);
        } catch (Exception e) {
            LogUtil.writeErrorLog2(keyId, "materialReconciliationSTAuditPlan", dto, farArg.toString(), null, e.getMessage(), MaterialReconciliationController.class);
            createAndSaveInterfaceLog(keyId, "materialReconciliationSTAuditPlan", dto, farArg.toString(), e.getMessage());
            if(isNotPush ==1) {
                // 回滚保存
                pcwpService.rollBackSaveAcceptance(keyId);
                // 回滚已审核数量
                pcwpService.rollBackWriteBackBillQuantiy(keyId);
            }
            throw new BusinessException(e.getMessage());
        }
        return R.success(isNotPush);
    }

    @GetMapping("performanceManage/materialReconciliationSTPushAcceptance")
    @ApiOperation(value = "大宗临购主动推送对账单")
    @NotResubmit
    @LogRecord(title = "对账单管理", businessType = BusinessType.UPDATE, operatorType = OperatorType.MANAGE)
    public R materialReconciliationSTPushAcceptance(String reconciliationId) {
        materialReconciliationService.materialReconciliationSTPushAcceptance(reconciliationId);
        return R.success();
    }

    @PostMapping("performanceManage/materialReconciliationCreate")
    @ApiOperation(value = "采购方-新增对账单")
    @NotResubmit
    @LogRecord(title = "对账单管理", businessType = BusinessType.INSERT, operatorType = OperatorType.MANAGE)
    public R save(@RequestBody MaterialReconciliation dto) {
        String idStr = IdWorker.getIdStr();
        String reconciliationNo = "";
        dto.setKeyId(idStr);
        int isNotPush = checkOrderPushToPCWPStatus(dto.getDtl().get(0).getOrderSn());
        try {
            reconciliationNo = materialReconciliationService.create(dto,isNotPush);
        } catch (Exception e) {
            LogUtil.writeErrorLog(idStr, "create", dto, null, null, e.getMessage(), MaterialReconciliationController.class);
            if(isNotPush == 1) {
                createAndSaveInterfaceLog(idStr, "create", dto, null, e.getMessage());
                restTemplateUtils.getPCWP2NotParams(mallConfig.prodPcwp2Url02 + ROLL_BACK_QUANTITY_DRAFT_URL + idStr);
            }
            throw new BusinessException(e.getMessage());
        }
        return R.success(reconciliationNo);
    }

    @PostMapping("performanceManage/materialReconciliationSupplierCreate")
    @ApiOperation(value = "供应商方-新增对账")
    @NotResubmit
    @LogRecord(title = "对账单管理", businessType = BusinessType.INSERT, operatorType = OperatorType.MANAGE)
    public R materialReconciliationSupplierCreate(@RequestBody MaterialReconciliation dto) {
        StringBuilder result = new StringBuilder();
        String idStr = IdWorker.getIdStr();
        dto.setKeyId(idStr);
        String sn = null;
        //判断订单是否已推送到PCWP
        int isNotPush = checkOrderPushToPCWPStatus(dto.getDtl().get(0).getOrderSn());
        try {
            sn = materialReconciliationService.materialReconciliationSupplierCreate(dto, result,isNotPush);
        } catch (Exception e) {
            LogUtil.writeErrorLog(idStr, "materialReconciliationSupplierCreate", dto, null, null, e.getMessage(), MaterialReconciliationController.class);
            if(isNotPush ==1) {
                createAndSaveInterfaceLog(idStr, "materialReconciliationSupplierCreate", dto, null, e.getMessage());
//                restTemplateUtils.getPCWP2NotParams(mallConfig.prodPcwp2Url02 + ROLL_BACK_QUANTITY_DRAFT_URL + idStr);
                pcwpService.rollBackWriteBackBillLockQuantiy(idStr);
            }
            throw new BusinessException(e.getMessage());
        }
        return R.success(sn);
    }

    @PostMapping("performanceManage/materialReconciliationUpdate")
    @ApiOperation(value = "修改对账")
    @NotResubmit
//    @IsRole(roleName = RoleEnum.ROLE_10)
    @LogRecord(title = "对账单管理", businessType = BusinessType.UPDATE, operatorType = OperatorType.MANAGE)
    public R materialReconciliationUpdate(@RequestBody MaterialReconciliationUpdateDTO dto) {
        String idStr = IdWorker.getIdStr();
        dto.setKeyId(idStr);
        int isNotPush = checkOrderPushToPCWPStatus(dto.getDtl().get(0).getOrderSn());
        try {
            materialReconciliationService.materialReconciliationUpdate(dto,isNotPush);
        } catch (Exception e) {
            LogUtil.writeErrorLog(idStr, "materialReconciliationUpdate", dto, null, null, e.getMessage(), MaterialReconciliationController.class);
            if(isNotPush ==1) {
                createAndSaveInterfaceLog(idStr, "materialReconciliationUpdate", dto, null, e.getMessage());
                pcwpService.rollBackWriteBackBillLockQuantiy(idStr);
            }
//            restTemplateUtils.getPCWP2NotParams(mallConfig.prodPcwp2Url02 + ROLL_BACK_QUANTITY_DRAFT_URL + idStr);
            throw new BusinessException(e.getMessage());
        }
        return R.success();
    }

    @PostMapping("performanceManage/materialReconciliationSupplierUpdate")
    @ApiOperation(value = "修改对账供应商")
    @NotResubmit
    @LogRecord(title = "对账单管理", businessType = BusinessType.UPDATE, operatorType = OperatorType.MANAGE)
    public R materialReconciliationSupplierUpdate(@RequestBody MaterialReconciliationUpdateDTO dto) {
        String idStr = IdWorker.getIdStr();
        dto.setKeyId(idStr);
        StringBuilder fag = new StringBuilder();
        int isNotPush = checkOrderPushToPCWPStatus(dto.getDtl().get(0).getOrderSn());
        try {
            materialReconciliationService.materialReconciliationSupplierUpdate(dto, fag,isNotPush);
        } catch (Exception e) {
            LogUtil.writeErrorLog(idStr, "materialReconciliationSupplierUpdate", dto, null, null, e.getMessage(), MaterialReconciliationController.class);
            createAndSaveInterfaceLog(idStr, "materialReconciliationSupplierUpdate", dto, fag.toString(), e.getMessage());
            restTemplateUtils.getPCWP2NotParams(mallConfig.prodPcwp2Url02 + ROLL_BACK_QUANTITY_DRAFT_URL + idStr);
            throw new BusinessException(e.getMessage());
        }
        return R.success();
    }

    @GetMapping("performanceManage/materialReconciliationDelete")
    @ApiOperation(value = "删除对账")
    @NotResubmit
    @LogRecord(title = "对账单管理", businessType = BusinessType.DELETE, operatorType = OperatorType.MANAGE)
    public R materialReconciliationDelete(String reconciliationId) {
        String idStr = IdWorker.getIdStr();
        int isNotPush = checkReconciliationPushToPCWPStatus(reconciliationId);
        try {
            materialReconciliationService.materialReconciliationDelete(reconciliationId, idStr,isNotPush);
        } catch (Exception e) {
            LogUtil.writeErrorLog(idStr, "materialReconciliationDelete", reconciliationId, null, null, e.getMessage(), MaterialReconciliationController.class);
            if(isNotPush ==1 ) {
                createAndSaveInterfaceLog(idStr, "materialReconciliationDelete", reconciliationId, null, e.getMessage());
                restTemplateUtils.getPCWP2NotParams(mallConfig.prodPcwp2Url02 + ROLL_BACK_QUANTITY_DRAFT_URL + idStr);
            }
            throw new BusinessException(e.getMessage());
        }
        return R.success();
    }

    @PostMapping("performanceManage/materialReconciliationCancellation")
    @ApiOperation(value = "作废单据")
    @NotResubmit
    @LogRecord(title = "对账单管理", businessType = BusinessType.DELETE, operatorType = OperatorType.MANAGE)
    public R materialReconciliationCancellation(@RequestBody MaterialReconciliationCancellationDTO dto) {
        String keyId = IdWorker.getIdStr();
        int isNotPush = checkReconciliationPushToPCWPStatus(dto.getReconciliationId());
        try {
            materialReconciliationService.materialReconciliationCancellation(dto.getReconciliationId(), dto.getResult(), keyId,isNotPush);
        } catch (Exception e) {
            LogUtil.writeErrorLog(keyId, "materialReconciliationCancellation", dto, null, null, e.getMessage(), MaterialReconciliationController.class);
            if(isNotPush ==1) {
                createAndSaveInterfaceLog(keyId, "materialReconciliationCancellation", dto, null, e.getMessage());
                // 回滚已审核数量
                restTemplateUtils.getPCWP2NotParams(mallConfig.prodPcwp2Url02 + ROLL_BACK_LOCK_QUANTITY_DRAFT_URL + keyId);
            }
            throw new BusinessException(e.getMessage());
        }
        return R.success();
    }

    @PostMapping("performanceManage/materialReconciliationAuditPlan")
    @ApiOperation(value = "审核对账单")
    @NotResubmit
//    @IsRole(roleName = RoleEnum.ROLE_8)
    @LogRecord(title = "对账单管理", businessType = BusinessType.UPDATE, operatorType = OperatorType.MANAGE)
    public R materialReconciliationAuditPlan(@RequestBody AuditDTO dto) {
        int isNotPush = checkReconciliationPushToPCWPStatus(dto.getId());
        String keyId = IdWorker.getIdStr();
        StringBuilder farArg = new StringBuilder();
        try {
            materialReconciliationService.materialReconciliationAuditPlan(dto, keyId, farArg,isNotPush);
        } catch (Exception e) {
            LogUtil.writeErrorLog2(keyId, "materialReconciliationAuditPlan", dto, farArg.toString(), null, e.getMessage(), MaterialReconciliationController.class);
            if(isNotPush == 1) {
                createAndSaveInterfaceLog(keyId, "materialReconciliationAuditPlan", dto, farArg.toString(), e.getMessage());
                // 回滚保存
                restTemplateUtils.getPCWP2NotParams(mallConfig.prodPcwp2Url02 + ROLL_BACK_SAVE_ACCEPTANCE + keyId);
                // 回滚已审核数量
                restTemplateUtils.getPCWP2NotParams(mallConfig.prodPcwp2Url02 + ROLL_BACK_LOCK_QUANTITY_DRAFT_URL + keyId);
                if (e.getMessage().contains("商城推送物资类别的与Pcwp物资基础库类别不一致")) {
                    Map maps = (Map) JSON.parse(farArg.toString());
                    Map<String, Object> data = (Map<String, Object>) maps.get("data");
                    Object dtlsMap = data.get("dtls");
                    JSONArray dtlList = JSON.parseArray(dtlsMap.toString());
                    ArrayList<Map<String, Object>> dtls = new ArrayList<>();
                    for (Object dtl : dtlList) {
                        if (dtl instanceof Map) {
                            dtls.add((Map<String, Object>) dtl);
                        }
                    }
                    materialReconciliationService.updateMaterialInfo(dtls);
                    throw new BusinessException(e.getMessage() + "问题已经修复，请重新对账");
                } else {
                    throw new BusinessException(e.getMessage());
                }
            }
        }
        return R.success(isNotPush);
    }

    @GetMapping("performanceManage/materialReconciliationPushAcceptance")
    @ApiOperation(value = "主动推送对账单")
    @NotResubmit
    @LogRecord(title = "对账单管理", businessType = BusinessType.UPDATE, operatorType = OperatorType.MANAGE)
    public R materialReconciliationPushAcceptance(String reconciliationId) {
        materialReconciliationService.materialReconciliationPushAcceptance(reconciliationId);
        return R.success();
    }

    @PostMapping("performanceManage/materialReconciliation/ListByIds")
    @ApiOperation(value = "批量查询对账单数据")
    public R ListByIds(@RequestBody List<String> reconciliationIds) {
        List<MaterialReconciliation> materialReconciliations = materialReconciliationService.ListByIds(reconciliationIds);
        return R.success(materialReconciliations);
    }

    /**
     * 创建并保存接口日志
     *
     * @param secretKey      秘钥唯一key
     * @param methodName     方法名
     * @param localArguments 本地方法请求参数
     * @param farArguments   请求远程接口参数
     * @param errorMessage   错误信息
     */
    private void createAndSaveInterfaceLog(String secretKey, String methodName, Object localArguments,
                                           String farArguments, String errorMessage) {
        InterfaceLogs iLog = new InterfaceLogs();
        iLog.setSecretKey(secretKey);
        iLog.setClassPackage(MaterialReconciliationController.class.getName());
        iLog.setMethodName(methodName);
        iLog.setLocalArguments(JSON.toJSONString(localArguments));
        if (farArguments != null) {
            iLog.setFarArguments(farArguments);
        }
        iLog.setIsSuccess(0);
        iLog.setLogType(1);
        iLog.setErrorInfo(errorMessage);
        interfaceLogsService.create(iLog);
    }

    /**
     * 检查订单是否已推送到PCWP
     * 判断逻辑：先查订单是否关联计划，再查计划的PCWP编号是否存在
     *
     * @param orderSn 订单号
     * @return isNotPush 1=已推送PCWP，0=未推送PCWP
     */
    private int checkOrderPushToPCWPStatus(String orderSn) {

        // 查询订单是否关联计划
        Orders order = ordersService.lambdaQuery()
                .eq(Orders::getOrderSn, orderSn)
                .select(Orders::getPlanNo, Orders::getOrderSn).one();
        if (order == null || order.getPlanNo() == null) {// 订单不存在或未关联计划，说明未推送PCWP
            return 0;
        }
        // 订单关联了计划，查询计划表的PCWP编号
        Plan plan = planService.lambdaQuery()
                .eq(Plan::getBillNo, order.getPlanNo())
                .select(Plan::getPBillNo, Plan::getBillNo).one();
        // 计划存在且PCWP编号不为空，说明已推送PCWP
        int isNotPush = (plan != null && plan.getPBillNo() != null) ? 1 : 0;
        return isNotPush;
    }

    /**
     * 检查计划是否已推送到PCWP
     * 直接通过计划编号查询计划的PCWP编号是否存在
     *
     * @param planBillNo 计划编号
     * @return isNotPush 1=已推送PCWP，0=未推送PCWP
     */
    private int checkPlanPushToPCWPStatus(String planBillNo) {
        Plan plan = planService.lambdaQuery()
                .eq(Plan::getBillNo, planBillNo)
                .select(Plan::getPBillNo, Plan::getBillId).one();
        int isNotPush = (plan != null && plan.getPBillNo() != null) ? 1 : 0;
        return isNotPush;
    }

    /**
     * 检查计划是否已推送到PCWP
     * 直接通过计划编号查询计划的PCWP编号是否存在
     *
     * @param reconciliationId 对账单id
     * @return isNotPush 1=已推送PCWP，0=未推送PCWP
     */
    public int checkReconciliationPushToPCWPStatus(String reconciliationId) {
        // 在同一事务中执行所有查询，确保数据一致性
        MaterialReconciliationDtl dtl = materialReconciliationDtlService
                .lambdaQuery()
                .eq(MaterialReconciliationDtl::getReconciliationId, reconciliationId)
                .select(MaterialReconciliationDtl::getOrderSn)
                .last("LIMIT 1")
                .one();
        String orderSn = dtl.getOrderSn();
        // 查询订单是否关联计划
        Orders order = ordersService.lambdaQuery()
                .eq(Orders::getOrderSn, orderSn)
                .select(Orders::getPlanNo, Orders::getOrderSn).one();
        if (order == null || order.getPlanNo() == null) {
            // 订单不存在或未关联计划，说明未推送PCWP
            return 0;
        }
        // 订单关联了计划，查询计划表的PCWP编号
        Plan plan = planService.lambdaQuery()
                .eq(Plan::getBillNo, order.getPlanNo())
                .select(Plan::getPBillNo, Plan::getBillNo).one();
        // 计划存在且PCWP编号不为空，说明已推送PCWP
        int isNotPush = (plan != null && plan.getPBillNo() != null) ? 1 : 0;
        return isNotPush;
    }
    @PostMapping("performanceManage/materialReconciliation/materialReconciliationLedger")
    @ApiOperation(value = "物资对账台账单")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "orgId", value = "机构id", dataTypeClass = String.class),
            @DynamicParameter(name = "supplierName", value = "供应商名称", dataTypeClass = String.class),
            @DynamicParameter(name = "reconciliationProductType", value = "对账类型", dataTypeClass = Integer.class),
            @DynamicParameter(name = "reconciliationNo", value = "对账单号", dataTypeClass = String.class),
            @DynamicParameter(name = "productName", value = "物资名称", dataTypeClass = String.class),
            @DynamicParameter(name = "productType", value = "物资类型", dataTypeClass = Integer.class),
            @DynamicParameter(name = "startFinishDate", value = "完成开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endFinishDate", value = "完成结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
            @DynamicParameter(name = "assetType", value = "物资类型", dataTypeClass = String.class),
            @DynamicParameter(name = "ledgerType", value = "对账类型", dataTypeClass = String.class),

    })
    public PageR<MaterialReconciliation> materialReconciliationLedger(@RequestBody JSONObject jsonObject) {
        PageUtils page = materialReconciliationDtlService.materialReconciliationLedger(jsonObject, new LambdaQueryWrapper<MaterialReconciliation>());
        //PageUtils page = new PageUtils();
        return PageR.success(page);
    }

}
