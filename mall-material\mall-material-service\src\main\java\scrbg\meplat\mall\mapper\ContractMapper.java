package scrbg.meplat.mall.mapper;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import scrbg.meplat.mall.entity.Contract;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;
import org.apache.ibatis.annotations.Mapper;
import scrbg.meplat.mall.vo.payment.UnUsedContractDto;

import java.util.List;

/**
 * @描述：合同 Mapper 接口
 * @作者: ye
 * @日期: 2025-03-04
 */
@Mapper
@Repository
public interface ContractMapper extends BaseMapper<Contract> {

    List<UnUsedContractDto> getContractNoList(@Param("year") String year, @Param("cType") Integer type, @Param("payStatus") Integer payStatus);

    @Delete("delete from contract where contract_no = #{contractNo}")
    void deleteRealByContractNo(String contractNo);
}