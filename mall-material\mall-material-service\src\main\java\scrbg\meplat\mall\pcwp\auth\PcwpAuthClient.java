package scrbg.meplat.mall.pcwp.auth;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import scrbg.meplat.mall.pcwp.PcwpClient;
import scrbg.meplat.mall.pcwp.PcwpRes;
import scrbg.meplat.mall.pcwp.auth.model.SignUp;
import scrbg.meplat.mall.pcwp.auth.model.TokenRes;

/**
 * 身份认证
 * 
 */
@FeignClient(name = "pcwp-auth-service", url = "${mall.prodPcwp2Url}")
public interface PcwpAuthClient extends PcwpClient{
    /**
     * 注册-用户/机构注册
     * @param signUp 注册信息
     * @return 注册结果
     */
    @PostMapping("/identity/auth/userorg/signup")
    PcwpRes<Boolean> userOrgSignUp(@RequestBody SignUp signUp);

     /**
     * 认证-创建外部用户Token
     * 
     * @param phoneNo 手机号
     * @param sysCode 系统编码
     * @return Token响应
     */
    @PostMapping("/identity/auth/createExternalToken")
    PcwpRes<TokenRes> createExternalToken(
            @RequestParam("phoneNo") String phoneNo,
            @RequestParam("sysCode") String sysCode
    );

     /**
     * 登录接口
     *
     * @param account       登录账号
     * @param password      密码
     * @param identityType  授权方式(0:手机号验证码|1:邮箱|2:微信|3:QQ|5:帐号密码|6:其他)
     * @param offline       是否离线
     * @param sysCode       系统编码
     * @return 登录结果
     */
    @PostMapping("/identity/auth/signin")
    PcwpRes<TokenRes>  signIn(
            @RequestParam("account") String account,
            @RequestParam("password") String password,
            @RequestParam(value = "identityType", required = false) String identityType,
            // 文档上有，但历史代码中没有
            // @RequestParam(value = "offline", required = false) String offline,
            @RequestParam(value = "sysCode", required = false) String sysCode
    );
}
