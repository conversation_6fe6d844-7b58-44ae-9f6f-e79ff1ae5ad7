package scrbg.meplat.mall.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import scrbg.meplat.mall.entity.ShopSupplierRele;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * @描述：店铺供方关联表 Mapper 接口
 * @作者: ye
 * @日期: 2023-06-05
 */
@Mapper
@Repository
public interface ShopSupplierReleMapper extends BaseMapper<ShopSupplierRele> {

    /**
     * 获取本机构下供店铺
     *
     * @param pages
     * @param innerMap
     * @return
     */
    List<ShopSupplierRele> listShopListBySupplierId(Page<ShopSupplierRele> pages, @Param("innerMap") Map<String, Object> innerMap);

    /**
     * 获取本机构下供店铺
     * @param innerMap
     * @return
     */
    int listShopListBySupplierIdCount(@Param("innerMap") Map<String, Object> innerMap);

    /**
     * 获取下供店铺
     *
     * @param pages
     * @param innerMap
     * @return
     */
    List<ShopSupplierRele> listShopList(Page<ShopSupplierRele> pages, @Param("innerMap") Map<String, Object> innerMap);

    /**
     * 获取下供店铺
     * @param innerMap
     * @return
     */
    int listShopListCount(@Param("innerMap") Map<String, Object> innerMap);

    /**
     * 根据订单条件筛选供应商分页查询
     * @param pages 分页参数
     * @param innerMap 查询条件
     * @return 供应商列表
     */
    List<ShopSupplierRele> querySuppliersByOrderConditions(Page<ShopSupplierRele> pages, @Param("innerMap") Map<String, Object> innerMap);

    /**
     * 根据订单条件筛选供应商总数查询
     * @param innerMap 查询条件
     * @return 总数
     */
    int querySuppliersByOrderConditionsCount(@Param("innerMap") Map<String, Object> innerMap);
}