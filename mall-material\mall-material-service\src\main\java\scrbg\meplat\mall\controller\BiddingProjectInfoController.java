package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.entity.BiddingProjectInfo;
import scrbg.meplat.mall.service.BiddingProjectInfoService;

import java.util.List;

/**
 * @描述：招标项目信息控制类
 * @作者: y
 * @日期: 2022-11-13
 */
@RestController
@RequestMapping("/biddingProjectInfo")
@Api(tags = "招标项目信息")
public class BiddingProjectInfoController {

    @Autowired
    public BiddingProjectInfoService biddingProjectInfoService;

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<BiddingProjectInfo> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = biddingProjectInfoService.queryPage(jsonObject, new LambdaQueryWrapper<BiddingProjectInfo>());
        return PageR.success(page);
    }

    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<BiddingProjectInfo> findById(String id) {
        BiddingProjectInfo biddingProjectInfo = biddingProjectInfoService.getById(id);
        return R.success(biddingProjectInfo);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody BiddingProjectInfo biddingProjectInfo) {
        biddingProjectInfoService.create(biddingProjectInfo);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody BiddingProjectInfo biddingProjectInfo) {
        biddingProjectInfoService.update(biddingProjectInfo);
        return R.success();
    }

    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        biddingProjectInfoService.delete(id);
        return R.success();
    }


    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")

    public R deleteBatch(@RequestBody List<String> ids) {
        biddingProjectInfoService.removeByIds(ids);
        return R.success();
    }
}

