package scrbg.meplat.mall.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @create 2023-08-16 18:58
 */
@Data
public class CheckReconciliationIsCancellationVO {


    @ApiModelProperty(value = "错误码（5001：对账单不存在！，5002：对账单未审核完成！）")
    private Integer errorCode;

    @ApiModelProperty(value = "错误消息")
    private String errorMsg;

}
