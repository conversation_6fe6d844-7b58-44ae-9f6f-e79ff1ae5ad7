package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @描述：招标详细信息
 * @作者: y
 * @日期: 2022-11-13
 */
@ApiModel(value = "招标详细信息")
@Data
@TableName("bidding_info")
public class BiddingInfo extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "招标id")

    private String biddingId;

    @ApiModelProperty(value = "采购公司id")

    private String purchasenCompanyId;

    @ApiModelProperty(value = "项目id")

    private String projectId;

    @ApiModelProperty(value = "投标保证金")

    private BigDecimal earnestMoney;

    @ApiModelProperty(value = "联系人ids")

    private String linkmanIds;

    @ApiModelProperty(value = "报名开始时间")

    private Date startTime;

    @ApiModelProperty(value = "报名结束时间")

    private Date endTime;

    @ApiModelProperty(value = "投标人限定")

    private String bidLimit;

    @ApiModelProperty(value = "状态：1启用 0停用")

    private Integer state;


}
