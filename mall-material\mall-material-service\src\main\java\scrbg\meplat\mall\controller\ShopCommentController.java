package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.config.logRecording.LogRecord;
import scrbg.meplat.mall.config.logRecording.enums.BusinessType;
import scrbg.meplat.mall.config.logRecording.enums.OperatorType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import scrbg.meplat.mall.service.ShopCommentService;
import scrbg.meplat.mall.entity.ShopComment;

import javax.validation.Valid;
import java.util.List;

/**
 * @描述：商铺评价控制类
 * @作者: ye
 * @日期: 2025-05-21
 */
@RestController
@RequestMapping("/shopComment")
@Api(tags = "商铺评价")
public class ShopCommentController{

        @Autowired
        public ShopCommentService shopCommentService;

        @PostMapping("/page")
        @ApiOperation(value = "供应商评价汇总分页查询")
        public PageR<ShopComment> listByPage(@RequestBody JSONObject jsonObject) {
                PageUtils page = shopCommentService.queryPage(jsonObject, new LambdaQueryWrapper<>());
                return PageR.success(page);
        }

        @GetMapping("/{id}")
        @ApiOperation(value = "根据主键查询")
        @ApiImplicitParams({
                @ApiImplicitParam(name = "id", value = "ID", required = true,
                        dataType = "String", paramType = "path")
        })
        public R<ShopComment> findById(@PathVariable String id) {
            ShopComment shopComment =shopCommentService.getById(id);
            return R.success(shopComment);
        }

        @PostMapping("/create")
        @ApiOperation(value = "新增")
        @LogRecord(title = "商铺评价新增", businessType = BusinessType.INSERT, operatorType = OperatorType.MANAGE)
        public R save(@RequestBody @Valid ShopComment shopComment) {
                R r = shopCommentService.create(shopComment);
                return r;
        }

        @PutMapping("/update")
        @ApiOperation(value = "修改")
        @LogRecord(title = "商铺评价新增修改", businessType = BusinessType.UPDATE, operatorType = OperatorType.MANAGE)
        public R update(@RequestBody @Valid ShopComment shopComment) {
                shopCommentService.update(shopComment);
                return R.success();
        }

        @DeleteMapping("/{id}")
        @ApiOperation(value = "根据主键删除")
        @ApiImplicitParams({
                @ApiImplicitParam(name = "id", value = "ID", required = true,
                        dataType = "String", paramType = "path")
        })
        @LogRecord(title = "xx管理", businessType = BusinessType.DELETE, operatorType = OperatorType.MANAGE)
        public R delete(@PathVariable String id) {
                shopCommentService.delete(id);
            return R.success();
        }


        @DeleteMapping("/batch")
        @ApiOperation(value = "根据主键批量删除")
        @LogRecord(title = "xx管理", businessType = BusinessType.DELETE, operatorType = OperatorType.MANAGE)

        public R deleteBatch(@RequestBody List<String> ids) {
                shopCommentService.deleteBatch(ids);
            return R.success();
        }
        }

