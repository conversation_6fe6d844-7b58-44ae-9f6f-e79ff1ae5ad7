package scrbg.meplat.mall.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-07-17 16:07
 */
@Data
public class BatchCreateTwoOrderDTO {


    @ApiModelProperty(value = "供应商id",required = true)
    @NotEmpty
    private String supplierId;

    @ApiModelProperty(value = "供应商名称",required = true)
    @NotEmpty
    private String supplierName;

    @ApiModelProperty(value = "订单项ids",required = true)
    @NotNull
    private List<String> orderItemId;



}
