package scrbg.meplat.mall.dto.reconciliation;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2023-07-31 8:51
 */
@Data
public class MaterialReconciliationUpdateDtlDTO {

    @ApiModelProperty(value = "对账明细ID")
    private String reconciliationDtlId;

    @ApiModelProperty(value = "对账单价（含税）")

    private BigDecimal price;


    @ApiModelProperty(value = "对账数量")

    private BigDecimal quantity;

    @ApiModelProperty(value = "到货网价")

    private BigDecimal freightPrice;


    @ApiModelProperty(value = "固定费用")

    private BigDecimal fixationPrice;

}
