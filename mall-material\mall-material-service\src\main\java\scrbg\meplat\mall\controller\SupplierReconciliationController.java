package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import scrbg.meplat.mall.config.logRecording.LogRecord;
import scrbg.meplat.mall.config.logRecording.enums.BusinessType;
import scrbg.meplat.mall.config.logRecording.enums.OperatorType;
import scrbg.meplat.mall.config.redis.redisson.NotResubmit;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.service.SupplierReconciliationService;
import scrbg.meplat.mall.util.LogUtil;
import scrbg.meplat.mall.vo.supplier.SupplierReconciliationVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @描述：物资验收控制类
 * @作者: ye
 * @日期: 2023-08-15
 */
@RestController
@RequestMapping("/supplierReconciliation")
@Api(tags = "二级对账单")
public class SupplierReconciliationController {

    @Autowired
    public SupplierReconciliationService supplierReconciliationService;

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "userType", value = "用户类型（1供应商  2二级供应商）", required = true, dataTypeClass = Integer.class),
            @DynamicParameter(name = "type", value = "对账类型（1发货单  2退货）", dataTypeClass = Integer.class),
            @DynamicParameter(name = "createType", value = "新增来源（1自营店新增2二级供应商新增）", dataTypeClass = Integer.class),
            @DynamicParameter(name = "state", value = "状态（0草稿1待提交2待审核3审核通过4审核失败7作废）", dataTypeClass = Integer.class),
            @DynamicParameter(name = "orderBy", value = "排序字段(0:创建时间:1开始时间2结束时间", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
            @DynamicParameter(name = "billNo", value = "对账单编号", dataTypeClass = String.class),
            @DynamicParameter(name = "sourceBillNo", value = "源单编号", dataTypeClass = String.class),
            @DynamicParameter(name = "orderSn", value = "订单号", dataTypeClass = String.class),
            @DynamicParameter(name = "supplierName", value = "自营店名称", dataTypeClass = String.class),
            @DynamicParameter(name = "twoSupplierName", value = "供应商名称", dataTypeClass = String.class),
    })
    public PageR<SupplierReconciliation> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = supplierReconciliationService.queryPage(jsonObject, new LambdaQueryWrapper<SupplierReconciliation>());
        return PageR.success(page);
    }

    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<SupplierReconciliation> findById(String id) {
        SupplierReconciliation supplierReconciliation = supplierReconciliationService.getById(id);
        return R.success(supplierReconciliation);
    }

    @PostMapping("/reconciliationSubmit")
    @ApiOperation(value = "批量提交")
    @NotResubmit
    @Transactional
    @LogRecord(title = "二级对账单", businessType = BusinessType.UPDATE, operatorType = OperatorType.MANAGE, isSaveRequestData = true)
    public R materialReconciliationSubmit(@RequestBody List<String> ids) {
        supplierReconciliationService.reconciliationSubmit(ids);
        return R.success();
    }

    @GetMapping("supplierReconciliationSupplierAffirm")
    @ApiOperation(value = "供应商确认单据")
    @NotResubmit
    @Transactional
    @LogRecord(title = "二级对账单", businessType = BusinessType.UPDATE, operatorType = OperatorType.MANAGE, isSaveRequestData = true)
    public R supplierReconciliationSupplierAffirm(String billId) {
        supplierReconciliationService.supplierReconciliationSupplierAffirm(billId);
        return R.success();
    }

    @GetMapping("twoSupplierReconciliationSupplierAffirm")
    @ApiOperation(value = "二级供应商确认单据")
    @NotResubmit
    @Transactional
    @LogRecord(title = "二级对账单", businessType = BusinessType.UPDATE, operatorType = OperatorType.MANAGE, isSaveRequestData = true)
    public R materialReconciliationSupplierAffirm(String billId) {
        supplierReconciliationService.twoSupplierReconciliationSupplierAffirm(billId);
        return R.success();
    }

    @PostMapping("twoSupplierReconciliationSupplierCreate")
    @ApiOperation(value = "供应商新增对账")
    @NotResubmit
    @Transactional
    @LogRecord(title = "二级对账单", businessType = BusinessType.INSERT, operatorType = OperatorType.MANAGE, isSaveRequestData = true)
    public R materialReconciliationSupplierCreate(@RequestBody SupplierReconciliation dto) {
        supplierReconciliationService.twoSupplierReconciliationSupplierCreate(dto);
        return R.success();
    }

    @PostMapping("/saveAndUpdate")
    @ApiOperation(value = "供应商新增对账")
    @NotResubmit
    @Transactional
    @LogRecord(title = "二级对账单", businessType = BusinessType.INSERT, operatorType = OperatorType.MANAGE, isSaveRequestData = true)
    public R saveAndUpdate(@RequestBody SupplierReconciliation dto) {
        supplierReconciliationService.saveAndUpdate(dto);
        return R.success();
    }

    @GetMapping("/findByNo")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "ID", required = true,
            dataType = "String", paramType = "query")
    })
    public R<SupplierReconciliation> findByNo(String billNo) {
        SupplierReconciliation supplierReconciliation = supplierReconciliationService.getByNo(billNo);
        return R.success(supplierReconciliation);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    @NotResubmit
    @Transactional
    @LogRecord(title = "二级对账单", businessType = BusinessType.INSERT, operatorType = OperatorType.MANAGE, isSaveRequestData = true)
    public R save(@RequestBody SupplierReconciliation supplierReconciliation) {
        supplierReconciliationService.create(supplierReconciliation);
        return R.success();
    }

    @GetMapping("/updateState")
    @ApiOperation(value = "改变对账单状态")
//    @NotResubmit
    @Transactional
    @LogRecord(title = "二级对账单", businessType = BusinessType.UPDATE, operatorType = OperatorType.MANAGE, isSaveRequestData = true)
    public R updateState(String billId, int state) {
        supplierReconciliationService.updateState(billId, state);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    @NotResubmit
    @Transactional
    @LogRecord(title = "二级对账单", businessType = BusinessType.INSERT, operatorType = OperatorType.MANAGE, isSaveRequestData = true)
    public R update(@RequestBody SupplierReconciliation supplierReconciliation) {
        supplierReconciliationService.update(supplierReconciliation);
        return R.success();
    }

    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "ID", required = true,
            dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        supplierReconciliationService.delete(id);
        return R.success();
    }

    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")
    public R deleteBatch(@RequestBody List<String> ids) {
        supplierReconciliationService.removeByIds(ids);
        return R.success();
    }

    @GetMapping("/outputExcel")
    @ApiOperation(value = "导出二级对账单")
    @LogRecord(title = "二级对账单", businessType = BusinessType.EXPORT, operatorType = OperatorType.MANAGE, isSaveRequestData = true)
    public R outputExcel(String billId, HttpServletResponse response) {
        supplierReconciliationService.outputExcel(billId, response);
        return R.success();
    }
}

