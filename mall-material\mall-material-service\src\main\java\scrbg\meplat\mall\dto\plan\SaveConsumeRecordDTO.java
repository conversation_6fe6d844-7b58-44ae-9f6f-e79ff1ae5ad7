package scrbg.meplat.mall.dto.plan;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023-12-26 15:48
 */
@Data
public class SaveConsumeRecordDTO {

    @ApiModelProperty(value = "消耗方式 商城：SHOP,可用值:SHOP")
    private String consumptionMethod = "SHOP";
    @ApiModelProperty(value = "合同id")
    private String contractId;
    @ApiModelProperty(value = "凭证(各平台唯一标志)")
    private String credential;
    @ApiModelProperty(value = "版本 1:2.0, 2:1.0")
    private Integer version;
    @ApiModelProperty(value = "清单信息    {\n" +
            "      \"listId\": \"\",\n" +
            "      \"quantity\": 0\n" +
            "    }")
    private List<Map> listInfo;

    @ApiModelProperty(value = "变更：change,新增：add")
    private String type;
}
