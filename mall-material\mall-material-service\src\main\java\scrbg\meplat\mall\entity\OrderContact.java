package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;

/**
 * @描述：订单合同关联信息
 * @作者: ye
 * @日期: 2023-03-15
 */
@ApiModel(value = "订单合同关联信息")
@Data
@TableName("order_contact")
public class OrderContact extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "订单合同id")
    private String orderContactId;

    @ApiModelProperty(value = "订单id")

    private String orderId;


    @ApiModelProperty(value = "订单编号")

    private String orderSn;


    @ApiModelProperty(value = "订单项id")

    private String orderItemId;


    @ApiModelProperty(value = "合同id")

    private String contactId;


    @ApiModelProperty(value = "状态")

    private Integer state;


}