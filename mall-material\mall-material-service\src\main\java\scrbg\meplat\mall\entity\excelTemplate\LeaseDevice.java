package scrbg.meplat.mall.entity.excelTemplate;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2023-03-07 9:19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@HeadRowHeight(40)
@ColumnWidth(25)
@ContentRowHeight(40)
public class LeaseDevice implements Serializable {

//        private static final long serialVersionUID = -5144055068797033748L;

        @ExcelProperty(value = "序号（自增即可）")
        private Long id;

        @ExcelProperty(value = "内部装备名称")
        private String relevanceName;

        @ExcelProperty(value = "装备标题")
        private String productName;

        @ExcelProperty(value = "分类名称(XXX/XXX/XXX)")
        private String classNamePath;

        @ExcelProperty(value = "最低价")
        private BigDecimal productMinPrice;

        @ExcelProperty(value = "品牌名称")
        private String brandName;

        @ExcelProperty(value = "排序值")
        private Integer shopSort;

        @ExcelProperty(value = "是否周材（0否1是）")
        private Integer isWeekMaterials;

        @ExcelProperty(value = "规格")
        private String skuName;

        @ExcelProperty(value = "成本价")
        private BigDecimal costPrice;

        @ExcelProperty(value = "库存")
        private BigDecimal stock;

        @ExcelProperty(value = "计量单位")
        private String unit;

        @ExcelProperty(value = "原价")
        private BigDecimal originalPrice;

        @ExcelProperty(value = "销售价格")

        private BigDecimal sellPrice;

        @ExcelProperty(value = "租赁单位（天、月、年）")

        private String leaseUnit;

        @ExcelProperty(value = "省")
        private String province;

        @ExcelProperty(value = "市")
        private String city;

        @ExcelProperty(value = "县、区")
        private String county;

        @ExcelProperty(value = "详细地址(带省市区完整地址)")
        private String detailedAddress;

        @ExcelProperty(value = "商品描述（可html代码）")
        @ColumnWidth(50)
        private String productDescribe;

}


