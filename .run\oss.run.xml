<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="oss" type="docker-deploy" factoryName="dockerfile" server-name="正式环境docker">
    <deployment type="dockerfile">
      <settings>
        <option name="imageTag" value="oss" />
        <option name="containerName" value="oss" />
        <option name="portBindings">
          <list>
            <DockerPortBindingImpl>
              <option name="containerPort" value="9009" />
              <option name="hostPort" value="9009" />
            </DockerPortBindingImpl>
          </list>
        </option>
        <option name="commandLineOptions" value="--restart=always -v /home/<USER>/logs:/logs" />
        <option name="sourceFilePath" value="oss/dockerfile" />
      </settings>
    </deployment>
    <method v="2">
      <option name="Maven.BeforeRunTask" enabled="true" file="$PROJECT_DIR$/oss/pom.xml" goal="clean package -U -DskipTests" />
    </method>
  </configuration>
</component>