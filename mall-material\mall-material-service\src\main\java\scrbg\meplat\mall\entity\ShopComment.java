package scrbg.meplat.mall.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

/**
 * @描述：商铺评价
 * @作者: ye
 * @日期: 2025-05-21
 */
@ApiModel(value = "商铺评价")
@Data
@TableName("shop_comment")
public class ShopComment extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "商铺评价关联表id")
    private String shopCommentId;
    @ApiModelProperty(value = "店铺id")
    private String shopId;
    @ApiModelProperty(value = "店铺名称")
    private String shopName;
    @ApiModelProperty(value = "主营业务")
    private String mainBusiness;
    @ApiModelProperty(value = "评价分数-服务评分")
    private BigDecimal commentServiceScore;
    @ApiModelProperty(value = "评价分数-商品品质")
    private BigDecimal commentLevel;
    @ApiModelProperty(value = "评价分数-保供能力")
    private BigDecimal commentSupply;
    @ApiModelProperty(value = "评价分数-诚信履约")
    private BigDecimal commentIntegrity;
    @ApiModelProperty(value = "评价分数-服务水平")
    private BigDecimal commentService;
    @ApiModelProperty(value = "统计月份（格式：YYYY-MM）")
    private String month;
    @ApiModelProperty(value = "评价区间开始时间")
    private Date commentStart;
    @ApiModelProperty(value = "评价区间结束时间")
    private Date commentEnd;

}