package scrbg.meplat.mall.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @create 2023-06-30 16:15
 */
@Data
public class AuditDTO {

    @ApiModelProperty(value = "单据id")
    private String id;

    @ApiModelProperty(value = "是否通过（1是0否）")
    private Integer isOpen;

    @ApiModelProperty(value = "未通过原因")
    private String auditResult;

    @ApiModelProperty(value = "关联类型（1月供计划2月供变更计划3竞价采购提交4竞价采购中标" +
            "5对账单6二级对账单 7大宗临购清单 8发票 9大宗临购供应商拒绝审核 10年度服务费缴费审核 11交易服务费缴费审核12交易服务费对账单确认审核）")
    private Integer relevanceType;

    @ApiModelProperty(value = "有效期开始日期")
    @TableField(exist = false)
    private LocalDate serveStartTime;

    @ApiModelProperty(value = "有效期结束日期")
    @TableField(exist = false)
    private LocalDate serveEndTime;
}
