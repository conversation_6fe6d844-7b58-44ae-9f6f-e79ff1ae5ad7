package scrbg.meplat.mall.dto.outer;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-02-27 15:47
 */
@Data
public class UpdateBatchUsedDeviceStateDTO {

    @ApiModelProperty(value = "商品id",required = true)
    @NotEmpty(message = "商品id不能为空！")
    private List<String> productIds;

    @ApiModelProperty(value = "商品状态（1上架 2下架）",required = true)
    @NotNull(message = "商品状态不能为空！")
    @Max(value = 2, message = "商品状态输入错误！")
    @Min(value = 1, message = "商品状态输入错误！")
    private Integer state;

    @ApiModelProperty(value = "店铺id",required = true)
    @NotEmpty(message = "店铺id不能为空！")
    private String shopId;
}
