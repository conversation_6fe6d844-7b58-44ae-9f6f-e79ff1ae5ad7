package scrbg.meplat.mall.enums.file;

public enum FileEnum {

    IMG_TYPE_GENERAL(0,"普通图片"),
    IMG_TYPE_MIN(1,"小图"),

    TYPE_IMG(1,"图片"),
    TYPE_VIDEO(2,"视频"),
    TYPE_ACCESSORY(3,"附件"),

    IS_MIN_NO(0,"否"),
    IS_MIN_YES(1,"是"),

    RELEVANCE_TYPE_PRODUCT(1,"商品"),
    RELEVANCE_TYPE_ASK(2,"问答"),
    RELEVANCE_TYPE_MESSAGE(3,"消息"),
    RELEVANCE_TYPE_SHOP(4,"店铺"),
    RELEVANCE_TYPE_SUPPLIER(5,"供应商"),
    RELEVANCE_TYPE_COMMENT(8,"评价");

    /**
     * 编码
     */
    private int code;

    /**
     * 说明
     */
    private String remark;

    FileEnum() {
    }

    FileEnum(int code, String remark) {
        this.code = code;
        this.remark = remark;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
    }

