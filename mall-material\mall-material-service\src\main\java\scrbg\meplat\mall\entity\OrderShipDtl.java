package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @描述：
 * @作者: ye
 * @日期: 2023-05-22
 */
@ApiModel(value = "发货单项")
@Data
@TableName("order_ship_dtl")
public class OrderShipDtl extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "发货单项Id")
    private String dtlId;

    @ApiModelProperty(value = "订单id")
    private String orderId;

    @ApiModelProperty(value = "订单项id")
    private String orderItemId;

    @ApiModelProperty(value = "材质")
    private String texture;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "订单编号")
    private String orderSn;

    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "商品编号")
    private String productSn;

    @ApiModelProperty(value = "商品Id")
    private String productId;

    @ApiModelProperty(value = "规格")
    private String skuName;

    @ApiModelProperty(value = "发货时间")
    private Date sendTime;

    @ApiModelProperty(value = "收货时间")
    private Date receiveTime;

    @ApiModelProperty(value = "发货数量")
    private BigDecimal shipCounts;

    @ApiModelProperty(value = "确认收货数量")
    private BigDecimal shipNum;

    @ApiModelProperty(value = "订单项总数量")
    private BigDecimal totalSum;

    @ApiModelProperty(value = "发货单id")
    private String billId;

    @ApiModelProperty(value = "发货单编号")
    private String billSn;

    @ApiModelProperty(value = "商品分类id")
    private String productCategoryId;

    @ApiModelProperty(value = "商品跟类名称")
    private String productCategoryName;

    @TableField(exist = false)
    @ApiModelProperty(value = "未发货数量")
    private BigDecimal unShipCounts;

    @ApiModelProperty(value = "退货数量")
    private BigDecimal returnCounts;

    @ApiModelProperty(value = "店铺id")
    private String shopId;

    @ApiModelProperty(value = "最大发货数量")
    @TableField(exist = false)
    private BigDecimal maxShipCounts;

    @ApiModelProperty(value = "是否相等   0 (发货数量==确认收货数量) 1：发货数量>确认收货数量  ")
    private Integer isEqual;

    @ApiModelProperty(value = "  0 未对账 1已对账  ")
    private Integer isReconciliation;

    @ApiModelProperty(value = "商品价格")
    private BigDecimal productPrice;

    @ApiModelProperty(value = "不含税商品价格")
    private BigDecimal noRatePrice;

    @ApiModelProperty(value = "供应商含税金额")
    private BigDecimal noRateAmount = BigDecimal.valueOf(0);

    @ApiModelProperty(value = "供应商金额（含税）")
    private BigDecimal totalAmount = BigDecimal.valueOf(0);

    @ApiModelProperty(value = "二级供应商商品价格 （含税）")
    private BigDecimal otherProductPrice;

    @ApiModelProperty(value = "二级供应商不含税商品价格")
    private BigDecimal otherNoRatePrice;

    @ApiModelProperty(value = "二级供应商商品总金额（含税）")
    private BigDecimal otherTotalAmount;

    @ApiModelProperty(value = "供应商含税金额")
    private BigDecimal otherNoRateAmount;

    @ApiModelProperty(value = "税额")
    BigDecimal taxRate = BigDecimal.valueOf(0);

    @ApiModelProperty(value = "税额")
    @TableField(exist = false)
    BigDecimal otherTaxRate = BigDecimal.valueOf(0);

    @TableField(exist = false)
    @ApiModelProperty(value = "关联名称（用于关联外部的名称唯一不修改）废弃")
    private String relevanceName;
}
