package scrbg.meplat.mall.controller;

import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import scrbg.meplat.mall.vo.system.MenuVo;
import scrbg.meplat.mall.vo.system.RouteVo;

import java.util.ArrayList;
import java.util.List;

@Api(tags = "系统")
@RestController
@RequestMapping("/system")
@ApiSort(value = 500)
public class SystemController {

    @ApiOperation(value = "取得用户菜单")
    @GetMapping("/getUserMenus")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", required = true)})
    public R getUserMenus(String type) {
        List<MenuVo> results = new ArrayList();
        MenuVo menu = new MenuVo();
        menu.setMenuName("测试1");
        menu.setMenuId("测试1111111");
        RouteVo vo = new RouteVo();
        vo.setPath("/platform/content/adPicture");
        menu.setRoute(vo);
        results.add(menu);

        MenuVo menu2 = new MenuVo();
        menu2.setMenuName("测试2");
        menu2.setMenuId("测试11311");
        RouteVo vo2 = new RouteVo();
        vo2.setPath("/platform/product/productCategory");
        menu2.setRoute(vo);
        results.add(menu2);

        List<MenuVo> results3 = new ArrayList();
        MenuVo menu3 = new MenuVo();
        menu3.setMenuName("测试1");
        menu3.setMenuId("测试1111111");
        RouteVo vo3 = new RouteVo();
        vo3.setPath("/platform/content/adPicture");
        menu3.setRoute(vo3);

        results3.add(menu3);
        //  menu2.setChildren(results3);


        return R.success(results);
    }

}
