package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @描述：店铺
 * @作者: y
 * @日期: 2022-11-30
 */
@ApiModel(value = "店铺")
@Data
@TableName("shop")
public class Shop extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "店铺id")
    private String shopId;

    @ApiModelProperty(value = "序列号")
    private String serialNum;

    @ApiModelProperty(value = "店铺名称")

    private String shopName;


    @ApiModelProperty(value = "店铺余额")

    private BigDecimal shopBalance;

    @ApiModelProperty(value = "店铺冻结的资金")

    private BigDecimal shopFreezeMoney;


    @ApiModelProperty(value = "店铺类型 0：个体户  1：企业  2：个人")

    private Integer shopType;


    @ApiModelProperty(value = "店铺logo")

    private String shopImg;

    @ApiModelProperty(value = "广告图")

    private String adImg;


    @ApiModelProperty(value = "省")

    private String province;


    @ApiModelProperty(value = "市")

    private String city;


    @ApiModelProperty(value = "县、区")

    private String county;


    @ApiModelProperty(value = "详细地址")

    private String detailedAddress;


    @ApiModelProperty(value = "经度")

    private BigDecimal longitude;


    @ApiModelProperty(value = "纬度")

    private BigDecimal latitude;


    @ApiModelProperty(value = "店铺状态 1:启用 0停用")

    private Integer state;


    @ApiModelProperty(value = "店铺简介")

    private String shopDescrible;


    @ApiModelProperty(value = "企业id")

    private String enterpriseId;


    @ApiModelProperty(value = "主营业务")

    private String mainBusiness;


    @ApiModelProperty(value = "是否自营：1：是  0：否（默认：0）")

    private Integer isBusiness;


    @ApiModelProperty(value = "是否为供应商：1： 是  0：否")

    private Integer isSupplier;


    @ApiModelProperty(value = "是否内部店铺：1：是  0：否")

    private Integer isInternalShop;


    @ApiModelProperty(value = "是否支持内部结算：1：是  0：否")

    private Integer isInternalSettlement;


    @ApiModelProperty(value = "店铺审核状态： 1：审核通过  2：未审核  3：审核未通过 ")

    private Integer auditStatus;

    @ApiModelProperty(value = "开店日期：  ")
    private Date openDate;

    @ApiModelProperty(value = "是否为蜀道企业(1是，0否)")
    private int shuDaoFlag;


    @ApiModelProperty(value = "联系人")

    private String linkMan;

    @ApiModelProperty(value = "联系电话")

    private String contactNumber;


    @ApiModelProperty(value = "首字母")

    private String initial;


    @ApiModelProperty(value = "综合排序值")

    private String synthesisSort;

    @ApiModelProperty(value = "审核失败原因")

    private String failReason;

    @ApiModelProperty(value = "是否首页显示")

    private Integer isIndexShow;

    @ApiModelProperty(value = "其他服务权限（JSON）")
    private String isOtherAuth;

    @ApiModelProperty(value = "退货详细地址")
    private String returnAddress;
    @ApiModelProperty(value = "退货联系人姓名")
    private String returnRelationName;
    @ApiModelProperty(value = "退货联系人电话姓名")
    private String returnRelationNumber;

    @ApiModelProperty(value = "店铺类别（1普通店铺2多供方店铺）")
    private Integer shopClass;

    @ApiModelProperty(value = "是否能提供主材（0否1是默认0）")
    private Integer isPrincipalMaterial;
    @ApiModelProperty(value = "店铺审核通过时间")
    private Date auditPassTime;


    @ApiModelProperty(value = "税率")
    @TableField(exist = false)
    private BigDecimal taxRate;

    @ApiModelProperty(value = "企业名称")
    @TableField(exist = false)
    private String enterpriseName;

    @ApiModelProperty(value = "是否缴费")
    private Integer isArrearage;

    @ApiModelProperty(value = "缴费截止天数")
    private Integer payDays;

    @ApiModelProperty(value = "评价分数-服务评分")
    @TableField(exist = false)
    private String commentServiceScore;

    @ApiModelProperty(value = "评价分数-商品品质")
    @TableField(exist = false)
    private String commentLevel;

    @ApiModelProperty(value = "评价分数-保供能力")
    @TableField(exist = false)
    private String commentSupply;

    @ApiModelProperty(value = "评价分数-诚信履约")
    @TableField(exist = false)
    private String commentIntegrity;

    @ApiModelProperty(value = "评价分数-服务水平")
    @TableField(exist = false)
    private String commentService;
}
