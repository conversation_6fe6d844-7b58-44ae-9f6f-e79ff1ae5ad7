package scrbg.meplat.mall.controller.website;

import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.entity.AdPicture;
import scrbg.meplat.mall.service.AdPictureService;

import java.util.List;

/**
 * @描述：广告图片控制类
 * @作者: y
 * @日期: 2022-11-08
 */
@RestController
@RequestMapping("/w/adPicture")
@ApiSort(value = 100)
@Api(tags = "广告图片（前台）")
public class WebsiteAdPictureController {
    @Autowired
    MallConfig mallConfig;
    @Autowired
    public AdPictureService adPictureService;

    @PostMapping("/getListAdPicture")
    @ApiOperation(value = "查询广告图")
    @DynamicParameters(name = "参数列表", properties = {
            @DynamicParameter(name = "useType", value = "面显示位置（1，平台首页，2，商城首页", dataTypeClass = Integer.class),
            @DynamicParameter(name = "size", value = "查询数量", dataTypeClass = Integer.class),
    })
    public R<List<AdPicture>> getListAdPicture(@RequestBody JSONObject jsonObject) {
        List<AdPicture> page = adPictureService.getListAdPicture(jsonObject);
        return R.success(page);
    }
}
