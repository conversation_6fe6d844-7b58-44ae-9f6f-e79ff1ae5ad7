package scrbg.meplat.mall.entity;

import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
/**
 * @描述：用户行为表
 * @作者: ye
 * @日期: 2023-09-04
 */
@ApiModel(value="用户行为表")
@Data
@TableName("user_activity")
public class UserActivity implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "用户行为id")
    private String userActivityId;

    @ApiModelProperty(value = "用户id")

    private String userId;


    @ApiModelProperty(value = "用户名称")

    private String userName;


    @ApiModelProperty(value = "登陆时间或行为时间")

    private Date loginTime;


    @ApiModelProperty(value = "机构id")

    private String orgId;


    @ApiModelProperty(value = "机构名称")

    private String orgName;


    @ApiModelProperty(value = "机构类型：0：个体户  1：企业  2：个人")

    private Integer orgType;


    @ApiModelProperty(value = "登陆ip")

    private String loginIp;


    @ApiModelProperty(value = "备注信息")

    private String remark;


    @ApiModelProperty(value = "登录方式")

    private String loginType;



    @ApiModelProperty(value = "用户手机号")

    private String userMobile;

    @ApiModelProperty(value = "行为类型（0登陆1发送验证码）")

    private Integer busType;
}