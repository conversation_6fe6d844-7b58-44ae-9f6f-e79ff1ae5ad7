package scrbg.meplat.mall.dto.order;

import java.math.BigDecimal;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.vo.product.website.material.WMaterialBaseVo;

/**
 * <AUTHOR>
 * @create 2023-05-18 16:21
 */
@Data
public class ProductBuyInfoDTO  extends WMaterialBaseVo {

    @ApiModelProperty(value = "商品id")
    private String productId;

    @ApiModelProperty(value = "店铺名称")
    private String shopName;

    @ApiModelProperty(value = "数量")
    private BigDecimal cartNum;

    @ApiModelProperty(value = "计划id")
    private String BillId;

    @ApiModelProperty(value = "计划编号")
    private String BillNo;

    @ApiModelProperty(value = "计划明细id")
    private String DtlId;

    @ApiModelProperty(value = "销售价格")
    private BigDecimal sellPrice;
    
    @ApiModelProperty(value = "账期，下单时选择")
    private Integer paymentPeriod;
    /**
     * 冗余
     */

    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "店铺id")
    private String shopId;

    @ApiModelProperty(value = "支付方式 1线上支付 2内部结算 3线下转账", required = true)
    @NotNull(message = "支付方式不能为空！")
    private Integer payWay;

    @ApiModelProperty(value = "收货人",required = true)
    @NotEmpty(message = "收货人不能为空！")
    private String receiverName;

    @ApiModelProperty(value = "收货人手机号",required = true)
    @NotEmpty(message = "收货人手机号不能为空！")
    private String receiverMobile;

    @ApiModelProperty(value = "收货地址",required = true)
    @NotEmpty(message = "收货地址不能为空！")
    private String receiverAddress;


    @ApiModelProperty(value = "订单备注")
    private String orderRemark;


    /**
     * 关林计划表所需的字段
     */


    @ApiModelProperty(value = "供应商内部id")
    private String storageId;

    @ApiModelProperty(value = "供应商内部名称")
    private String storageName;

    @ApiModelProperty(value = "供应商内部机构id")
    private String storageOrgId;

    @ApiModelProperty(value = "机构简码")
    private String shortCode;


    @ApiModelProperty(value = "供应商信用代码")
    private String creditCode;


    @ApiModelProperty(value = "计划单价")

    private BigDecimal price;

    @ApiModelProperty(value = "计划总金额")

    private BigDecimal account;

    @ApiModelProperty(value = "类别路径Id")
    private String ClassId;

    @ApiModelProperty(value = "类别路径名称")
    private String ClassName;
}
