package scrbg.meplat.mall.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;
import scrbg.meplat.mall.entity.FileRecord;

/**
 * @描述：文件上传记录信息 Mapper 接口
 * @作者: ye
 * @日期: 2023-03-27
 */
@Mapper
@Repository
public interface FileRecordMapper extends BaseMapper<FileRecord> {

    @Delete("DELETE FROM file_record WHERE record_id=#{id}")
    void deleteByRecordId(String id);



}