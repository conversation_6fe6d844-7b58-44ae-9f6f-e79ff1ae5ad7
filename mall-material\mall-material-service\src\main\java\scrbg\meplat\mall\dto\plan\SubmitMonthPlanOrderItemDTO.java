package scrbg.meplat.mall.dto.plan;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @create 2023-07-03 16:14
 */
@Data
public class SubmitMonthPlanOrderItemDTO {

    @ApiModelProperty(value = "支付方式 1线上支付 2内部结算 3线下转账", required = true)
    @NotNull(message = "支付方式不能为空！")
    private Integer payWay;

    @ApiModelProperty(value = "收货人",required = true)
    @NotEmpty(message = "收货人不能为空！")
    private String receiverName;

    @ApiModelProperty(value = "收货人手机号",required = true)
    @NotEmpty(message = "收货人手机号不能为空！")
    private String receiverMobile;

    @ApiModelProperty(value = "收货地址",required = true)
    @NotEmpty(message = "收货地址不能为空！")
    private String receiverAddress;

    @ApiModelProperty(value = "订单备注")
    private String orderRemark;




    @ApiModelProperty(value = "计划id",required = true)
    @NotEmpty(message = "计划id不能为空！")
    private String planId;
    @ApiModelProperty(value = "计划编号",required = true)
    @NotEmpty(message = "计划编号不能为空！")
    private String planNo;
    @ApiModelProperty(value = "计划明细id",required = true)
    @NotEmpty(message = "计划明细id不能为空！")
    private String planDtlId;
    @ApiModelProperty(value = "物资id",required = true)
    @NotEmpty(message = "物资id不能为空！")
    private String materialId;
    @ApiModelProperty(value = "物资名称",required = true)
    @NotEmpty(message = "物资名称不能为空！")
    private String materialName;

    @ApiModelProperty(value = "分类路径名称（xxx/xxx/xxx）")
    private String classPathName;

    @ApiModelProperty(value = "分类路径id（xxx/xxx/xxx）")
    private String classPathId;
    @ApiModelProperty(value = "规格型号")
    private String spec;
    @ApiModelProperty(value = "计量单位")
    private String unit;
    @ApiModelProperty(value = "材质")
    private String texture;
    @ApiModelProperty(value = "选择数量",required = true)
    @NotNull(message = "选择数量不能为空！")
    private BigDecimal selectQty;
    @ApiModelProperty(value = "合同明细id",required = true)
    @NotEmpty(message = "合同明细id不能为空！")
    private String contractDtlId;

    @ApiModelProperty(value = "合同id",required = true)
    @NotEmpty(message = "合同id不能为空！")
    private String contractId;

    @ApiModelProperty(value = "合同编号",required = true)
    @NotEmpty(message = "合同编号不能为空！")
    private String contractNo;


    public BigDecimal getSelectQty() {
        if(selectQty != null) {
           return selectQty.setScale(4, RoundingMode.HALF_UP);
        }
        return selectQty;
    }


}
