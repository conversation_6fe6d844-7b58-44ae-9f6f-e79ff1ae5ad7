package scrbg.meplat.mall.common.TreeModel;

import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2022-11-09 14:02
 */
public class TreeMethod {

    /**
     * 根据列表生成树
     *
     * @param all
     * @return
     */
    private List<BaseTreeModel> getTree(List<BaseTreeModel> all) {
        List<BaseTreeModel> tree = new ArrayList<>();
        if (CollectionUtils.isEmpty(all)) return tree;
        tree = all.stream().filter(t -> StringUtils.isEmpty(t.getParentId()))
                .map(t -> {
                    t.setChildren(getChildren(t, all));
                    return t;
                })
                .sorted((t1, t2) ->
                        (t1.getSort() == null ? 0 : t1.getSort()) -
                                (t2.getSort() == null ? 0 : t2.getSort()))
                .collect(Collectors.toList());
        return tree;
    }

    /**
     * 递归查找所有菜单的子菜单（其他方法调用）
     *
     * @param root
     * @param all
     * @return
     */
    private List<BaseTreeModel> getChildren(BaseTreeModel root, List<BaseTreeModel> all) {
        List<BaseTreeModel> children = all.stream().
                filter(t -> Objects.equals(root.getId(),t.getParentId()))
                .map(t -> {
                    t.setChildren(getChildren(t, all));
                    return t;
                })
                .sorted((t1, t2) ->
                        (t1.getSort() == null ? 0 : t1.getSort()) -
                                (t2.getSort() == null ? 0 : t2.getSort()))
                .collect(Collectors.toList());
        return children;
    }


    // -----------------------------------------------------------------------------------------------------

    /**
     * 根据实体查找对应的父级并且展示为树
     *
     * @param all
     * @param queryEntities
     * @return
     */
    public List<BaseTreeModel> parentTreeByEntities(List<BaseTreeModel> all, List<BaseTreeModel> queryEntities) {
        // 将查询的父级和本身递归装入集合（未完成树）
        List<BaseTreeModel> list = new ArrayList<>();
        if(CollectionUtils.isEmpty(all) || CollectionUtils.isEmpty(queryEntities)) return list;
        for (BaseTreeModel entity : queryEntities) {
            list.add(entity);
            getChildParentAddList(list, entity, all);
        }
        // 去除多余的父级
        List<BaseTreeModel> lists = list.stream().collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getId() + ";" + o.getId()))), ArrayList::new
        ));
        // 组装树
        return getTree(lists);
    }


    /**
     * 递归查找父级，并且将父级添加到集合
     * @param list
     * @param child
     * @param all
     */
    public void getChildParentAddList(List<BaseTreeModel> list, BaseTreeModel child, List<BaseTreeModel> all) {
        if (StringUtils.isEmpty(child.getParentId())) {
            return;
        }
        BaseTreeModel parent = all.stream().filter(
                x -> Objects.equals(x.getId(), child.getParentId())
        ).findFirst().get();
        list.add(parent);
        getChildParentAddList(list, parent, all);
    }


    // -------------------------------------------------------------------------------------------------------------



}
