package scrbg.meplat.mall.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
/**
 * @描述：
 * @作者: ye
 * @日期: 2024-01-04
 */
@ApiModel(value="")
@Data
@TableName("operate_log")
public class OperateLog implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "操作记录id")
    private String operateLogId;

    @ApiModelProperty(value = "关联id")

    private String relevanceId;


    @ApiModelProperty(value = "关联类型（1:订单id）")

    private Integer relevanceType;


    @ApiModelProperty(value = "操作说明")

    private String explainInfo;


    @ApiModelProperty(value = "修改前数据")

    private String beforeUpdate;


    @ApiModelProperty(value = "修改后数据")

    private String afterUpdate;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;
    @ApiModelProperty(value = "更新时间")
    @TableField(value = "gmt_modified", fill = FieldFill.INSERT_UPDATE)
    private Date gmtModified;

    @ApiModelProperty(value = "创建人名称")
    @TableField(value = "founder_name", fill = FieldFill.INSERT)
    private String founderName;

    @ApiModelProperty(value = "创建人Id")
    @TableField(value = "founder_id", fill = FieldFill.INSERT)
    private String founderId;


    @ApiModelProperty(value = "逻辑删除 -1: 删除 0:未删除")
    @TableField(value = "is_delete", fill = FieldFill.INSERT)
    private Integer isDelete;








}