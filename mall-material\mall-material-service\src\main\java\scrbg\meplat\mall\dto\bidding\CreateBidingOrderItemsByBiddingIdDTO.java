package scrbg.meplat.mall.dto.bidding;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.OrderItem;
import scrbg.meplat.mall.entity.SynthesizeTemporaryDtl;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-07-21 10:59
 */
@Data
public class CreateBidingOrderItemsByBiddingIdDTO {

    @ApiModelProperty(value = "竞价采购id",required = true)
    @NotEmpty
    private String biddingId;

    @ApiModelProperty(value = "竞价采购编号",required = true)
    @NotEmpty
    private String biddingSn;

    @ApiModelProperty(value = "订单信息")
    private List<OrderItem> orderItems;
    @ApiModelProperty(value = "清单明细")
    private List<SynthesizeTemporaryDtl> synthesizeTemporaryDtls;

}
