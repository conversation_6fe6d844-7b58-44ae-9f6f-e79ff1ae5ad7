package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
import scrbg.meplat.mall.vo.product.website.IndexMaterialVO;

import java.io.Serializable;

/**
 * @描述：楼层显示的商品
 * @作者: y
 * @日期: 2022-11-13
 */
@ApiModel(value = "楼层显示的商品")
@Data
@TableName("floor_goods")
public class FloorGoods extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "楼层商品id")

    private String floorGoodsId;

    @ApiModelProperty(value = "商品排序值")

    private Integer orderValue;

    @ApiModelProperty(value = "显示楼层id")

    private String floorId;

    @ApiModelProperty(value = "显示的商品id")

    private String goodsId;

    @ApiModelProperty(value = "图片地址")

    private String goodsPictureUrl;


    @ApiModelProperty(value = "商品状态（1：发布（显示）  0：未发布（不显示））")

    private Integer state;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @TableField(
            exist = false
    )
    @ApiModelProperty(hidden = true)
    private IndexMaterialVO  vo;

}
