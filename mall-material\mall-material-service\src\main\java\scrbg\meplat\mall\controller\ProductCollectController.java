package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.entity.Product;
import scrbg.meplat.mall.entity.ProductCollect;
import scrbg.meplat.mall.service.ProductCollectService;

import java.util.List;

/**
 * @描述：个人商品收藏控制类
 * @作者: sund
 * @日期: 2022-11-10
 */
@RestController
@RequestMapping("/productCollect")
@Api(tags = "个人商品收藏")
public class ProductCollectController {

    @Autowired
    public ProductCollectService productCollectService;

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<ProductCollect> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = productCollectService.queryPage(jsonObject, new LambdaQueryWrapper<ProductCollect>());
        return PageR.success(page);
    }

    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<ProductCollect> findById(String id) {
        ProductCollect productCollect = productCollectService.getById(id);
        return R.success(productCollect);
    }

    @PostMapping("/createByUserId")
    @ApiOperation(value = "根据userId新增")
    public R save(@RequestBody ProductCollect productCollect) {
        productCollectService.create(productCollect);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody ProductCollect productCollect) {
        productCollectService.update(productCollect);
        return R.success();
    }

    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        productCollectService.delete(id);
        return R.success();
    }


    @PostMapping("/addBatch")
    @ApiOperation(value = "根据商品主键批量添加")
    public R addBatch(@RequestBody List<String> productIds) {
        productCollectService.addBatch(productIds);
        return R.success();
    }

    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")

    public R deleteBatch(@RequestBody List<String> ids) {
        productCollectService.removeByIds(ids);
        return R.success();
    }

    @PostMapping("/createByProductId")
    @ApiOperation(value = "根据商品id新增")
    public R saveProductCollect(@RequestBody ProductCollect productCollect) {
        R result = productCollectService.addProductCollect(productCollect);
        return result;
    }
    @PostMapping("/selCollectData")
    @ApiOperation(value = " 根据当前用户查询数量查询商品的关注信息")
    public R selUserNum(@RequestBody JSONObject jsonObject) {
       List<Product> list= productCollectService.selUserColloctNum(jsonObject);
        return R.success(list);
    }
    @PostMapping ("/isCollect")
    @ApiOperation(value = "判断是否收藏")
    public R isCollect(@RequestBody ProductCollect productCollect) {
      Integer  isCollect =productCollectService.isCollect(productCollect);
       if (isCollect==0){
          return R.success(isCollect,"未收藏");
      }else {
          return R.success(isCollect,"收藏");
      }

    }
    @PostMapping("/getCollectNum")
    @ApiOperation(value = "根据当前用户id和收集类型查询商品关注数量")
    public R getCollectNum(@RequestBody ProductCollect productCollect) {
        Integer collectNum= productCollectService.getCollectNum(productCollect);
        return R.success(collectNum);
    }
}

