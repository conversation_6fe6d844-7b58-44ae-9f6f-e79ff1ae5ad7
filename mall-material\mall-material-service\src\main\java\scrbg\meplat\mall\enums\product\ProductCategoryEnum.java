package scrbg.meplat.mall.enums.product;

public enum ProductCategoryEnum {
    TYPE_OPEN(1,"启用"),
    TYPE_STOP(0,"停用"),

    IS_HAVE_PRODUCT_NO(0,"没有"),
    IS_HAVE_PRODUCT_YES(1,"有");


    /**
     * 编码
     */
    private int code;

    /**
     * 说明
     */
    private String remark;

    ProductCategoryEnum() {
    }

    ProductCategoryEnum(int code, String remark) {
        this.code = code;
        this.remark = remark;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
    }

