package scrbg.meplat.mall.entity;

import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
/**
 * @描述：异常信息记录表
 * @作者: ye
 * @日期: 2023-10-25
 */
@ApiModel(value="异常信息记录表")
@Data
@TableName("error_info")
public class ErrorInfo implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "异常id")
    private String errorInfoId;

    @ApiModelProperty(value = "异常业务类型（1pcwp和商城分类不统一）")

    private Integer businessType;


    @ApiModelProperty(value = "错误接口请求JSON")

    private String errorRqJson;


    @ApiModelProperty(value = "创建时间")

    private Date createTime;


    @ApiModelProperty(value = "是否已解决")

    private Integer isDispose;


    @ApiModelProperty(value = "错误方法名称")

    private String methodName;


    @ApiModelProperty(value = "错误信息")

    private String errorInfo;


    @ApiModelProperty(value = "用户id")

    private String userId;


    @ApiModelProperty(value = "用户名称")

    private String userName;



}