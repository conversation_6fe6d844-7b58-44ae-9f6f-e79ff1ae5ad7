package scrbg.meplat.mall.entity;

import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
/**
 * @描述：机构表
 * @作者: ye
 * @日期: 2024-04-30
 */
@ApiModel(value="机构表")
@Data
@TableName("sys_org")
public class SysOrg extends MustBaseEntity implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String orgId;

    @ApiModelProperty(value = "机构编码")

    private String shortCode;


    @ApiModelProperty(value = "机构名称")

    private String orgName;


    @ApiModelProperty(value = "机构类型（1集团2分子公司3经理部4项目部5股份6事业部）")

    private Integer orgType;


    @ApiModelProperty(value = "父机构id")

    private String pid;


    @ApiModelProperty(value = "父机构名称")

    private String pName;


    @ApiModelProperty(value = "状态(1:正常；0:弃用)")

    private Integer state;


    @ApiModelProperty(value = "负责人user_id")

    private String orgUserId;



    @ApiModelProperty(value = "负责人名称")

    private String userName;


    @ApiModelProperty(value = "负责人联系电话")

    private String userPhone;

















}