package scrbg.meplat.mall.dto.plan;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.MaterialMonthSupplyPlan;
import scrbg.meplat.mall.entity.MaterialMonthSupplyPlanDtl;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-06-29 9:04
 */
@Data
public class MaterialMonthSupplyPlanDTO extends MaterialMonthSupplyPlan {


    @ApiModelProperty(value = "是否提交")
    private Integer isSubmit;

    @ApiModelProperty(value = "计划明细")
    private List<MaterialMonthSupplyPlanDtl> dtls;
}
