package scrbg.meplat.mall.controller;//package scrbg.meplat.mall.controller;
//import com.alibaba.fastjson.JSONObject;
//import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
//import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
//import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiImplicitParam;
//import io.swagger.annotations.ApiImplicitParams;
//import io.swagger.annotations.ApiOperation;
//import com.scrbg.common.utils.PageR;
//import com.scrbg.common.utils.PageUtils;
//import com.scrbg.common.utils.R;
//import scrbg.meplat.mall.service.FileRecordService;
//import scrbg.meplat.mall.entity.FileRecord;
//
//import java.util.List;
//
///**
// * @描述：文件上传记录信息控制类
// * @作者: ye
// * @日期: 2023-03-27
// */
//@RestController
//@RequestMapping("/fileRecord")
//@Api(tags = "文件上传记录信息")
//public class FileRecordController{
//
//@Autowired
//public FileRecordService fileRecordService;
//
//@PostMapping("/listByEntity")
//@ApiOperation(value = "根据实体属性分页查询")
//@DynamicParameters(name = "根据实体属性分页查询", properties = {
//        @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
//        @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
//})
//public PageR<FileRecord> listByEntity(@RequestBody JSONObject jsonObject){
//        PageUtils page= fileRecordService.queryPage(jsonObject,new LambdaQueryWrapper<FileRecord>());
//        return PageR.success(page);
//        }
//
//@GetMapping("/findById")
//@ApiOperation(value = "根据主键查询")
//@ApiImplicitParams({
//        @ApiImplicitParam(name = "id", value = "ID", required = true,
//                dataType = "String", paramType = "query")
//})
//public R<FileRecord> findById(String id){
//    FileRecord fileRecord = fileRecordService.getById(id);
//        return R.success(fileRecord);
//        }
//
//@PostMapping("/create")
//@ApiOperation(value = "新增")
//public R save(@RequestBody FileRecord fileRecord){
//    fileRecordService.create(fileRecord);
//        return R.success();
//        }
//
//@PostMapping("/update")
//@ApiOperation(value = "修改")
//public R update(@RequestBody FileRecord fileRecord){
//    fileRecordService.update(fileRecord);
//        return R.success();
//        }
//
//@GetMapping("/delete")
//@ApiOperation(value = "根据主键删除")
//@ApiImplicitParams({
//        @ApiImplicitParam(name = "id", value = "ID", required = true,
//                dataType = "String", paramType = "query")
//})
//public R delete(String id){
//    fileRecordService.delete(id);
//        return R.success();
//        }
//
//
//@PostMapping("/deleteBatch")
//@ApiOperation(value = "根据主键批量删除")
//public R deleteBatch(@RequestBody List<String> ids){
//    fileRecordService.removeByIds(ids);
//        return R.success();
//        }
//        }
//
