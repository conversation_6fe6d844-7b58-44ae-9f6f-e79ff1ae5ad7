package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import scrbg.meplat.mall.entity.PlatformDealFeeRecord;
import scrbg.meplat.mall.service.PlatformDealFeeService;
import scrbg.meplat.mall.entity.PlatformDealFee;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;

import java.util.List;

/**
 * @描述：平台交易费控制类
 * @作者: ye
 * @日期: 2024-01-24
 */
@RestController
@RequestMapping("/")
@Api(tags = "平台交易费")
public class PlatformDealFeeController {

    @Autowired
    public PlatformDealFeeService platformDealFeeService;

    @PostMapping("supplier/platformDealFee/supplierListByEntity")
    @ApiOperation(value = "供应商查询交易记录")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<PlatformDealFee> supplierListByEntity(@RequestBody JSONObject jsonObject) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        LambdaQueryWrapper<PlatformDealFee> q = new LambdaQueryWrapper<>();
        q.eq(PlatformDealFee::getEnterpriseId, user.getEnterpriseId());
        PageUtils page = platformDealFeeService.listByEntity(jsonObject, q);
        return PageR.success(page);
    }

    @PostMapping("platform/platformDealFee/listByEntity")
    @ApiOperation(value = "供应商查询交易记录")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<PlatformDealFee> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = platformDealFeeService.listByEntity(jsonObject, new LambdaQueryWrapper<PlatformDealFee>());
        return PageR.success(page);
    }

    @GetMapping("platformDealFee/findBySn")
    @ApiOperation(value = "根据编号获取数据")
    @ApiImplicitParams({@ApiImplicitParam(name = "sn", value = "SN", required = true,
            dataType = "String", paramType = "query")
    })
    public R<PlatformDealFee> findBySn(String sn) {
        PlatformDealFee platformDealFee = platformDealFeeService.findBySn(sn);
        return R.success(platformDealFee);
    }

//    @GetMapping("/findById")
//    @ApiOperation(value = "根据主键查询")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "id", value = "ID", required = true,
//                    dataType = "String", paramType = "query")
//    })
//    public R<PlatformDealFee> findById(String id) {
//        PlatformDealFee platformDealFee = platformDealFeeService.getById(id);
//        return R.success(platformDealFee);
//    }
//
//    @PostMapping("/create")
//    @ApiOperation(value = "新增")
//    public R save(@RequestBody PlatformDealFee platformDealFee) {
//        platformDealFeeService.create(platformDealFee);
//        return R.success();
//    }
//
//    @PostMapping("/update")
//    @ApiOperation(value = "修改")
//    public R update(@RequestBody PlatformDealFee platformDealFee) {
//        platformDealFeeService.update(platformDealFee);
//        return R.success();
//    }
//
//    @GetMapping("/delete")
//    @ApiOperation(value = "根据主键删除")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "id", value = "ID", required = true,
//                    dataType = "String", paramType = "query")
//    })
//    public R delete(String id) {
//        platformDealFeeService.delete(id);
//        return R.success();
//    }
//
//
//    @PostMapping("/deleteBatch")
//    @ApiOperation(value = "根据主键批量删除")
//    public R deleteBatch(@RequestBody List<String> ids) {
//        platformDealFeeService.removeByIds(ids);
//        return R.success();
//    }
}

