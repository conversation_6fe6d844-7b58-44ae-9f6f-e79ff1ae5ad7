package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;
/**
 * @描述：个人商品收藏
 * @作者: y
 * @日期: 2022-11-29
 */
@ApiModel(value="个人商品收藏")
@Data
@TableName("product_collect")
public class ProductCollect extends MustBaseEntity implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "收藏id")
    private String collectId;

    @ApiModelProperty(value = "用户id")
    private String userId;

    @ApiModelProperty(value = "关注id（商品id,店铺id）")
    private String productId;

    @ApiModelProperty(value = "关注类型 ( 1:商品  2：店铺 )")
    private Integer collectType;

    @ApiModelProperty(value = "商品类型：0物资 1设备 2周材（设备租赁） 3周材（设备） 4二手设备 5租赁设备")
    private Integer productType;

    @ApiModelProperty(value = "状态（1：启用 0： 停用）")
    private Integer state;


}
