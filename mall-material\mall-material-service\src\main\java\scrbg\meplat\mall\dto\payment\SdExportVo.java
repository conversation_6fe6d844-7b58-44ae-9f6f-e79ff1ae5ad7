package scrbg.meplat.mall.dto.payment;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @program: maill_api
 * @description: 蜀道企业导出
 * @author: 代文翰
 * @create: 2023-09-07 11:09
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@HeadRowHeight(40)
@ColumnWidth(40)
@ContentRowHeight(30)
@ContentFontStyle(fontHeightInPoints=16)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
public class SdExportVo implements Serializable {
    private static final long serialVersionUID = 1L;
    @ExcelProperty(value = "企业名称")
    @ColumnWidth(50)
    private String enterpriseName;

    @ExcelProperty(value = "隶属企业")
    @ColumnWidth(50)
    private String affiliationEnterprise;
    @ExcelProperty(value = "企业类别(1:一类 2:二类 3:三类)")
    @ColumnWidth(80)

    private String enterpriseCategory;
    @ExcelProperty(value = "调整")
    @ColumnWidth(50)

    private String adjust;
    @ExcelProperty(value = "企业性质")
    @ColumnWidth(20)

    private String enterpriseNature;
}
