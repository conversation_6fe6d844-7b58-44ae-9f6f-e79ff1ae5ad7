package scrbg.meplat.mall.entity.plan;

import java.io.Serializable;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MallBaseEntity;

/**
 * @描述：采购计划明细表
 * @作者: tanfei
 * @日期: 2025-05-27
 */
@ApiModel(value = "采购计划明细表")
@Data
@TableName("plan_detail")

public class PlanDetail extends MallBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "计划明细ID")
    private String dtlId;

    @ApiModelProperty(value = "计划ID")
    private String billId;

    @ApiModelProperty(value = "PCWP计划明细ID")
    private String pDtlId;

    @ApiModelProperty(value = "物料ID")
    private String materialId;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "规格")
    private String spec;

    @ApiModelProperty(value = "材质")
    private String texture;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "物料用途")
    private String materialUse;

    @ApiModelProperty(value = "物料分类ID")
    private String materialClassId;

    @ApiModelProperty(value = "物料分类名称")
    private String materialClassName;

    @ApiModelProperty(value = "顶级分类ID")
    private String topClassId;

    @ApiModelProperty(value = "顶级分类名称")
    private String topClassName;

    @ApiModelProperty(value = "商品ID")
    private String tradeId;

    @ApiModelProperty(value = "商品名称")
    private String tradeName;

    @ApiModelProperty(value = "数量")
    private Integer quantity;

    @ApiModelProperty(value = "单价(不含税)")
    private BigDecimal price;

    @ApiModelProperty(value = "含税单价")
    private BigDecimal taxPrice;

    @ApiModelProperty(value = "金额")
    private BigDecimal amount;
    
    @ApiModelProperty(value = "账期")
    private Integer paymentPeriod;

    @ApiModelProperty(value = "税额")
    private BigDecimal taxAmount;

    @ApiModelProperty(value = "已消耗金额")
    private BigDecimal consumeAmount;

    @ApiModelProperty(value = "未消耗金额")
    private BigDecimal notConsumeAmount;

    @ApiModelProperty(value = "已消耗数量")
    private Integer consumeNumber;

    @ApiModelProperty(value = "未消耗数量")
    private Integer notConsumeNumber;

    @ApiModelProperty(value = "店铺ID")
    private String shopId;

    @ApiModelProperty(value = "店铺名称")
    private String shopName;

    @ApiModelProperty(value = "供应商ID")
    private String storageId;

    @ApiModelProperty(value = "供应商名称")
    private String storageName;

    @ApiModelProperty(value = "供应商机构简码")
    private String orgShort;

    @ApiModelProperty(value = "供应商组织机构ID(PCWP组织机构id)")
    private String storageOrgId;

    @ApiModelProperty(value = "统一社会信用代码")
    private String creditCode;

    @ApiModelProperty(value = "乐观锁")
    private Integer version;
}