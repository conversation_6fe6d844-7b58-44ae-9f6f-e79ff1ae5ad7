package scrbg.meplat.mall.dto.product;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-05-07 17:45
 */
@Data
public class CheckIsPutawayPDTO {

    @Valid
    @ApiModelProperty(value = "商品",required = true)
    @NotNull(message = "商品不能为空！")
    private List<CheckIsPutawayDTO> products;

    @ApiModelProperty(value = "统一社会信用代码",required = true)
    @NotEmpty(message = "统一社会信用代码不能为空！")
    private String socialCreditCode;
    @ApiModelProperty(value = "合同类型（7采购合同，8租赁合同）",required = true)
    @NotNull(message = "合同类型不能为空！")
    private Integer type;


}
