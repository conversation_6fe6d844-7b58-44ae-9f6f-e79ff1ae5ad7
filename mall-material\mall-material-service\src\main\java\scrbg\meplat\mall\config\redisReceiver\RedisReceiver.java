package scrbg.meplat.mall.config.redisReceiver;//package scrbg.meplat.mall.config.redisReceiver;
//
//import org.springframework.stereotype.Service;
//
///**
// * <AUTHOR>
// * @create 2022-12-06 10:26
// */
//@Service
//public class RedisReceiver {
//
//    public void receiveMessage(String message) {
//        System.out.println("接收消息：" + message);
//    }
//    public void receiveMessage2(String message) {
//        System.out.println("接收消息2：" + message);
//    }
//}
