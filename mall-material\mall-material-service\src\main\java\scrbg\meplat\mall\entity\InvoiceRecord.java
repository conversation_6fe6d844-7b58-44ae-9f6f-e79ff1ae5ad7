package scrbg.meplat.mall.entity;

import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
/**
 * @描述：
 * @作者: ye
 * @日期: 2023-11-16
 */
@ApiModel(value="")
@Data
@TableName("invoice_record")
public class InvoiceRecord extends MustBaseEntity implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "发票抬头id")
    private String invoiceRecordId;

    @ApiModelProperty(value = "采购单位id")

    private String enterpriseId;


    @ApiModelProperty(value = "0 未选择 1默认")

    private Integer state;


    @ApiModelProperty(value = "0：增值税专用发票、1增值税普通发票")

    private Integer invoiceType;


    @ApiModelProperty(value = "0:个人 1:单位")

    private Integer riseType;


    @ApiModelProperty(value = "收票人姓名")

    private String userName;


    @ApiModelProperty(value = "收票人地址")

    private String userAddress;


    @ApiModelProperty(value = "收票人联系电话")

    private String userPhone;


    @ApiModelProperty(value = "收票人邮箱")

    private String email;


    @ApiModelProperty(value = "单位名称")

    private String company;


    @ApiModelProperty(value = "单位税号")

    private String dutyParagraph;


    @ApiModelProperty(value = "注册地址")

    private String registerAddress;


    @ApiModelProperty(value = "注册电话")

    private String registerPhone;


    @ApiModelProperty(value = "开户银行")

    private String bank;


    @ApiModelProperty(value = "银行账号")

    private String bankAccount;
    @ApiModelProperty(value = "省")
    private String province;

    @ApiModelProperty(value = "市")
    private String city;

    @ApiModelProperty(value = "县、区")
    private String county;


    @ApiModelProperty(value = "省")
    private String userProvince;

    @ApiModelProperty(value = "市")
    private String userCity;

    @ApiModelProperty(value = "县、区")
    private String userCounty;

    @ApiModelProperty(value = "用户类型 1供应商 0项目部")
    private Integer userType;

















}
