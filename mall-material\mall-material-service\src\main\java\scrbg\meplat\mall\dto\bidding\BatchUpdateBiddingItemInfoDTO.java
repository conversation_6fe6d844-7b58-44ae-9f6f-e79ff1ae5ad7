package scrbg.meplat.mall.dto.bidding;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2023-07-21 13:48
 */
@Data
public class BatchUpdateBiddingItemInfoDTO {

    @ApiModelProperty(value = "竞价采购商品id")

    private String biddingProductId;

    @ApiModelProperty(value = "竞价id")

    private String biddingId;

    @ApiModelProperty(value = "商品名称")

    private String productName;


    @ApiModelProperty(value = "规格型号")

    private String spec;


    @ApiModelProperty(value = "计量单位")

    private String unit;


    @ApiModelProperty(value = "商品材质")

    private String productTexture;


    @ApiModelProperty(value = "备注")

    private String remarks;

    @ApiModelProperty(value = "送货时间")

    private Date deliveryDate;


    @ApiModelProperty(value = "送货地址")

    private String deliveryAddress;

    @ApiModelProperty(value = "最高价")
    private BigDecimal referencePrice;
    @ApiModelProperty(value = "网价（浮动报价使用）")
    private BigDecimal netPrice;
}
