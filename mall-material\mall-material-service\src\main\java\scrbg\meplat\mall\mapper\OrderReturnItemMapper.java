package scrbg.meplat.mall.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.ibatis.annotations.Param;
import scrbg.meplat.mall.entity.OrderReturnItem;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;
import org.apache.ibatis.annotations.Mapper;

import java.math.BigDecimal;
import java.util.List;

/**
 * @描述：退货项表 Mapper 接口
 * @作者: ye
 * @日期: 2023-08-07
 */
@Mapper
@Repository
public interface OrderReturnItemMapper extends BaseMapper<OrderReturnItem> {

    List<OrderReturnItem> selectRetuenList(@Param("ew") QueryWrapper<OrderReturnItem> q);

    BigDecimal selectCounts(@Param("ew") QueryWrapper<OrderReturnItem> q);
}
