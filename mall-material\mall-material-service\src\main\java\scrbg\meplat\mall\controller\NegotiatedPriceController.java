package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.entity.NegotiatedPrice;
import scrbg.meplat.mall.service.NegotiatedPriceService;
import scrbg.meplat.mall.vo.user.userCenter.NegotiatedPriceVo;

import java.util.List;

/**
 * @描述：报价控制类
 * @作者: y
 * @日期: 2022-11-22
 */
@RestController
@RequestMapping("/negotiatedPrice")
@Api(tags = "报价")
public class NegotiatedPriceController {

    @Autowired
    public NegotiatedPriceService negotiatedPriceService;

    @PostMapping("/listByParameters")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字（标题，发件人名称）", dataTypeClass = String.class),
            @DynamicParameter(name = "relevanceId", value = "关联id", dataTypeClass = String.class),
            @DynamicParameter(name = "type", value = "信息类型（0需求）", dataTypeClass = Integer.class)
    })
    public PageR<NegotiatedPrice> listByParameters(@RequestBody JSONObject jsonObject) {
        PageUtils page = negotiatedPriceService.queryPage(jsonObject, new LambdaQueryWrapper<NegotiatedPrice>());
        return PageR.success(page);
    }
    @PostMapping("/listByUser")
    @ApiOperation(value = "查询需求的全部报价报价")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字（标题，发件人名称）", dataTypeClass = String.class),
            @DynamicParameter(name = "relevanceId", value = "关联id", dataTypeClass = String.class),
            @DynamicParameter(name = "type", value = "信息类型（0需求）", dataTypeClass = Integer.class)
    })
    public PageR<NegotiatedPriceVo> listByUser(@RequestBody JSONObject jsonObject) {
        PageUtils page = negotiatedPriceService.listByUser(jsonObject, new QueryWrapper<NegotiatedPriceVo>());
        return PageR.success(page);
    }
    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<NegotiatedPrice> findById(String id) {
        NegotiatedPrice negotiatedPrice = negotiatedPriceService.getById(id);
        return R.success(negotiatedPrice);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody NegotiatedPrice negotiatedPrice) {
        negotiatedPriceService.create(negotiatedPrice);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody NegotiatedPrice negotiatedPrice) {
        negotiatedPriceService.update(negotiatedPrice);
        return R.success();
    }

    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        negotiatedPriceService.delete(id);
        return R.success();
    }


    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")

    public R deleteBatch(@RequestBody List<String> ids) {
        negotiatedPriceService.removeByIds(ids);
        return R.success();
    }
}

