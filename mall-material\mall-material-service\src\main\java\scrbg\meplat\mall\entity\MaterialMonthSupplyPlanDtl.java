package scrbg.meplat.mall.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
/**
 * @描述：计划明细
 * @作者: ye
 * @日期: 2023-06-27
 */
@ApiModel(value="计划明细")
@Data
@TableName("material_month_supply_plan_dtl")
public class MaterialMonthSupplyPlanDtl extends MustBaseEntity implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "计划明细id")
    private String planDtlId;

    @ApiModelProperty(value = "计划id")
    private String planId;

    @ApiModelProperty(value = "物资id")

    private String materialId;
    @ApiModelProperty(value = "分类路径名称（xxx/xxx/xxx）")
    private String classPathName;

    @ApiModelProperty(value = "分类路径id（xxx/xxx/xxx）")
    private String classPathId;

    @ApiModelProperty(value = "物资名称")

    private String materialName;


    @ApiModelProperty(value = "规格型号")

    private String spec;


    @ApiModelProperty(value = "计量单位")

    private String unit;


    @ApiModelProperty(value = "材质")

    private String texture;


    @ApiModelProperty(value = "本期计划数量")

    private BigDecimal thisPlanQty;
    @ApiModelProperty(value = "完结计划回归数量")

    private BigDecimal closeQty;


    @ApiModelProperty(value = "计划明细总数量")

    private BigDecimal sourceQty;


    @ApiModelProperty(value = "计划状态（0草稿1待审核2通过3未通过4已作废）冗余状态，为了统计使用")

    private Integer state;


    @ApiModelProperty(value = "合同明细id")

    private String contractDtlId;

    @ApiModelProperty(value = "已下单数量")

    private BigDecimal orderQty;

    @ApiModelProperty(value = "二级供应商id")

    private String twoSupplierId;

    @ApiModelProperty(value = "二级供应商名称")

    private String twoSupplierName;



    //乐观锁
    @Version
    private Integer version;











}
