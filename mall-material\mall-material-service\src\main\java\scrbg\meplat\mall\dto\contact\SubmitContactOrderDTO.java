package scrbg.meplat.mall.dto.contact;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2023-04-21 9:29
 */
@Data
public class SubmitContactOrderDTO {

    @ApiModelProperty(value = "订单项id")

    private String orderItemId;

    @ApiModelProperty(value = "订单id")

    private String orderId;

    @ApiModelProperty(value = "订单号")

    private String orderSn;

    @ApiModelProperty(value = "商品id")

    private String productId;

    @ApiModelProperty(value = "商品编号")

    private String productSn;

    @ApiModelProperty(value = "商品名称")

    private String productName;

    @ApiModelProperty(value = "商品图片")

    private String productImg;

    @ApiModelProperty(value = "skuid")

    private String skuId;

    @ApiModelProperty(value = "sku名称")

    private String skuName;

    @ApiModelProperty(value = "商品成本价")

    private BigDecimal costPrice;

    @ApiModelProperty(value = "商品价格")

    private BigDecimal productPrice;

    @ApiModelProperty(value = "购买数量")

    private BigDecimal buyCounts;

    @ApiModelProperty(value = "商品金额")

    private BigDecimal totalAmount;

    @ApiModelProperty(value = "关联名称（用于关联外部的名称唯一不修改）")

    private String relevanceName;

    @ApiModelProperty(value = "商品类型：0物资 1设备 2周材（设备租赁） 3周材（设备） 4二手设备 5租赁设备")
    private Integer productType;



    @ApiModelProperty(value = "店铺id")

    private String shopId;

    @ApiModelProperty(value = "供应商名称")

    private String orgName;

    @ApiModelProperty(value = "统一社会信用代码")

    private String socialCreditCode;
}
