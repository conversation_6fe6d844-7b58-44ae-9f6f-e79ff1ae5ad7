package scrbg.meplat.mall.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import scrbg.meplat.mall.entity.SupplierReconciliation;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;
import org.apache.ibatis.annotations.Mapper;

import java.util.HashMap;
import java.util.List;

/**
 * @描述：物资验收 Mapper 接口
 * @作者: ye
 * @日期: 2023-08-15
 */
@Mapper
@Repository
public interface SupplierReconciliationMapper extends BaseMapper<SupplierReconciliation> {

}
