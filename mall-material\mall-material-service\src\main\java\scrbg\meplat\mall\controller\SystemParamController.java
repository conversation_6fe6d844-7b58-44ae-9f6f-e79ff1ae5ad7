package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.entity.SystemParam;
import scrbg.meplat.mall.service.SystemParamService;
import scrbg.meplat.mall.vo.system.SystemInitVo;

import java.util.ArrayList;
import java.util.List;

/**
 * @描述：系统参数表控制类
 * @作者: sund
 * @日期: 2022-12-05
 */
@RestController
@RequestMapping("/platform/systemParam")
@Api(tags = "系统参数表")
public class SystemParamController {


    //可物资的字典code
    public static final String[] mcodessDic = {"materialCity", "materialUnit"};
    //可新增物资的字典名称
    public static final String[] mnamesDic = {"城市", "计量单位"};


    //可新增设备字典code
    public static final String[] dcodessDic = {"deviceCity", "deviceUnit", "deviceQuality", "financeFirm", "insuranceFirm"};
    //可新增设备的字典名称
    public static final String[] dnamesDic = {"城市", "计量单位", "二手装备成色", "金融公司", "保险公司"};

    @Autowired
    public SystemParamService systemParamService;

    @Autowired
    private MallConfig mallConfig;

    @PostMapping("/listByEntitySys")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<SystemParam> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = systemParamService.queryPage(jsonObject, new LambdaQueryWrapper<>());
        return PageR.success(page);
    }

    @GetMapping("/getListSystemInitVos")
    @ApiOperation(value = "查询可添加的参数")
    public R getListSystemInitVos() {
        List<SystemInitVo> mvos = new ArrayList<>();
        if (mallConfig.mallType == 0) {
            for (int i = 0; i < mcodessDic.length; i++) {
                SystemInitVo vo = new SystemInitVo();
                vo.setLabel(mnamesDic[i]);
                vo.setValue(mcodessDic[i]);
                mvos.add(vo);
            }
        } else {
            for (int i = 0; i < dcodessDic.length; i++) {
                SystemInitVo vo = new SystemInitVo();
                vo.setLabel(dnamesDic[i]);
                vo.setValue(dcodessDic[i]);
                mvos.add(vo);
            }
        }
        return R.success(mvos);

    }

    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<SystemParam> findById(String id) {
        SystemParam systemParam = systemParamService.getById(id);
        return R.success(systemParam);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody SystemParam systemParam) {
        systemParamService.create(systemParam);
        return R.success();
    }

    @PostMapping("/createSysParam")
    @ApiOperation(value = "新增")
    public R add(@RequestBody SystemParam systemParam) {
        systemParamService.createSysParam(systemParam);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody SystemParam systemParam) {

        systemParamService.update(systemParam);
        return R.success();
    }

    @PostMapping("/updateByBatch")
    @ApiOperation(value = "批量修改")
    public R updateByBatch(@RequestBody List<SystemParam> systemParams) {

        systemParamService.saveOrUpdateBatch(systemParams);
        return R.success();
    }

    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        systemParamService.delete(id);
        return R.success();
    }

    @GetMapping("/batchDelete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R batchDelete(List<String> ids) {
        systemParamService.batchDelete(ids);
        return R.success();
    }


    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")

    public R deleteBatch(@RequestBody List<String> ids) {
        systemParamService.removeByIds(ids);
        return R.success();
    }

    @GetMapping("/listByCode")
    @ApiOperation(value = "根据编码获取参数列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编码", required = true,
                    dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "size", value = "数量", required = true,
                    dataType = "Integer", paramType = "query")
    })
    public R<List<SystemParam>> listByCode(String code, Integer size) {
        List<SystemParam> systemParam = systemParamService.listByCode(code, size);
        return R.success(systemParam);
    }


}

