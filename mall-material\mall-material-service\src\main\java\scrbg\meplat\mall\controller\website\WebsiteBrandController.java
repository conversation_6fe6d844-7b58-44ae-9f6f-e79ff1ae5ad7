package scrbg.meplat.mall.controller.website;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.entity.Brand;
import scrbg.meplat.mall.service.BrandService;
import scrbg.meplat.mall.vo.product.website.WBrandVO;

import java.util.List;

@RestController
@RequestMapping("/w/brand")
@ApiSort(value = 100)
@Api(tags = "品牌（前台）")
public class WebsiteBrandController {

    @Autowired
    public BrandService brandService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @PostMapping("/pageList")
    @ApiOperation(value = "根据类型获取品牌列表")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "productType", value = "品来类型", dataTypeClass = Integer.class),
            @DynamicParameter(name = "classId", value = "分类Id", dataTypeClass = String.class),
    })
    public PageR<WBrandVO> pageList(@RequestBody JSONObject jsonObject) {
        PageUtils page = brandService.pageList(jsonObject, new QueryWrapper<>());
        return PageR.success(page);
    }

    @GetMapping("/selectAllByClassId")
    @ApiOperation(value = "根据根据分类id查询品牌集合")
    public List<Brand> selListByClassId( String classId) {
        List<Brand> list= brandService.selectAllByClassId(classId);
        return list;
    }
//        stringRedisTemplate.boundHashOps("dsaf").put("111", JSON.toJSONString(page.getList()));

}

