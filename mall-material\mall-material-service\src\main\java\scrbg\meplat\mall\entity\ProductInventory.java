package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;

/**
 * @描述：商品库
 * @作者: y
 * @日期: 2022-11-13
 */
@ApiModel(value = "商品库")
@Data
@TableName("product_inventory")
public class ProductInventory extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "商品库存id")

    private String productInventoryId;

    @ApiModelProperty(value = "商品名称")

    private String productName;

    @ApiModelProperty(value = "类别id")

    private String classId;

    @ApiModelProperty(value = "类别名称")

    private String className;

    @ApiModelProperty(value = "类别名称路径")

    private String classNamePath;

    @ApiModelProperty(value = "商品描述")

    private String productDescribe;

    @ApiModelProperty(value = "规格")

    private String spec;

    @ApiModelProperty(value = "计量单位")

    private String unit;

    @ApiModelProperty(value = "关联外部id")

    private String relevanceId;

    @ApiModelProperty(value = "状态（1启用0停用）")

    private Integer state;

    @ApiModelProperty(value = "商品类型：0物资 1设备 2周材（设备租赁） 3周材（设备） 4二手设备 5租赁设备")
    private Integer productType;

    @ApiModelProperty(value = "关联外部编号")

    private String relevanceNo;

    @ApiModelProperty(value = "商品名称")

    private String productTitle;

    @ApiModelProperty(value = "规格标题")

    private String specTitle;
}
