package scrbg.meplat.mall.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
/**
 * @描述：结算订单明细
 * @作者: ye
 * @日期: 2023-06-16
 */
@ApiModel(value="结算订单明细")
@Data
@TableName("deal_order_info")
public class DealOrderInfo extends MustBaseEntity implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "结算id")
    private String dealOrderInfoId;

    @ApiModelProperty(value = "店铺id")

    private String shopId;


    @ApiModelProperty(value = "店铺名称")

    private String shopName;


    @ApiModelProperty(value = "买方本机机构id")

    private String buyOrgId;


    @ApiModelProperty(value = "买方机构名称")

    private String bugOrgName;


    @ApiModelProperty(value = "供应商id")

    private String supplierId;


    @ApiModelProperty(value = "供应商名称")

    private String supplierName;


    @ApiModelProperty(value = "订单编号（废弃）")

    private String orderSn;


    @ApiModelProperty(value = "订单id")

    private String orderId;


    @ApiModelProperty(value = "商品id")

    private String productId;


    @ApiModelProperty(value = "商品名称")

    private String productName;


    @ApiModelProperty(value = "交易数量")

    private BigDecimal number;


    @ApiModelProperty(value = "交易金额")

    private BigDecimal amount;


    @ApiModelProperty(value = "交易成本价（废弃）")

    private BigDecimal costAmount;



    @ApiModelProperty(value = "交易完成时间")

    private Date finishDate;


    @ApiModelProperty(value = "规格名称")

    private String skuName;


    @ApiModelProperty(value = "计量单位")

    private String unit;


    @ApiModelProperty(value = "结算类型（1零星采购）")

    private Integer dealType;


    @ApiModelProperty(value = "订单完成时间")

    private Date orderFinishDate;

    @ApiModelProperty(value = "结算id")
    private String settleAccountsId;

    @ApiModelProperty(value = "对账单总金额")
    private BigDecimal reconciliationAmount;

    @ApiModelProperty(value = "对账单id")
    private String reconciliationId;







    @ApiModelProperty(value = "税率")

    private BigDecimal taxRate;

    @ApiModelProperty(value = "单价")

    private BigDecimal price;

    @ApiModelProperty(value = "网价（浮动价格使用）")

    private BigDecimal netPrice;

    @ApiModelProperty(value = "固定费用（浮动价格使用）")

    private BigDecimal fixationPrice;

    @ApiModelProperty(value = "出厂价（固定价格使用）")

    private BigDecimal outFactoryPrice;

    @ApiModelProperty(value = "运杂费（固定价格使用）")

    private BigDecimal transportPrice;


    @ApiModelProperty(value = "业务类型（1合同2计划3调拨4甲供5暂估6大宗临购）")

    private Integer businessType;

    @ApiModelProperty(value = "清单类型（1浮动价格2固定价格）（大宗临购使用）")

    private Integer billType;

    @ApiModelProperty(value = "不含税单价（废弃）")

    private BigDecimal noRatePrice;

    @ApiModelProperty(value = "不含税金额（废弃，不含税都是放在amount）")

    private BigDecimal noRateAmount;


    @ApiModelProperty(value = "交易完成时间")
    @TableField(exist = false)
    private String finishDateStr;


    @ApiModelProperty(value = "查询合计金额")
    @TableField(exist = false)
    private BigDecimal countAmount;

    @ApiModelProperty(value = "查询合计金额(不含税)")
    @TableField(exist = false)
    private BigDecimal noRateCountAmount;

    @ApiModelProperty(value = "查询累计金额")
    @TableField(exist = false)
    private BigDecimal accumulateAmount;

    @ApiModelProperty(value = "商品类型")
    @TableField(exist = false)
    private Integer productType;

}
