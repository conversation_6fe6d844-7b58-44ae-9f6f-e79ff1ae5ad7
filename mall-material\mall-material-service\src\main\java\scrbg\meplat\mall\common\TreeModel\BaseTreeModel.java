package scrbg.meplat.mall.common.TreeModel;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-11-09 14:16
 */
@Data
@ApiModel(value = "基础树")
public abstract class BaseTreeModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "分类层级 1:一级大分类 2:二级分类 3:三级小分类")
    private Integer level;

    @ApiModelProperty(value = "父id")
    private String parentId;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @TableField(
            exist = false
    )
    private List<BaseTreeModel> children;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @TableField(
            exist = false
    )
    private BaseTreeModel child;
}
