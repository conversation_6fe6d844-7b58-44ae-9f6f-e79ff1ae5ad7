package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.entity.MaterialMasterPlanDtl;
import scrbg.meplat.mall.service.MaterialMasterPlanDtlService;

import java.util.List;

/**
 * @描述：物资总计划明细控制类
 * @作者: y
 * @日期: 2022-11-10
 */
@RestController
@RequestMapping("/materialMasterPlanDtl")
@Api(tags = "物资总计划明细")
public class MaterialMasterPlanDtlController {

    @Autowired
    public MaterialMasterPlanDtlService materialMasterPlanDtlService;

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<MaterialMasterPlanDtl> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = materialMasterPlanDtlService.queryPage(jsonObject, new LambdaQueryWrapper<MaterialMasterPlanDtl>());
        return PageR.success(page);
    }

    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<MaterialMasterPlanDtl> findById(String id) {
        MaterialMasterPlanDtl materialMasterPlanDtl = materialMasterPlanDtlService.getById(id);
        return R.success(materialMasterPlanDtl);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody MaterialMasterPlanDtl materialMasterPlanDtl) {
        materialMasterPlanDtlService.create(materialMasterPlanDtl);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody MaterialMasterPlanDtl materialMasterPlanDtl) {
        materialMasterPlanDtlService.update(materialMasterPlanDtl);
        return R.success();
    }

    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        materialMasterPlanDtlService.delete(id);
        return R.success();
    }


    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")

    public R deleteBatch(@RequestBody List<String> ids) {
        materialMasterPlanDtlService.removeByIds(ids);
        return R.success();
    }
}

