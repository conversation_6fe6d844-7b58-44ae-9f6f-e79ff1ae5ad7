package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import scrbg.meplat.mall.service.MaterialMonthSupplyPlanDtlService;
import scrbg.meplat.mall.entity.MaterialMonthSupplyPlanDtl;
import scrbg.meplat.mall.vo.supplier.PushPlanToSupplierVo;

import java.util.List;

/**
 * @描述：计划明细控制类
 * @作者: ye
 * @日期: 2023-06-27
 */
@RestController
@RequestMapping("/materialMonthSupplyPlanDtl")
@Api(tags = "计划明细")
public class MaterialMonthSupplyPlanDtlController {

    @Autowired
    public MaterialMonthSupplyPlanDtlService materialMonthSupplyPlanDtlService;

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<MaterialMonthSupplyPlanDtl> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = materialMonthSupplyPlanDtlService.queryPage(jsonObject, new LambdaQueryWrapper<MaterialMonthSupplyPlanDtl>());
        return PageR.success(page);
    }

    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<MaterialMonthSupplyPlanDtl> findById(String id) {
        MaterialMonthSupplyPlanDtl materialMonthSupplyPlanDtl = materialMonthSupplyPlanDtlService.getById(id);
        return R.success(materialMonthSupplyPlanDtl);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody MaterialMonthSupplyPlanDtl materialMonthSupplyPlanDtl) {
        materialMonthSupplyPlanDtlService.create(materialMonthSupplyPlanDtl);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody MaterialMonthSupplyPlanDtl materialMonthSupplyPlanDtl) {
        materialMonthSupplyPlanDtlService.update(materialMonthSupplyPlanDtl);
        return R.success();
    }


    @PostMapping("/pushPlanToSupplier")
    @ApiOperation(value = "推送大宗月供二级供应商")
    public R pushPlanToSupplier(@RequestBody PushPlanToSupplierVo pushPlanToSupplierVo) {
        materialMonthSupplyPlanDtlService.pushPlanToSupplierVo(pushPlanToSupplierVo);
        return R.success();
    }


    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        materialMonthSupplyPlanDtlService.delete(id);
        return R.success();
    }
    @GetMapping("/closePushDtl")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R closePushDtl(String id) {
        materialMonthSupplyPlanDtlService.closePushDtl(id);
        return R.success();
    }


    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")
    public R deleteBatch(@RequestBody List<String> ids) {
        materialMonthSupplyPlanDtlService.removeByIds(ids);
        return R.success();
    }
}

