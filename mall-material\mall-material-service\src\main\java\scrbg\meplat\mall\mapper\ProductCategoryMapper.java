package scrbg.meplat.mall.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import scrbg.meplat.mall.entity.ProductCategory;

import java.util.List;
import java.util.Map;

/**
 * @描述：商品分类 Mapper 接口
 * @作者: y
 * @日期: 2022-11-02
 */
@Mapper
@Repository
public interface ProductCategoryMapper extends BaseMapper<ProductCategory> {


    @Delete("DELETE FROM product_category WHERE class_id=#{id}")
    int removeById(@Param("id")String id);

    @Delete("<script>" +
            "delete from product_category where class_id in " +
            "<foreach collection='ids' open='(' item='id_' separator=',' close=')'> #{id_}" +
            "</foreach>" +
            "</script>")
    int removeByIds(@Param("ids") List<String> ids);


    List<ProductCategory> selectThreeCateList(@Param("name")String name);
}
