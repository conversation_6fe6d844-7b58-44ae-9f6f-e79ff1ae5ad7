package scrbg.meplat.mall.openInterface.pcwp2;

import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.dto.category.UpdateCategorySateByIdDTO;
import scrbg.meplat.mall.dto.thirdapi.CreateProductCategoryDTO;
import scrbg.meplat.mall.dto.thirdapi.MaterialDtlDTO;
import scrbg.meplat.mall.entity.InterfaceLogs;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.service.InterfaceLogsService;
import scrbg.meplat.mall.service.ProductCategoryService;
import scrbg.meplat.mall.util.LogUtil;
import scrbg.meplat.mall.util.R;

import javax.validation.Valid;
import java.util.UUID;


@RestController
@RequestMapping("/w/thirdApi/material")
@Api(tags = "物资管理")
public class ClassAndBasicLibraryController {

    @Autowired
    public ProductCategoryService productCategoryService;

    @Autowired
    private InterfaceLogsService interfaceLogsService;

    @PostMapping("/category/createOrUpdate")
    @ApiOperation(value = "新增或修改类别")
    // 删除缓存如果出现异常不会删除
    @CacheEvict(value = {"category"}, key = "#dto.productType + '_' + #dto.mallType + '_getTree'")
    public R categoryCreateOrUpdate(@Valid @RequestBody CreateProductCategoryDTO dto) {
        String idStr = dto.getKeyId();
        try {
            productCategoryService.categoryCreateOrUpdate(dto);
        }catch (Exception e) {
            LogUtil.writeErrorLog(idStr,"categoryCreateOrUpdate",dto,null,null,e.getMessage(), ClassAndBasicLibraryController.class);
            InterfaceLogs iLog = new InterfaceLogs();
            iLog.setSecretKey(idStr);
            iLog.setClassPackage(ClassAndBasicLibraryController.class.getName());
            iLog.setMethodName("categoryCreateOrUpdate");
            iLog.setFarArguments(JSON.toJSONString(dto));
            iLog.setIsSuccess(0);
            iLog.setLogType(3);
            iLog.setErrorInfo(e.getMessage());
            interfaceLogsService.create(iLog);
            throw new BusinessException(500,e.getMessage());
        }
        return R.success();
    }


    @PostMapping("/saveMaterialInfo")
    @ApiOperation(value = "新增或修改物资商品")
    public R saveMaterialInfo(@Valid @RequestBody MaterialDtlDTO dto) {
        String idStr= UUID.randomUUID().toString();
        try {
            productCategoryService.saveMaterialInfo(idStr,dto);
            return R.success();
        }catch (Exception e) {
            LogUtil.writeErrorLog(idStr,"saveMaterialInfo",dto,null,null,e.getMessage(), ClassAndBasicLibraryController.class);
            InterfaceLogs iLog = new InterfaceLogs();
            iLog.setSecretKey(idStr);
            iLog.setClassPackage(ClassAndBasicLibraryController.class.getName());
            iLog.setMethodName("saveMaterialInfo");
            iLog.setFarArguments(JSON.toJSONString(dto));
            iLog.setIsSuccess(0);
            iLog.setLogType(3);
            iLog.setErrorInfo(e.getMessage());
            interfaceLogsService.create(iLog);
            throw new BusinessException(500,e.getMessage());
        }
    }

    @PostMapping("/batchUpdateClassState")
    @ApiOperation(value = "批量修改启用停用状态")
    public R thirdApiBatchUpdateClassState(@Valid @RequestBody UpdateCategorySateByIdDTO dto) {
        String idStr = dto.getKeyId();
        try {
            productCategoryService.thirdApiBatchUpdateClassState(dto);
        }catch (Exception e) {
            LogUtil.writeErrorLog(idStr,"thirdApiBatchUpdateClassState",dto,null,null,e.getMessage(), ClassAndBasicLibraryController.class);
            InterfaceLogs iLog = new InterfaceLogs();
            iLog.setSecretKey(idStr);
            iLog.setClassPackage(ClassAndBasicLibraryController.class.getName());
            iLog.setMethodName("thirdApiBatchUpdateClassState");
            iLog.setFarArguments(JSON.toJSONString(dto));
            iLog.setIsSuccess(0);
            iLog.setLogType(3);
            iLog.setErrorInfo(e.getMessage());
            interfaceLogsService.create(iLog);
            throw new BusinessException(500,e.getMessage());
        }
        return R.success();
    }




}

