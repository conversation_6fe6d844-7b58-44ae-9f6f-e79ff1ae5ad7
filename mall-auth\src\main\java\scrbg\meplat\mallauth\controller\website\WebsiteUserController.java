package scrbg.meplat.mallauth.controller.website;


import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mallauth.exception.BusinessException;
import scrbg.meplat.mallauth.vo.user.LoginVO;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/w/user")
@ApiSort(value = 99)
@Api(tags = "用户")
@Log4j2
public class WebsiteUserController {

    @Value("${mall.account}")
    public String account1;

    @Value("${mall.password}")
    public String password1;
    @Value("${mall.token}")
    public String token;

    @GetMapping("/login")
    public R<LoginVO> login(@RequestParam("account") String account, @RequestParam("password") String password, HttpServletRequest request) {
        if (account.equals(account1) && password.equals(password1)) {
            LoginVO loginVO = new LoginVO();
            loginVO.setAccount(account);
            loginVO.setPassword(password);
            loginVO.setToken(token);
            return R.success(loginVO);
        }else {
            throw new BusinessException(500,"账户密码不正确");
        }


    }

}
