package scrbg.meplat.mall.entity;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Objects;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

/**
 * @描述：
 * @作者: ye
 * @日期: 2023-12-12
 */
@ApiModel(value = "")
@Data
@TableName("product_compare_item")
public class ProductCompareItem extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String compareItemId;

    @ApiModelProperty(value = "比价主表ID")

    private String compareId;


    @ApiModelProperty(value = "比价商品id")

    private String productId;


    @ApiModelProperty(value = "物资名称")

    private String productName;


    @ApiModelProperty(value = "skuid")

    private String skuId;


    @ApiModelProperty(value = "规格型号")

    private String skuName;


    @ApiModelProperty(value = "店铺id")

    private String shopId;
    @ApiModelProperty(value = "店铺名称")

    private String shopName;

    @ApiModelProperty(value = "商品小图")

    private String productMinImg;


    @ApiModelProperty(value = "销售价格")

    private BigDecimal sellPrice;


    @ApiModelProperty(value = "sku单位")

    private String unit;


    @ApiModelProperty(value = "品牌id")

    private String brandId;


    @ApiModelProperty(value = "品牌名称")

    private String brandName;


    @ApiModelProperty(value = "供应商id")

    private String supperBy;


    @ApiModelProperty(value = "供方名称")

    private String supplierName;


    @ApiModelProperty(value = "是否自营：1：是  0：否（默认：0）")

    private Integer isBusiness;


    @ApiModelProperty(value = "是否支持内部结算：1：是  0：否")

    private Integer isInternalSettlement;


    @ApiModelProperty(value = "状态")

    private Integer state;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        //if (!super.equals(o)) return false;
        ProductCompareItem that = (ProductCompareItem) o;
        return productId.equals(that.productId) && Objects.equals(productName, that.productName) && skuId.equals(that.skuId) && Objects.equals(skuName, that.skuName) && Objects.equals(shopId, that.shopId) && Objects.equals(shopName, that.shopName) && Objects.equals(productMinImg, that.productMinImg) && Objects.equals(sellPrice, that.sellPrice) && Objects.equals(unit, that.unit) && Objects.equals(brandId, that.brandId) && Objects.equals(brandName, that.brandName) && Objects.equals(supperBy, that.supperBy) && Objects.equals(supplierName, that.supplierName) && Objects.equals(isBusiness, that.isBusiness) && Objects.equals(isInternalSettlement, that.isInternalSettlement) && Objects.equals(state, that.state);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), productId, productName, skuId, skuName, shopId, shopName, productMinImg, sellPrice, unit, brandId, brandName, supperBy, supplierName, isBusiness, isInternalSettlement, state);
    }
}