package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @描述：文件上传记录信息
 * @作者: ye
 * @日期: 2023-03-27
 */
@ApiModel(value = "文件上传记录信息")
@Data
@TableName("file_record")
public class FileRecord extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "记录id")
    private String recordId;

    @ApiModelProperty(value = "对象名称")

    private String objectName;


    @ApiModelProperty(value = "对象路径")

    private String objectPath;


    @ApiModelProperty(value = "不含IP/域名的对象路径")

    private String nonIpObjectPath;


    @ApiModelProperty(value = "桶名称")

    private String bucketName;


    @ApiModelProperty(value = "对象大小kb")

    private BigDecimal objectSizeKb;


    @ApiModelProperty(value = "对象大小mb")

    private BigDecimal objectSizeMb;


    @ApiModelProperty(value = "状态")

    private Integer state;


}