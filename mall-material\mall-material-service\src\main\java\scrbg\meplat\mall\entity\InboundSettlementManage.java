package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@ApiModel(value = "入库管理")
@Data
@TableName("inbound_settlement")
public class InboundSettlementManage {

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    @ApiModelProperty(value = "对账单id")
    private String reconciliationId;

    @ApiModelProperty(value = "业务类型")
    private Integer supplierType;

    @ApiModelProperty(value = "供货单位id")
    private String supplierId;

    @ApiModelProperty(value = "供货单位")
    private String supplierName;


    @ApiModelProperty(value = "合同编号")
    private String contractNo;

    @ApiModelProperty(value = "发票字号")
    private String invoiceNum;


    @ApiModelProperty(value = "入库方式  1-手动入库 2-自动入库")
    private Integer inboundType;

    @ApiModelProperty(value = "审核状态")
    private Integer auditStatus;

    @ApiModelProperty(value = "仓库id")
    private String warehouseId;


    @ApiModelProperty(value = "含税总金额")
    private BigDecimal rateAmount;

    @ApiModelProperty(value = "不含税总金额")
    private BigDecimal noRateAmount;

    @ApiModelProperty(value = "数量")
    private BigDecimal num;

    @ApiModelProperty(value = "收件人名称")
    private String receiveName;

    @ApiModelProperty(value = "收件人手机号")
    private String receivePhone;

    @ApiModelProperty(value = "入库时间")
    private Date storedWarehouseTime;

    @ApiModelProperty(value = "期数")
    private String accountPeriod;

    @ApiModelProperty(value = "创建时间")
    private Date gmtCreate;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "附件")
    @TableField(exist = false)
    private List<File> attachmentFile;

    @ApiModelProperty(value = "结算单明细")
    private String settlementInfo;
}
