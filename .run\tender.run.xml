<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="tender" type="docker-deploy" factoryName="dockerfile" server-name="正式环境docker">
    <deployment type="dockerfile">
      <settings>
        <option name="imageTag" value="mall-tender-api" />
        <option name="containerName" value="mall-tender-api" />
        <option name="portBindings">
          <list>
            <DockerPortBindingImpl>
              <option name="containerPort" value="9030" />
              <option name="hostPort" value="9030" />
            </DockerPortBindingImpl>
          </list>
        </option>
        <option name="commandLineOptions" value="--restart=always -v /home/<USER>/logs:/logs" />
        <option name="sourceFilePath" value="tende/tende-service/dockerfile" />
      </settings>
    </deployment>
    <method v="2">
      <option name="Maven.BeforeRunTask" enabled="true" file="$PROJECT_DIR$/tende/tende-service/pom.xml" goal="clean package -U -DskipTests" />
    </method>
  </configuration>
</component>