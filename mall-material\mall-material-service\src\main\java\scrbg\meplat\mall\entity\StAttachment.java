package scrbg.meplat.mall.entity;

import java.time.LocalDate;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import lombok.Data;
import lombok.EqualsAndHashCode;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
/**
 * 清单附件
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class StAttachment extends MustBaseEntity{
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    private String stId;
    private String name;
    private Integer fileSize;
    private String fileFarId;
    private Integer fileType;
    private LocalDate uploadDate;
}
