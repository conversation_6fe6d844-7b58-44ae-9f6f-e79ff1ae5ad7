package scrbg.meplat.mall.dto.product;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-11-04 11:20
 */
@Data
public class UpdateProductInventoryStateDTO {

    @ApiModelProperty(value = "商品库id",required = true)
    @NotEmpty(message = "商品库id不能为空！")
    private List<String> productInventoryIds;

    @ApiModelProperty(value = "状态（1启用 0停用）",required = true)
    @NotNull(message = "状态不能为空！")
    @Max(value = 1, message = "状态输入错误！")
    @Min(value = 0, message = "状态输入错误！")
    private Integer state;

}
