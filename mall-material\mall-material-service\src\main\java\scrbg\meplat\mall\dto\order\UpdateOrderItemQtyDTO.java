package scrbg.meplat.mall.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-10-27 8:59
 */
@Data
public class UpdateOrderItemQtyDTO {
    @ApiModelProperty(value = "订单明细id")
    private String orderItemId;

    @ApiModelProperty(value = "新的数量")
    private BigDecimal qty;
}
