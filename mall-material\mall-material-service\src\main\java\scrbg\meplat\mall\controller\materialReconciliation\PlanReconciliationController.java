package scrbg.meplat.mall.controller.materialReconciliation;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.config.auth.IsRole;
import scrbg.meplat.mall.config.auth.RoleEnum;
import scrbg.meplat.mall.config.logRecording.LogRecord;
import scrbg.meplat.mall.config.logRecording.enums.BusinessType;
import scrbg.meplat.mall.config.logRecording.enums.OperatorType;
import scrbg.meplat.mall.config.redis.redisson.NotResubmit;
import scrbg.meplat.mall.dto.AuditDTO;
import scrbg.meplat.mall.dto.order.MaterialReconciliationCancellationDTO;
import scrbg.meplat.mall.dto.reconciliation.MaterialReconciliationUpdateDTO;
import scrbg.meplat.mall.dto.reconciliation.SupplierPlanReconciliationQueryDTO;
import scrbg.meplat.mall.entity.InterfaceLogs;
import scrbg.meplat.mall.entity.MaterialReconciliation;
import scrbg.meplat.mall.entity.OrderItem;
import scrbg.meplat.mall.entity.Orders;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.pcwp.PcwpService;
import scrbg.meplat.mall.service.InterfaceLogsService;
import scrbg.meplat.mall.service.MaterialReconciliationService;
import scrbg.meplat.mall.service.OrderItemService;
import scrbg.meplat.mall.service.OrdersService;
import scrbg.meplat.mall.service.PlanReconciliationService;
import scrbg.meplat.mall.util.LogUtil;
import scrbg.meplat.mall.util.RestTemplateUtils;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;
import scrbg.meplat.mall.vo.reconciliation.PlanReconciliationVO;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @描述：计划对账控制类
 * @作者: tanfei
 * @日期: 2025-06-19
 */
@RestController
@RequestMapping("/planReconciliation")
@Api(tags = "计划对账管理")
public class PlanReconciliationController {
    @Autowired
    public PlanReconciliationService planReconciliationService;

    @PostMapping("/getReconciliablePlanList")
    @ApiOperation(value = "供应商方-获取可对账的计划订单列表")
    public PageR<PlanReconciliationVO> getReconciliablePlansBySupplier(@RequestBody SupplierPlanReconciliationQueryDTO dto) {
        PageUtils page = planReconciliationService.getReconciliablePlansBySupplierPageList(dto);
        return PageR.success(page);
    }

    @PostMapping("/getReconciliableEnterprisePageList")
    @ApiOperation(value = "供应商方-获取可对账的项目部")
    public PageR<PlanReconciliationVO> getReconciliableEnterprisePageList(@RequestBody SupplierPlanReconciliationQueryDTO dto) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        dto.setSupplierId(user.getEnterpriseId());
        PageUtils page = planReconciliationService.getReconciliableEnterpriseBySupplierPageList(dto);
        return PageR.success(page);
    }

    @PostMapping("/getReconciliableSupplierByEnterprisePageList")
    @ApiOperation(value = "采购方-获取可对账的供应商列表")
    public PageR<PlanReconciliationVO> getReconciliableSupplierByEnterprisePageList(@RequestBody SupplierPlanReconciliationQueryDTO dto) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        dto.setSupplierId(user.getEnterpriseId());
        PageUtils page = planReconciliationService.getReconciliableSupplierByEnterprisePageList(dto);
        return PageR.success(page);
    }

    @PostMapping("/getReconciliablePlansByEnterprisePageList")
    @ApiOperation(value = "采购方-获取可对账的计划订单列表")
    public PageR<PlanReconciliationVO> getReconciliablePlansByEnterprisePageList(@RequestBody SupplierPlanReconciliationQueryDTO dto) {
        PageUtils page = planReconciliationService.getReconciliablePlansByEnterprisePageList(dto);
        return PageR.success(page);
    }
}
