package scrbg.meplat.mall.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

/**
 * @描述：平台年费表
 * @作者: ye
 * @日期: 2024-01-24
 */
@ApiModel(value = "平台年费表")
@Data
@TableName("platform_year_fee")
public class PlatformYearFee extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "平台计费id")
    private String platformYearFeeId;

    @ApiModelProperty(value = "平台年费编号")
    private String platformYearFeeNu;

    @ApiModelProperty(value = "店铺id")
    private String shopId;

    @ApiModelProperty(value = "店铺名称")
    private String shopName;

    @ApiModelProperty(value = "企业id")
    private String enterpriseId;

    @ApiModelProperty(value = "企业名称")
    private String enterpriseName;

    @ApiModelProperty(value = "服务类型（1店铺年费2招标年费）")
    private Integer serveType;

    @ApiModelProperty(value = "总缴费金额")
    private BigDecimal paymentAmount;

    @ApiModelProperty(value = "服务到期时间（为null或者小于当前时间都是过期）")
    private LocalDate serveEndTime;
    @ApiModelProperty(value = "服务到期时间（为null或者小于当前时间都是过期）")
    @TableField(exist = false)
    private String serveEndTimeStr;

    public String getServeEndTimeStr() {
        if (serveEndTime != null) {
            return serveEndTime.toString();
        } else {
            return null;
        }
    }

    @ApiModelProperty(value = "服务是否未过期")
    private Integer outTime;

    @ApiModelProperty(value = "服务是否未过期")
    @TableField(exist = false)
    private String outTimeStr;

    public String getOutTimeStr() {
        if (outTime == 0) {
            return "是";
        } else {
            return "否";
        }
    }

    @ApiModelProperty(value = "状态 已废弃")
    private Integer state;

    @ApiModelProperty(value = "年费记录")
    @TableField(exist = false)
    private List<PlatformYearFeeRecord> dtls;

    @ApiModelProperty(value = "乐观锁")
    @Version
    private Integer version;
}