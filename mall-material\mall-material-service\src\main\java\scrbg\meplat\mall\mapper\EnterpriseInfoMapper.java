package scrbg.meplat.mall.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import scrbg.meplat.mall.entity.EnterpriseInfo;
import scrbg.meplat.mall.vo.platform.enterprise.EnterpriseLedgerVo;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【enterprise_info】的数据库操作Mapper
* @createDate 2022-11-03 11:46:18
* @Entity scrbg.meplat.mall.entity.EnterpriseInfo
*/
@Mapper
@Repository
public interface EnterpriseInfoMapper extends BaseMapper<EnterpriseInfo> {
    int ledgerListCount(@Param("dto") Map<String, Object> dto);
    List<EnterpriseLedgerVo> ledgerList(Page<EnterpriseLedgerVo> pages, @Param("dto") Map<String, Object> dto);
}




