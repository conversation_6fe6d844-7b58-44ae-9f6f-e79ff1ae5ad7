package scrbg.meplat.mall.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-06-08 13:48
 */
@Data
public class MasterAffirmTwoOrderDTO {

    @ApiModelProperty(value = "订单ids")
    private List<String> orderIds;

    @ApiModelProperty(value ="是否确认")
    private Boolean isAffirm;
}
