package scrbg.meplat.mall.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
/**
 * @描述：
 * @作者: ye
 * @日期: 2023-10-07
 */
@ApiModel(value="大宗临购单明细")
@Data
@TableName("synthesize_temporary_dtl")
public class SynthesizeTemporaryDtl extends MustBaseEntity implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "大宗临购单明细id")
    private String synthesizeTemporaryDtlId;

    @ApiModelProperty(value = "大宗临购单id")

    private String synthesizeTemporaryId;


    @ApiModelProperty(value = "商品id")

    private String productId;


    @ApiModelProperty(value = "商品编号")

    private String productSn;

    @ApiModelProperty(value = "商品名称")

    private String productName;



    @ApiModelProperty(value = "基础库物资id")

    private String materialId;
    @ApiModelProperty(value = "基础库物资编号")

    private String materialSn;
    @ApiModelProperty(value = "基础库物资名称")

    private String materialName;


    @ApiModelProperty(value = "商品供应商机构id")

    private String supplierOrgId;

    @ApiModelProperty(value = "商品供应商机构名称")

    private String supplierName;


    @ApiModelProperty(value = "分类id")

    private String classId;


    @ApiModelProperty(value = "分类名称")

    private String className;


    @ApiModelProperty(value = "分类id路径（xxx/xxx/xx）")

    private String classIdPath;


    @ApiModelProperty(value = "分类name路径（xxx/xxx/xx）")

    private String classNamePath;


    @ApiModelProperty(value = "规格型号")

    private String spec;


    @ApiModelProperty(value = "材质")

    private String texture;


    @ApiModelProperty(value = "品牌id")

    private String brandId;


    @ApiModelProperty(value = "品牌名称")

    private String brandName;


    @ApiModelProperty(value = "计量单位")

    private String unit;


    @ApiModelProperty(value = "数量")

    private BigDecimal qty;


    @ApiModelProperty(value = "重量（废弃）")

    private BigDecimal weightNum;

    @ApiModelProperty(value = "成本价")

    private BigDecimal costPrice;

    @ApiModelProperty(value = "参考单价（商品的参考价（不会变化））")

    private BigDecimal referencePrice;


    @ApiModelProperty(value = "综合单价（会变化，网价+固定费 或者 出厂价+运杂费），新增时是商品参考价（销售价）")

    private BigDecimal synthesizePrice;


    @ApiModelProperty(value = "网价（浮动价格使用）")

    private BigDecimal netPrice;

    @ApiModelProperty(value = "固定费用（浮动价格使用）")

    private BigDecimal fixationPrice;

    @ApiModelProperty(value = "出厂价（固定价格使用）")

    private BigDecimal outFactoryPrice;

    @ApiModelProperty(value = "运杂费（固定价格使用）")

    private BigDecimal transportPrice;



    @ApiModelProperty(value = "参考金额（不会变化）")

    private BigDecimal referenceAmount;


    @ApiModelProperty(value = "综合金额")

    private BigDecimal synthesizeAmount;




    @ApiModelProperty(value = "是否有二级单位")

    private Integer isTwoUnit;

    @ApiModelProperty(value = "二级单位")

    private String twoUnit;

    @ApiModelProperty(value = "二级单位购买数量")

    private BigDecimal twoUnitNum;

    @ApiModelProperty(value = "临购副单位对应主单位数量系数")

    private BigDecimal secondUnitNum;


    @ApiModelProperty(value = "超期垫资利息（%）批量修改冗余使用")
    @TableField(exist = false)
    private BigDecimal outPhaseInterest;


    @ApiModelProperty(value = "是否确认")
    @TableField(exist = false)
    private Integer isAffirm;

    @ApiModelProperty(value = "是否生成竞价 0 未生成竞价 1 已生成竞价")
    private Integer isBidding;

    @ApiModelProperty(value = "竞价编号")
    @TableField(exist = false)
    private String biddingSn;
    @ApiModelProperty(value = "竞价商品明细")
    @TableField(exist = false)
    private BiddingProduct biddingProduct;

    @ApiModelProperty(value = "竞价商品报价明细")
    @TableField(exist = false)
    private BiddingBidRecordItem bidRecordItem;

    // 竞价编号
    @ApiModelProperty(value = "最高价")
    @TableField(
            exist = false
    )
    private BigDecimal maxPrice;

    @ApiModelProperty(value = "网价（浮动价格报价使用）")
    @TableField(exist = false)
    private BigDecimal bidNetPrice;


    @ApiModelProperty(value = "区域id")

    private String zoneId;

    @ApiModelProperty(value = "区域地址详细地址")

    private String zoneAddr;
    @ApiModelProperty(value = "销售区域")
    @TableField(exist = false)
    private String zonePath;




}