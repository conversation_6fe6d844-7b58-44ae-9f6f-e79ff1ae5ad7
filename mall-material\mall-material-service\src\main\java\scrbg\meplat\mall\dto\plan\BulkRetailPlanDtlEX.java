package scrbg.meplat.mall.dto.plan;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 大宗零购计划明细(物资贸易平台)
 */
@ApiModel(value="大宗零购计划明细")
@Data
public class BulkRetailPlanDtlEX {


    @ApiModelProperty(value = "明细id")

    private String dtlId;

    @ApiModelProperty(value = "计划id")

    private String billId;

    @ApiModelProperty(value = "物资类别id(1级类别id/2级类别id/..)")

    private String materialClassId;

    @ApiModelProperty(value = "物资类别名称(1级类别名称/2级类别名称/..)")

    private String materialClassName;

    @ApiModelProperty(value = "物资id")

    private String materialId;

    @ApiModelProperty(value = "物资名称")

    private String materialName;

    @ApiModelProperty(value = "规格")

    private String spec;

    @ApiModelProperty(value = "计量单位")

    private String unit;

    @ApiModelProperty(value = "材质")

    private String texture;

    @ApiModelProperty(value = "数量")

    private BigDecimal quantity;

    @ApiModelProperty(value = "网价")

    private BigDecimal networkPrice;

    @ApiModelProperty(value = "出厂价")

    private BigDecimal factoryPrice;

    @ApiModelProperty(value = "固定价")

    private BigDecimal fixedFee;

    @ApiModelProperty(value = "运费")

    private BigDecimal freight;

    @ApiModelProperty(value = "单价(元)")

    private BigDecimal price;

    @ApiModelProperty(value = "含税单价(商城推送)(元)")

    private BigDecimal taxPrice;

    @ApiModelProperty(value = "含税单价(商城)(元)")

    private BigDecimal TaxAmount;

    @ApiModelProperty(value = "金额(元)")

    private BigDecimal amount;

    @ApiModelProperty(value = "供应商id")

    private String supplierId;

    @ApiModelProperty(value = "供应商名称")

    private String supplierName;

//    @ApiModelProperty(value = "垫资时间")
//
//    private String advancePaymentTime;
//
//    @ApiModelProperty(value = "垫资利息(%)")
//
//    private BigDecimal advanceInterest;

    @ApiModelProperty(value = "商城单据明细id")

    private String sourceDtlId;

    @ApiModelProperty("商品名称")
    public String tradeName;

    @ApiModelProperty("商品Id")
    public String tradeId;

    @ApiModelProperty("供应商机构短码")
    public String orgShort;

    @ApiModelProperty("信用代码")
    public String creditCode;

    @ApiModelProperty("消耗金额")
    public BigDecimal consumeAmount;

    @ApiModelProperty("未消耗金额")
    public BigDecimal notConsumeAmount;


    @ApiModelProperty("已下单数量")
    public BigDecimal receivedQuantity;


}
