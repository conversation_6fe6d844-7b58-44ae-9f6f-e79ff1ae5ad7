package scrbg.meplat.mall.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2022-11-07 10:45
 */
@Data
public class CreateRepairOrderDTO {

    @ApiModelProperty(value = "支付金额",required = true)
    @NotNull(message = "支付金额不能为空！")
    private BigDecimal payPrice;

    @ApiModelProperty(value = "商品id（如果不是购物车结算这必传）")
    private String productId;

    @ApiModelProperty(value = "订单备注")
    private String orderRemark;

//    @ApiModelProperty(value = "配送方式")
//    private Integer deliveryType;



}
