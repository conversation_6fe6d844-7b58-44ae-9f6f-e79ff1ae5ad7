package scrbg.meplat.mall.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

/**
 * @描述：物资验收
 * @作者: ye
 * @日期: 2023-08-15
 */
@ApiModel(value = "物资验收")
@Data
@TableName("supplier_reconciliation")
public class SupplierReconciliation extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "对账单ID")
    private String billId;

    @ApiModelProperty(value = "对账单编号")
    private String billNo;

    @ApiModelProperty(value = "订单编号")
    private String orderSn;

    @ApiModelProperty(value = "订单Id")
    private String orderId;

    @ApiModelProperty(value = "二级供应商提交时间")
    private Date twoSubmitTime;

    @ApiModelProperty(value = "自营店提交时间")
    private Date submitTime;

    @ApiModelProperty(value = "提交人id")
    private String submitUserId;

    @ApiModelProperty(value = "提交人姓名")
    private String submitUserName;

    @ApiModelProperty(value = "0 未申请 1:已申请 2:已开票，3被拒 4采购员申请作废 5供应商申请作废,6作废 7.作废被拒 8.采购员申请红字 9.供应商申请红字 10已申请通过红字发票 11.申请红字被拒 12.红字已开票")
    private Integer invoiceState;

    @ApiModelProperty(value = "业务类型（1合同2计划3调拨4甲供5暂估6大宗临购）")
    private Integer businessType;

    @ApiModelProperty(value = "对账类型（1浮动价格对账单2固定价格对账单）")
    private Integer type;

    @ApiModelProperty(value = "二级供应商id")
    private String twoSupplierOrgId;

    @ApiModelProperty(value = "二级供应商名称")
    private String twoSupplierName;

    @ApiModelProperty(value = "供应商机构id")
    private String supplierOrgId;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "对账总金额（不含税）")
    private BigDecimal noRateAmount;

    @ApiModelProperty(value = "对账总金额（含税）")
    private BigDecimal rateAmount;

    @ApiModelProperty(value = "已结算金额")
    private BigDecimal settleAmount;

    @ApiModelProperty(value = "对账开始时间")
    private Date startTime;

    @ApiModelProperty(value = "对账结束时间")
    private Date endTime;

    @ApiModelProperty(value = "税额")
    private BigDecimal taxAmount;

    @ApiModelProperty(value = "新增来源（1供应商新增2二级供应商新增)")
    private Integer createType;

    @ApiModelProperty(value = "二级供应商是否确认（0否1是）")
    private Integer twoSupplierIsAffirm;

    @ApiModelProperty(value = "二级供应商确认时间")
    private Date twoSupplierAffirmTime;

    @ApiModelProperty(value = "供应商确认时间")
    private Date supplierAffirmTime;

    @ApiModelProperty(value = "供应商是否确认（0否1是）")
    private Integer supplierIsAffirm;

    @ApiModelProperty(value = "税率(%)")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "作废原因")
    private String nullifyReason;

    @ApiModelProperty(value = "作废人机构id")
    private String nullifyCreatorOrgId;

    @ApiModelProperty(value = "作废人id")
    private String nullifyCreatorId;

    @ApiModelProperty(value = "作废人")
    private String nullifyCreator;

    @ApiModelProperty(value = "作废时间")
    private Date nullifyCreated;

    @ApiModelProperty(value = "状态（0草稿1待提交2待审核3审核通过4审核失败 5供应商保存  7作废）")
    private Integer state;

    @ApiModelProperty(value = "审核失败原因")
    @TableField(exist = false)
    private String auditRecord;

    @ApiModelProperty(value = "审核历史（新增请忽略）")
    @TableField(exist = false)
    private List<AuditRecord> auditRecords;

    @ApiModelProperty(value = "对账明细")
    @TableField(exist = false)
    private List<SupplierReconciliationDtl> dtl;

    @ApiModelProperty(value = "开始时间")
    @TableField(exist = false)
    private String starDate;

    @ApiModelProperty(value = "结束时间")
    @TableField(exist = false)
    private String endDate;

    @ApiModelProperty(value = "单据时间")
    @TableField(exist = false)
    private String billDate;

    @ApiModelProperty(value = "关联id（对应pcwp验收单id）")
    private String relevanceId;

    @ApiModelProperty(value = "关联名称（对应pcwp验收单编号）")
    private String relevanceSn;

    @ApiModelProperty(value = "是否待推送pcwp（1是0已推送null不需要推送）")
    private Integer isNotPush;

    @ApiModelProperty(value = "对账明细导出模板")
    @TableField(exist = false)
    private List<SupplierReconciliationDtlExcel> dtlExcel;

}
