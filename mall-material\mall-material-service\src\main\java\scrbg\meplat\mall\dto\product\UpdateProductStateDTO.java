package scrbg.meplat.mall.dto.product;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-11-04 11:20
 */
@Data
public class UpdateProductStateDTO {

    @ApiModelProperty(value = "商品id",required = true)
    @NotEmpty(message = "商品id不能为空！")
    private List<String> productIds;

    @ApiModelProperty(value = "商品状态（0待上架 1已上架 2已下架 3 待审核 4审核失败）",required = true)
    @NotNull(message = "商品状态不能为空！")
    private Integer state;

    @ApiModelProperty(value = "审核失败原因")
    private String failReason;

    private Integer markUp;

    private BigDecimal markUpNum;

}
