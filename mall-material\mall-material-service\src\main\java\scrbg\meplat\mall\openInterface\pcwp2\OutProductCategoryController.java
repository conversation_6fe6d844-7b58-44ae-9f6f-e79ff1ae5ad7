package scrbg.meplat.mall.openInterface.pcwp2;

import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import scrbg.meplat.mall.entity.ProductCategory;
import scrbg.meplat.mall.service.ProductCategoryService;


import java.util.List;

@RestController
@RequestMapping("/w/thirdApi/productCategory")
@Api(tags = "商品分类对接（对接）")
public class OutProductCategoryController {

    @Autowired
    ProductCategoryService productCategoryService;

    @PostMapping("/list")
    @ApiOperation(value = "查看启动的地址易耗品分类")
    public R<List<ProductCategory>> listProductCategory() {

        List<ProductCategory> productCategory = productCategoryService.listByClassName(null, 0, 1, null,2);

        return R.success(productCategory);
    }
}
