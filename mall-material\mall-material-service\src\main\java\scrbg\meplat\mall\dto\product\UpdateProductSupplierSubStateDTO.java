package scrbg.meplat.mall.dto.product;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-11-04 11:20
 */
@Data
public class UpdateProductSupplierSubStateDTO {

    @ApiModelProperty(value = "商品id",required = true)
    @NotEmpty(message = "商品id不能为空！")
    private List<String> productIds;

    @ApiModelProperty(value = "提交状态（0默认1待提交2待确认3已确认4已拒绝）供方使用",required = true)
    @NotNull(message = "提交状态不能为空！")
    private Integer state;

    @ApiModelProperty(value = "审核失败原因")
    private String failReason;

}
