<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mallauth.mapper.SysMenuMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mallauth.entity.SysMenu" id="SysMenuMap">
        <result property="menuId" column="menu_id"/>
        <result property="code" column="code"/>
        <result property="title" column="title"/>
        <result property="type" column="type"/>
        <result property="authLabel" column="auth_label"/>
        <result property="icon" column="icon"/>
        <result property="pathUrl" column="path_url"/>
        <result property="classCode" column="class_code"/>
        <result property="parentMenuId" column="parent_menu_id"/>
        <result property="level" column="level"/>
        <result property="sort" column="sort"/>
        <result property="state" column="state"/>
        <result property="remarks" column="remarks"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="isDelete" column="is_delete"/>
        <result property="mallType" column="mall_type"/>
        <result property="isOpen" column="is_open"/>
    </resultMap>


</mapper>