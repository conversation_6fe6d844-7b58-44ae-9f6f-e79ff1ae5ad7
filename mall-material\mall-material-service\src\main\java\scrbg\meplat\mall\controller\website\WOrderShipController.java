package scrbg.meplat.mall.controller.website;

import com.github.xiaoymin.knife4j.annotations.ApiSort;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import scrbg.meplat.mall.entity.OrderShip;
import scrbg.meplat.mall.service.OrderShipService;

@RestController
@RequestMapping("/w/orderShip")
@ApiSort(value = 99)
@Api(tags = "发货单")
public class WOrderShipController {
    @Autowired
    OrderShipService orderShipServicel;


    @ApiOperation("供应商查询发货单远程Id查询发货单")
    @GetMapping("/getDataByOutBillId")
    public OrderShip getDataByOutBillId(String outBillId) {
        OrderShip orderShip = orderShipServicel.getDataByOutBillId(outBillId);
        return orderShip;
    }



}
