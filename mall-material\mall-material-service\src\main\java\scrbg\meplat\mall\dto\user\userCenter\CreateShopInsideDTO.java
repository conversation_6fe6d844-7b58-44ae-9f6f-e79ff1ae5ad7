package scrbg.meplat.mall.dto.user.userCenter;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-01-03 9:06
 */
@Data
public class CreateShopInsideDTO {

    @ApiModelProperty(value = "店铺id")
    private String shopId;

    @ApiModelProperty(value = "店铺名称")
    private String shopName;

    @ApiModelProperty(value = "联系人")
    private String contact;
    @ApiModelProperty(value = "税率（整数）")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "联系电话")
    private String tel;

    @ApiModelProperty(value = "详细地址")
    private String detailedAddress;
    @ApiModelProperty(value = "店铺简介")
    private String shopProfile;

}
