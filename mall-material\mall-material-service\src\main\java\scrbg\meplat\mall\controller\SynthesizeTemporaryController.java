package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import org.apache.xml.res.XMLErrorResources_tr;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import scrbg.meplat.mall.config.auth.IsRole;
import scrbg.meplat.mall.config.auth.RoleEnum;
import scrbg.meplat.mall.config.logRecording.LogRecord;
import scrbg.meplat.mall.config.logRecording.enums.BusinessType;
import scrbg.meplat.mall.config.logRecording.enums.OperatorType;
import scrbg.meplat.mall.config.redis.redisson.NotResubmit;
import scrbg.meplat.mall.dto.bidding.AuditBidingInfoDTO;
import scrbg.meplat.mall.dto.bidding.AuditBusinessDTO;
import scrbg.meplat.mall.dto.bidding.SynthesizeTemporaryDto;
import scrbg.meplat.mall.entity.SynthesizeTemporaryDtl;
import scrbg.meplat.mall.service.SynthesizeTemporaryService;
import scrbg.meplat.mall.entity.SynthesizeTemporary;
import scrbg.meplat.mall.vo.user.userCenter.IsSynthesizeTemporaryVO;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;

/**
 * @描述：大宗临购单控制类
 * @作者: ye
 * @日期: 2023-10-07
 */
@RestController
@RequestMapping("/")
@Api(tags = "大宗临购单")
public class SynthesizeTemporaryController {

    @Autowired
    public SynthesizeTemporaryService synthesizeTemporaryService;

//    @PostMapping("synthesizeTemporary/listByEntity")
//    @ApiOperation(value = "根据实体属性分页查询")
//    @DynamicParameters(name = "根据实体属性分页查询", properties = {
//            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
//            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
//            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = Integer.class)
//    })
//    public PageR<SynthesizeTemporary> listByEntity(@RequestBody JSONObject jsonObject) {
//        PageUtils page = synthesizeTemporaryService.queryPage(jsonObject, new LambdaQueryWrapper<SynthesizeTemporary>());
//        return PageR.success(page);
//    }

    @PostMapping("supplier/synthesizeTemporary/listByEntity")
    @ApiOperation(value = "供应商查询大宗临购清单")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = Integer.class),
            @DynamicParameter(name = "state", value = "状态（0草稿1已提交2供应商已确认3审核通过4审核不通过5收货方拒绝6已推送大宗临购计划））", dataTypeClass = Integer.class),
            @DynamicParameter(name = "states", value = "状态（0草稿1已提交2供应商已确认3审核通过4审核不通过5收货方拒绝6已推送大宗临购计划））", dataTypeClass = List.class),
    })
    public PageR<SynthesizeTemporary> supplierListByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = synthesizeTemporaryService.supplierListByEntity(jsonObject, new LambdaQueryWrapper<SynthesizeTemporary>());
        return PageR.success(page);
    }


    @PostMapping("platform/synthesizeTemporary/platformListByEntity")
    @ApiOperation(value = "物资分公司查看所有的大宗清单")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = Integer.class),
            @DynamicParameter(name = "state", value = "状态（0草稿1已提交2供应商已确认3审核通过4审核不通过5收货方拒绝6已推送大宗临购计划））", dataTypeClass = Integer.class),
            @DynamicParameter(name = "states", value = "状态（0草稿1已提交2供应商已确认3审核通过4审核不通过5收货方拒绝6已推送大宗临购计划））", dataTypeClass = List.class),
    })
    public PageR<SynthesizeTemporary> platformListByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = synthesizeTemporaryService.platformListByEntity(jsonObject, new LambdaQueryWrapper<SynthesizeTemporary>());
        return PageR.success(page);
    }


    @GetMapping("supplier/synthesizeTemporary/getBySn")
    @ApiOperation(value = "根据编号获取清单数据（供应商）")
    public R<SynthesizeTemporary> getSupplierBilBySn(String sn) {
        SynthesizeTemporary data = synthesizeTemporaryService.getSupplierBilBySn(sn);
        return R.success(data);
    }

    @PostMapping("supplier/synthesizeTemporary/batchUpdateItems")
    @ApiOperation(value = "批量修改明细并确认（供应商）")
    @LogRecord(title = "大宗临购清单",businessType = BusinessType.UPDATE,operatorType = OperatorType.MANAGE,isSaveRequestData = false)

    public R batchUpdateItems(@RequestBody List<SynthesizeTemporaryDtl> dtls) {
        BigDecimal outPhaseInterest = dtls.get(0).getOutPhaseInterest();
        synthesizeTemporaryService.batchUpdateItems(dtls,outPhaseInterest);
        return R.success();
    }

    @GetMapping("supplier/synthesizeTemporary/supplierDeleteInfo")
    @ApiOperation(value = "供应商删除单据（供应商）")
    @LogRecord(title = "大宗临购清单",businessType = BusinessType.DELETE,operatorType = OperatorType.MANAGE,isSaveRequestData = true)

    public R supplierDeleteInfo(String id) {
        synthesizeTemporaryService.supplierDeleteInfo(id);
        return R.success();
    }

    @GetMapping("supplier/synthesizeTemporary/exportExcel")
    @ApiOperation(value = "导出excel")
    @NotResubmit
    @LogRecord(title = "大宗临购清单",businessType = BusinessType.EXPORT,operatorType = OperatorType.MANAGE,isSaveRequestData = true)

    public void exportExcel(String id, HttpServletResponse response) {
        synthesizeTemporaryService.exportExcel(id,response);
    }


    @PostMapping("supplier/synthesizeTemporary/auditBusiness")
    @ApiOperation(value = "审核单据")
    @NotResubmit
    @IsRole(roleName = RoleEnum.ROLE_12)
    @LogRecord(title = "大宗临购清单",businessType = BusinessType.UPDATE,operatorType = OperatorType.MANAGE,isSaveRequestData = true)

    public R auditBusiness(@RequestBody AuditBusinessDTO dto) {
        synthesizeTemporaryService.auditBusiness(dto);
        return R.success();
    }


    @PostMapping("supplier/synthesizeTemporary/auditRefuseOrg")
    @ApiOperation(value = "供应商拒绝单据给收货单位")
    @NotResubmit
    @LogRecord(title = "大宗临购清单",businessType = BusinessType.UPDATE,operatorType = OperatorType.MANAGE,isSaveRequestData = true)

    public R auditRefuseOrg(@RequestBody AuditBusinessDTO dto) {
        synthesizeTemporaryService.auditRefuseOrg(dto);
        return R.success();
    }

    // 生成大宗临购清单竞价
    @PostMapping("supplier/synthesizeTemporary/createBidding")
    @ApiOperation(value = "生成大宗临购清单竞价")
    @NotResubmit
    @LogRecord(title = "大宗临购清单",businessType = BusinessType.INSERT,operatorType = OperatorType.MANAGE,isSaveRequestData = true)
    public R createBidding(@RequestBody SynthesizeTemporaryDto synthesizeTemporaryDto) {
        synthesizeTemporaryService.createBidding(synthesizeTemporaryDto);
        return R.success();
    }


}

