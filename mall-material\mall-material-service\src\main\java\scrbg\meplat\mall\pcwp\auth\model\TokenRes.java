package scrbg.meplat.mall.pcwp.auth.model;

import lombok.Data;

@Data
public class TokenRes {
    private Integer isExternal;   // 是否是外部人员(0:否|1:是)
    private String openId;        // TT员工Id
    private String ssoToken;      // TT Token
    private String token;         // 令牌
    private String userId;        // 用户Id
    private String userName;      // 用户名
    private String userNumber;    // 用户Id
}
