package scrbg.meplat.mall.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import scrbg.meplat.mall.dto.free.TotalCountFreeVO;
import scrbg.meplat.mall.entity.PlatformYearFee;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;
import org.apache.ibatis.annotations.Mapper;

/**
 * @描述：平台年费表 Mapper 接口
 * @作者: ye
 * @日期: 2024-01-24
 */
@Mapper
@Repository
public interface PlatformYearFeeMapper extends BaseMapper<PlatformYearFee> {

    /**
     * 平台统计报表
     * @param pages
     * @param jsonObject
     * @return
     */
    IPage<TotalCountFreeVO> totalCountFree(Page<TotalCountFreeVO> pages,@Param("dto") JSONObject jsonObject);
}