package scrbg.meplat.mall.entity.excelTemplate;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2023-03-07 9:19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@HeadRowHeight(40)
@ColumnWidth(25)
@ContentRowHeight(40)
public class ArrearageShopExcel implements Serializable {

//        private static final long serialVersionUID = -5144055068797033748L;

        @ExcelProperty(value = "序号（自增即可）", index = 0)
        private Long id;

        @ExcelProperty(value = "供应商名称", index = 1)
        private String supplierName;

        @ExcelProperty(value = "缴费情况(0已缴费，1未缴费)", index = 2)
        private Integer isArrearage;


}


