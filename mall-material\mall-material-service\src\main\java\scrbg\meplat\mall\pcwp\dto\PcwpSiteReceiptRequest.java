package scrbg.meplat.mall.pcwp.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * PCWP现场收料请求对象
 *
 * 请求示例：
 * {
 *   "data": [
 *     {
 *       "amount": 0,
 *       "dtlId": "",
 *       "quantity": 0
 *     }
 *   ],
 *   "keyId": "",
 *   "orgId": ""
 * }
 *
 * <AUTHOR>
 * @date 2024
 */
@Data
public class PcwpSiteReceiptRequest {

    /**
     * 反写数据
     */
    private List<WriteBackBillQuantity> data;

    /**
     * 本次操作keyId
     */
    private String keyId;

    /**
     * 机构ID
     */
    private String orgId;

    /**
     * 反写单据数量对象
     */
    @Data
    public static class WriteBackBillQuantity {

        /**
         * 金额 (number类型)
         */
        private BigDecimal amount;

        /**
         * 现场收料明细Id
         */
        private String dtlId;

        /**
         * 数量 (number类型)
         */
        private BigDecimal quantity;
    }
}
