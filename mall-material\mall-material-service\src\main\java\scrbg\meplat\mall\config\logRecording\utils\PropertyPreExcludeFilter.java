package scrbg.meplat.mall.config.logRecording.utils;

/**
 * @program: maill_api
 * @description:
 * @author: 代文翰
 * @create: 2024-11-04 14:34
 **/

import com.alibaba.fastjson.serializer.SimplePropertyPreFilter;

/**
 * 排除JSON敏感属性
 *
 * <AUTHOR>
 */
public class PropertyPreExcludeFilter extends SimplePropertyPreFilter
{
    public PropertyPreExcludeFilter()
    {
    }

    public PropertyPreExcludeFilter addExcludes(String... filters)
    {
        for (int i = 0; i < filters.length; i++)
        {
            this.getExcludes().add(filters[i]);
        }
        return this;
    }
}
