package scrbg.meplat.mall.controller.website.fronDesk;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import scrbg.meplat.mall.entity.File;
import scrbg.meplat.mall.service.FileService;

import java.util.List;

@RestController
@RequestMapping("/w/file")
@ApiSort(value = 100)
@Api(tags = "首页内容（前台附件）")
public class WFileController {

    @Autowired
    public FileService fileService;

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class, required = true),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class,required = true),
            @DynamicParameter(name = "relevanceType", value = "关联类型（1商品2问答3消息4店铺,5内容）", dataTypeClass = String.class,required = true),
            @DynamicParameter(name = "programaKey", value = "栏目Key", dataTypeClass = String.class,required = true),
            @DynamicParameter(name = "relevanceId", value = "关联id", dataTypeClass = String.class,required = true)
    })
    public PageR<File> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = fileService.queryPage(jsonObject, new LambdaQueryWrapper<File>());
        return PageR.success(page);
    }

    @PostMapping("/createBath")
    @ApiOperation(value = "根据实体属性分页查询")
    public R createBath(@RequestBody List<File> files) {
         fileService.createBathFiles(files);
        return R.success();
    }


}
