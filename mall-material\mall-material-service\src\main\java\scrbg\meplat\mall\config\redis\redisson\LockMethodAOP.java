package scrbg.meplat.mall.config.redis.redisson;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.util.ThreadLocalUtil;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.nio.charset.Charset;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 防重复提交注解的实现，使用AOP。
 * </p>
 *
 */
@Aspect
@Component
public class LockMethodAOP {
   private static final Logger log = LoggerFactory.getLogger(LockMethodAOP.class);

   @Resource
   private RedisLock redisLock;

   /**
    * 这里注意，我的注解写在同一个包下所以没有包名，如果换自己的目录，要改为@annotation(com.xxx.NotResubmit)加上完整包名.
    */
   @Around("@annotation(NotResubmit)")
   public Object interceptor(ProceedingJoinPoint pjp) throws Throwable {
      // 获取到这个注解
      MethodSignature signature = (MethodSignature) pjp.getSignature();
      Method method = signature.getMethod();
      NotResubmit lock = method.getAnnotation(NotResubmit.class);

//      final String lockKey = generateKey(pjp);
      // 只有登陆才能使用
      final String lockKey = "lock:userId:" +  ThreadLocalUtil.getCurrentUser().getUserId() + ":" + generateKey(pjp);
//      final String lockKey = "lock:userId:" +  "1650395779687841794" + ":" + generateKey(pjp);
//      System.out.println(lockKey);

      // 上锁
      final boolean success = redisLock.Rlock(lockKey, null, lock.delaySeconds(), TimeUnit.SECONDS);
      if (!success) {
         // 这里也可以改为自己项目自定义的异常抛出
         throw new BusinessException("业务操作频繁！");
      }
      return pjp.proceed();
   }

   // TODO 这里append实体属性时用到了toString()方法，所以要求我们生成的实体对象一定要有toString()方法。
   // 这里采用用户id
   private String generateKey(ProceedingJoinPoint pjp) {
      StringBuilder sb = new StringBuilder();
      Signature signature = pjp.getSignature();
      MethodSignature methodSignature = (MethodSignature) signature;
      Method method = methodSignature.getMethod();
      sb.append(pjp.getTarget().getClass().getName())//类名
            .append(method.getName());//方法名
      // 放重点击不要使用重载方法！
//      for (Object o : pjp.getArgs()) {
//         sb.append(o.toString());
//      }
      return DigestUtils.md5DigestAsHex(sb.toString().getBytes(Charset.defaultCharset()));
   }

}
