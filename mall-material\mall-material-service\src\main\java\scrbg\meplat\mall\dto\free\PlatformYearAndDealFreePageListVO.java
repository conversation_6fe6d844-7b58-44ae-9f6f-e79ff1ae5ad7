package scrbg.meplat.mall.dto.free;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @create 2024-04-09 15:46
 */
@Data
public class PlatformYearAndDealFreePageListVO {
    @ApiModelProperty(value = "店铺id")
    private String shopId;
    @ApiModelProperty(value = "店铺名称")
    private String shopName;
    @ApiModelProperty(value = "企业id")
    private String enterpriseId;
    @ApiModelProperty(value = "企业名称")
    private String enterpriseName;
    @ApiModelProperty(value = "年费服务到期时间（为null或者小于当前时间都是过期）")
    private LocalDate serveEndTime;
    @ApiModelProperty(value = "年费是否未过期")
    private Integer outTime;
    @ApiModelProperty(value = "服务类型（1店铺年费2招标年费）")
    private Integer serveType;




    @ApiModelProperty(value = "关联编号")
    private String relevanceNu;
    @ApiModelProperty(value = "交易金额")
    private BigDecimal dealAmount;
    @ApiModelProperty(value = "收取比例（%）")
    private BigDecimal feeRatio;
    @ApiModelProperty(value = "交易费用（需缴费金额）")
    private BigDecimal serveFee;
    @ApiModelProperty(value = "已缴费交易费用金额")
    private BigDecimal payFee;




}
