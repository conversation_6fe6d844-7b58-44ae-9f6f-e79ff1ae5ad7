package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.entity.OrderSelectPlan;
import scrbg.meplat.mall.entity.Orders;
import scrbg.meplat.mall.service.OrderSelectPlanService;

import java.util.List;

/**
 * @描述：控制类
 * @作者: ye
 * @日期: 2023-02-28
 */
@RestController
@RequestMapping("/")
@Api(tags = "订单计划关联")
public class OrderSelectPlanController {

    @Autowired
    public OrderSelectPlanService orderSelectPlanService;

    @PostMapping("orderSelectPlan/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<OrderSelectPlan> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = orderSelectPlanService.queryPage(jsonObject, new LambdaQueryWrapper<OrderSelectPlan>());
        return PageR.success(page);
    }

    @GetMapping("orderSelectPlan/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<OrderSelectPlan> findById(String id) {
        OrderSelectPlan orderSelectPlan = orderSelectPlanService.getById(id);
        return R.success(orderSelectPlan);
    }

    @PostMapping("orderSelectPlan/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody OrderSelectPlan orderSelectPlan) {
        orderSelectPlanService.create(orderSelectPlan);
        return R.success();
    }

    @PostMapping("orderSelectPlan/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody OrderSelectPlan orderSelectPlan) {
        orderSelectPlanService.update(orderSelectPlan);
        return R.success();
    }

    @GetMapping("orderSelectPlan/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        orderSelectPlanService.delete(id);
        return R.success();
    }

    @PostMapping("orderSelectPlan/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")
    public R deleteBatch(@RequestBody List<String> ids) {
        orderSelectPlanService.removeByIds(ids);
        return R.success();
    }

    @PostMapping("orderSelectPlan/recoverPlanNum")
    @ApiOperation(value = "恢复计划数量")
    public R recoverPlanNum(List<String> orderItemId) {
        orderSelectPlanService.recoverPlanNum(orderItemId);
        return R.success();
    }

    @PostMapping("performanceManage/orderSelectPlan/getContactPlanPageList")
    @ApiOperation(value = "获取可对账的合同或计划列表（获取采购员的）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
            @DynamicParameter(name = "productType", value = "商品类型：0物资 10零星采购，11办公用品, 12大宗月供订单", dataTypeClass = Integer.class)
    })
    public PageR<OrderSelectPlan> getContactPlanPageList(@RequestBody JSONObject jsonObject) {
        PageUtils page = orderSelectPlanService.getContactPlanPageList(jsonObject, new LambdaQueryWrapper<OrderSelectPlan>());
        return PageR.success(page);
    }

    @PostMapping("performanceManage/orderSelectPlan/selectOrderListByPlanNo")
    @ApiOperation(value = "根据计划编号查询订单状态")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
            @DynamicParameter(name = "productType", value = "商品类型：0物资 10零星采购，11办公用品, 12大宗月供订单", dataTypeClass = Integer.class)
    })
    public R<List<Orders>> selectOrderListByPlanNo(@RequestBody JSONObject jsonObject) {
        List<Orders> ordersList = orderSelectPlanService.selectOrderListByPlanNo(jsonObject);
        return R.success(ordersList);
    }

    @PostMapping("performanceManage/orderSelectPlan/supplierGetContactPlanPageList")
    @ApiOperation(value = "获取可对账的合同或计划列表（获取供应商的）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
            @DynamicParameter(name = "productType", value = "商品类型：0物资 10零星采购，11办公用品, 12大宗月供订单", dataTypeClass = Integer.class)
    })
    public PageR<OrderSelectPlan> supplierGetContactPlanPageList(@RequestBody JSONObject jsonObject) {
        PageUtils page = orderSelectPlanService.supplierGetContactPlanPageList(jsonObject, new LambdaQueryWrapper<OrderSelectPlan>());
        return PageR.success(page);
    }

    @PostMapping("performanceManage/orderSelectPlan/supplierGetEnterprisePageList")
    @ApiOperation(value = "获取可对账的项目部")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
            @DynamicParameter(name = "createType", value = "分组依据 //1采购方  2供应商", dataTypeClass = Integer.class),
            @DynamicParameter(name = "productType", value = "商品类型：0物资 10零星采购，11办公用品, 12大宗月供订单", dataTypeClass = Integer.class)
    })
    public PageR<OrderSelectPlan> supplierGetEnterprisePageList(@RequestBody JSONObject jsonObject) {
        PageUtils page = orderSelectPlanService.supplierGetEnterprisePageList(jsonObject, new LambdaQueryWrapper<OrderSelectPlan>());
        return PageR.success(page);
    }

    @PostMapping("performanceManage/orderSelectPlan/getContactSupplierPageList")
    @ApiOperation(value = "获取可对账的合同或计划列表（获取采购员的供货单位分组）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
            @DynamicParameter(name = "productType", value = "商品类型：l 10零星采购，11办公用品, 12大宗月供订单", dataTypeClass = Integer.class)
    })
    public PageR<OrderSelectPlan> getContactSupplierPageList(@RequestBody JSONObject jsonObject) {
        PageUtils page = orderSelectPlanService.getContactSupplierPageList(jsonObject, new LambdaQueryWrapper<OrderSelectPlan>());
        return PageR.success(page);
    }

}

