package scrbg.meplat.mall.openInterface.pcwp2;

import com.alibaba.fastjson.JSON;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import scrbg.meplat.mall.dto.thirdapi.BatchUpdateMaterialDtlState;
import scrbg.meplat.mall.dto.thirdapi.MaterialDtlDTO;
import scrbg.meplat.mall.entity.InterfaceLogs;
import scrbg.meplat.mall.entity.ProductCategory;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.service.InterfaceLogsService;
import scrbg.meplat.mall.service.ProductCategoryService;
import scrbg.meplat.mall.util.LogUtil;
import scrbg.meplat.mall.util.R;

import javax.validation.Valid;
import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/thirdApi/material")
@Api(tags = "物资管理")
public class MaterialController {
    @Autowired
    public ProductCategoryService productCategoryService;
    @Autowired
    private InterfaceLogsService interfaceLogsService;

    @PostMapping("/saveMaterialInfo")
    @ApiOperation(value = "新增或修改物资商品")
    public R saveMaterialInfo( @RequestBody MaterialDtlDTO dto) {
        String idStr= UUID.randomUUID().toString();
        try {
            /**
             * 这样判断是为了兼容PCWP1客户端，因为他们用的数据库是SQLServer,并且在插入字符串单引号的时候没有预编译，导致插入会导致异常。
             * */
            filterMaterialDtlDTO(dto);//清洗一下dto数据，防止最终推送到PCWP异常问题。
            boolean isSpecialCharMaterialName = isSpecialChar(dto.getMaterialName());
            if(isSpecialCharMaterialName){
                throw new BusinessException(500, "【物料名称】"+dto.getMaterialName()+"开头或者结尾不能使用单引号");
            }
            boolean isSpecialCharSpec = isSpecialChar(dto.getSpec());
            if(isSpecialCharSpec){
                throw new BusinessException(500, "【规格】"+dto.getSpec()+"开头或者结尾不能使用单引号");
            }
            boolean isSpecialCharUnit = isSpecialChar(dto.getUnit());
            if(isSpecialCharUnit){
                throw new BusinessException(500, "【计量单位】"+dto.getUnit()+"开头或者结尾不能使用单引号");
            }
            productCategoryService.saveMaterialInfo(idStr,dto);
            return R.success();
        }catch (Exception e) {
            LogUtil.writeErrorLog(idStr,"saveMaterialInfo",dto,null,null,e.getMessage(), ClassAndBasicLibraryController.class);
            InterfaceLogs iLog = new InterfaceLogs();
            iLog.setSecretKey(idStr);
            iLog.setClassPackage(ClassAndBasicLibraryController.class.getName());
            iLog.setMethodName("saveMaterialInfo");
            iLog.setFarArguments(JSON.toJSONString(dto));
            iLog.setIsSuccess(0);
            iLog.setLogType(3);
            iLog.setErrorInfo(e.getMessage());
            interfaceLogsService.create(iLog);
            throw new BusinessException(500,e.getMessage());
        }
    }

    private void filterMaterialDtlDTO(MaterialDtlDTO dto) {
        if(dto.getMaterialName()!=null){
            dto.setMaterialName(dto.getMaterialName().trim());
        }
        if(dto.getSpec()!=null){
            dto.setSpec(dto.getSpec().trim());
        }
        if(dto.getUnit()!=null){
            dto.setUnit(dto.getUnit().trim());
        }
    }

    private boolean isSpecialChar(String str) {
        boolean specialQuote  = str != null && !str.isEmpty() &&
                (str.charAt(0) == '\'' || str.charAt(str.length() - 1) == '\'');
        return specialQuote;
    }

    @PostMapping("/batchUpdateMaterialDtlState")
    @ApiOperation(value = "启用和停用物资基础库")
    public R batchUpdateMaterialDtlState(@RequestBody BatchUpdateMaterialDtlState dto) {
        String idStr= UUID.randomUUID().toString();
        StringBuilder farArguments = new StringBuilder();
        try {
            productCategoryService.batchUpdateMaterialDtlState(idStr,dto,farArguments);
            return R.success();
        }catch (Exception e) {
            LogUtil.writeErrorLog(idStr,"batchUpdateMaterialDtlState",dto,null,null,e.getMessage(), ClassAndBasicLibraryController.class);
            InterfaceLogs iLog = new InterfaceLogs();
            iLog.setSecretKey(idStr);
            iLog.setClassPackage(ClassAndBasicLibraryController.class.getName());
            iLog.setMethodName("batchUpdateMaterialDtlState");
            iLog.setLocalArguments(JSON.toJSONString(dto));
            iLog.setFarArguments(JSON.toJSONString(farArguments));
            iLog.setIsSuccess(0);
            iLog.setLogType(3);
            iLog.setErrorInfo(e.getMessage());
            interfaceLogsService.create(iLog);
            throw new BusinessException(500,e.getMessage());
        }
    }


    @PostMapping("/batchUpdateCategoryLibraryState")
    @ApiOperation(value = "批量更新物资类别使用状态")
    public R batchUpdateCategoryLibraryState(@RequestBody BatchUpdateMaterialDtlState dto) {
        String idStr= UUID.randomUUID().toString();
        StringBuilder farArguments = new StringBuilder();
        try {
            productCategoryService.batchUpdateCategoryLibraryState(idStr,dto,  farArguments);
            return R.success();
        }catch (Exception e) {
            LogUtil.writeErrorLog(idStr,"batchUpdateCategoryLibraryState",dto,null,null,e.getMessage(), ClassAndBasicLibraryController.class);
            InterfaceLogs iLog = new InterfaceLogs();
            iLog.setSecretKey(idStr);
            iLog.setClassPackage(ClassAndBasicLibraryController.class.getName());
            iLog.setMethodName("batchUpdateCategoryLibraryState");
            iLog.setFarArguments(JSON.toJSONString(farArguments));
            iLog.setLocalArguments(JSON.toJSONString(dto));
            iLog.setIsSuccess(0);
            iLog.setLogType(3);
            iLog.setErrorInfo(e.getMessage());
            interfaceLogsService.create(iLog);
            throw new BusinessException(500,e.getMessage());
        }
    }
}
