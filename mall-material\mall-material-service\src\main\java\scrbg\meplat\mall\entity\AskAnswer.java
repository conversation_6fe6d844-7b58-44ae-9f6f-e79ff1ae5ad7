package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;
import java.util.Date;

/**
 * @描述：问答
 * @作者: y
 * @日期: 2022-11-24
 */
@ApiModel(value = "问答")
@Data
@TableName("ask_answer")
public class AskAnswer extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "问答id")
    private String askAnswerId;

    @ApiModelProperty(value = "标题")

    private String title;


    @ApiModelProperty(value = "信息类型（0求购1求租2招标）")

    private Integer type;


    @ApiModelProperty(value = "发件人Id")

    private String sendId;


    @ApiModelProperty(value = "发件人名称")

    private String sendName;


    @ApiModelProperty(value = "发件人类型（0店铺1用户2平台）")

    private Integer sendType;


    @ApiModelProperty(value = "内容")

    private String content;


    @ApiModelProperty(value = "关联id")

    private String relevanceId;


    @ApiModelProperty(value = "发件时间")

    private Date sendDate;


    @ApiModelProperty(value = "收件人ids（逗号分割）")

    private Integer acceptIds;


    @ApiModelProperty(value = "状态（0显示1不显示）默认显示")

    private Integer state;


    @ApiModelProperty(value = "是否附件（0否1是）")

    private Integer isFile;


}
