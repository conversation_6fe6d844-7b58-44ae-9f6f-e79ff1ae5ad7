package scrbg.meplat.mall.dto.bidding;/**
 * <AUTHOR>
 * @date 2023/7/19
 */

import io.swagger.annotations.ApiModelProperty;

/**
 * @program: maill_api
 *
 * @description: 我参与的竞价
 *
 * @author: 代文翰
 *
 * @create: 2023-07-19 16:10
 **/
public class MyBiddingDto {
    @ApiModelProperty(value = "竞价标题")
    private String title;

    @ApiModelProperty(value = "竞价类型")
    private String type;
    @ApiModelProperty(value = "竞价编号")
    private String biddingSn;
    @ApiModelProperty(value = "竞价状态")
    private String state;
    @ApiModelProperty(value = "开始时间")
    private String startTime;
    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @ApiModelProperty(value = "公开状态")
    private String publicityState;
}
