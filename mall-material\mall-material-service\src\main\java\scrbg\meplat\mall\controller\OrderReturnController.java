package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.config.logRecording.LogRecord;
import scrbg.meplat.mall.config.logRecording.enums.BusinessType;
import scrbg.meplat.mall.config.logRecording.enums.OperatorType;
import scrbg.meplat.mall.entity.OrderItem;
import scrbg.meplat.mall.entity.OrderReturn;
import scrbg.meplat.mall.service.OrderReturnService;

import java.util.List;

/**
 * @描述：控制类
 * @作者: sund
 * @日期: 2023-01-31
 */
@RestController
@RequestMapping("/")
@Api(tags = "退货管理")
public class OrderReturnController {

    @Autowired
    public OrderReturnService orderReturnService;

    @PostMapping("/platform/orderReturn/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<OrderReturn> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = orderReturnService.queryPage(jsonObject, new LambdaQueryWrapper<OrderReturn>());
        return PageR.success(page);
    }
    @PostMapping("/shopManage/orderReturn/twoListByEntity")
    @ApiOperation(value = "多供方退货记录查询（店铺）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "sourceType", value = "类型（1 合同 2 计划 6大宗临采）", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<OrderReturn> TwolistByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = orderReturnService.twoListByEntity(jsonObject, new LambdaQueryWrapper<OrderReturn>());
        return PageR.success(page);
    }
    @PostMapping("/shopManage/orderReturn/listByEntity")
    @ApiOperation(value = "店铺-退货记录")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<OrderReturn> list(@RequestBody JSONObject jsonObject){
        PageUtils page= orderReturnService.shopOrderReturnList(jsonObject,new LambdaQueryWrapper<OrderReturn>());
        return PageR.success(page);
    }


    @GetMapping("/orderReturn/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<OrderReturn> findById(String id) {
        OrderReturn orderReturn = orderReturnService.getById(id);
        return R.success(orderReturn);
    }
    @GetMapping("/orderReturn/findByIdTwoInfo")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<OrderReturn> findByIdTwoInfo(String id) {
        OrderReturn orderReturn = orderReturnService.getById(id);
        return R.success(orderReturn);
    }

    @GetMapping("/orderReturn/getOrderItemListByOrderReturnId")
    @ApiOperation(value = "根据退货id查询订单信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public PageR<OrderItem> orderReturnService(String orderReturnId) {
        PageUtils page = orderReturnService.getOrderItemListByOrderReturnId(orderReturnId);
        return PageR.success(page);
    }

    @GetMapping("/orderReturn/getOrderItemTwoListByOrderReturnId")
    @ApiOperation(value = "根据退货id查询订单信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public PageR<OrderItem> orderReturnTwoService(String orderReturnId) {
        PageUtils page = orderReturnService.getOrderItemTwoListByOrderReturnId(orderReturnId);
        return PageR.success(page);
    }



    @PostMapping("orderReturn/create")
    @ApiOperation(value = "新增")
    @LogRecord(title = "退货管理",businessType = BusinessType.INSERT,operatorType = OperatorType.MANAGE)

    public R save(@RequestBody OrderReturn orderReturn) {
        orderReturnService.create(orderReturn);
        return R.success();
    }

    @PostMapping("orderReturn/update")
    @ApiOperation(value = "修改")
    @LogRecord(title = "退货管理",businessType = BusinessType.UPDATE,operatorType = OperatorType.MANAGE)

    public R update(@RequestBody OrderReturn orderReturn) {
        orderReturnService.update(orderReturn);
        return R.success();
    }

    @PostMapping("orderReturn/updateState")
    @ApiOperation(value = "修改")
    @LogRecord(title = "退货管理",businessType = BusinessType.UPDATE,operatorType = OperatorType.MANAGE)

    public R updateState(@RequestBody OrderReturn orderReturn) {
        orderReturnService.updateState(orderReturn);
        return R.success();
    }

    @PostMapping("orderReturn/updateTwoOrderItemState")
    @ApiOperation(value = "修改")
    @LogRecord(title = "退货管理",businessType = BusinessType.UPDATE,operatorType = OperatorType.MANAGE)
    public R updateTwoOrderItemState(@RequestBody OrderReturn orderReturn) {
        orderReturnService.updateTwoOrderItemState(orderReturn);
        return R.success();
    }

    @GetMapping("orderReturn/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        orderReturnService.delete(id);
        return R.success();
    }


    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")
    public R deleteBatch(@RequestBody List<String> ids) {
        orderReturnService.removeByIds(ids);
        return R.success();
    }
}

