package scrbg.meplat.mall.dto.plan;

import com.scrbg.common.utils.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @描述：采购计划明细表查询参数
 * @作者: ye
 * @日期: 2025-05-27
 */
@ApiModel(value = "采购计划明细表查询参数")
@Data
@EqualsAndHashCode(callSuper = true)
public class PlanDetailDTO extends PageParam implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "计划ID")
    private String billId;

    @ApiModelProperty(value = "物料ID")
    private String matterId;

    @ApiModelProperty(value = "物料名称")
    private String matterName;

    @ApiModelProperty(value = "规格")
    private String spec;

    @ApiModelProperty(value = "物料单位")
    private String matterUnit;

    @ApiModelProperty(value = "物料用途")
    private String matterUse;

    @ApiModelProperty(value = "分类ID")
    private String classId;

    @ApiModelProperty(value = "分类名称")
    private String className;

    @ApiModelProperty(value = "商品名称")
    private String tradeName;

    @ApiModelProperty(value = "商品ID")
    private String tradeId;

    @ApiModelProperty(value = "数量")
    private BigDecimal number;

    @ApiModelProperty(value = "单价")
    private BigDecimal price;

    @ApiModelProperty(value = "含税单价")
    private BigDecimal taxPrice;

    @ApiModelProperty(value = "金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "税额")
    private BigDecimal taxAmount;

    @ApiModelProperty(value = "库存数量")
    private BigDecimal stockNumber;

    @ApiModelProperty(value = "采购类型(-1表示未定义)")
    private Integer purchaseType;

    @ApiModelProperty(value = "顶级分类ID")
    private String topClassId;

    @ApiModelProperty(value = "顶级分类名称")
    private String topClassName;

    @ApiModelProperty(value = "供应商ID")
    private String storageId;

    @ApiModelProperty(value = "供应商名称")
    private String storageName;

    @ApiModelProperty(value = "供应商组织机构ID")
    private String storageOrgId;

    @ApiModelProperty(value = "组织机构简称")
    private String orgShort;

    @ApiModelProperty(value = "统一社会信用代码")
    private String creditCode;

    @ApiModelProperty(value = "已消耗金额")
    private BigDecimal consumeAmount;

    @ApiModelProperty(value = "未消耗金额")
    private BigDecimal notConsumeAmount;

    @ApiModelProperty(value = "已消耗数量")
    private BigDecimal consumeNumber;

    @ApiModelProperty(value = "未消耗数量")
    private BigDecimal notConsumeNumber;

}