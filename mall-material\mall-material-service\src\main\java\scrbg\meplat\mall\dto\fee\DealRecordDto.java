package scrbg.meplat.mall.dto.fee;

import java.time.LocalDate;

import lombok.Data;
import lombok.EqualsAndHashCode;
import scrbg.meplat.mall.dto.PageDto;
@Data
@EqualsAndHashCode(callSuper = true)
public class DealRecordDto extends PageDto {
    /**
     * 缴费截止日期最早
     */
    private LocalDate startDeadline;
    /**
     * 缴费截止日期最晚
     */
    private LocalDate endDeadline;
    /**
     * 
     */
    private Integer state;
}
