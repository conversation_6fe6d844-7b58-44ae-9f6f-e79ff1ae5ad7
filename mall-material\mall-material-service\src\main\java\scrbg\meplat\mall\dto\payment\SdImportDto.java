package scrbg.meplat.mall.dto.payment;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @program: maill_api
 * @description: 蜀道企业导入数据对象
 * @author: 代文翰
 * @create: 2023-09-05 23:19
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@HeadRowHeight(40)
@ColumnWidth(40)
@ContentRowHeight(30)
@ContentFontStyle(fontHeightInPoints=16)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
public class SdImportDto implements Serializable {
    private static final long serialVersionUID = 1L;
    @ExcelProperty(value = "单位名称")
    private String enterpriseName;
    @ExcelProperty(value = "隶属企业")

    private String affiliationEnterprise;
    @ExcelProperty(value = "单位类别(1:一类 2:二类 3:三类)")

    private String enterpriseCategory;
    @ExcelProperty(value = "调整")

    private String adjust;
    @ExcelProperty(value = "企业性质")

    private String enterpriseNature;


}
