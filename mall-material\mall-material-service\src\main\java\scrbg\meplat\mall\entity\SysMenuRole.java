package scrbg.meplat.mall.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @描述：菜单角色表
 * @作者: ye
 * @日期: 2023-12-21
 */
@ApiModel(value="菜单角色表")
@Data
@TableName("sys_menu_role")
public class SysMenuRole implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "菜单角色表id")
    private String sysMenuRoleId;

    @ApiModelProperty(value = "菜单id")

    private String menuId;


    @ApiModelProperty(value = "角色id")

    private String roleId;


    @ApiModelProperty(value = "逻辑删除 -1: 删除 0:未删除")
    @TableField(value = "is_delete", fill = FieldFill.INSERT)
    private Integer isDelete;


}