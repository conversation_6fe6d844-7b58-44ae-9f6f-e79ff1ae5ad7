package scrbg.meplat.mall.entity.excelTemplate;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2023-05-09 9:47
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@HeadRowHeight(40)
@ColumnWidth(25)
//@ContentRowHeight(40)
// 设置表头颜色
//@HeadStyle(fillForegroundColor = 17)
public class ImportSupplerExcelResult implements Serializable {
//    private static final long serialVersionUID = -5144055068797033748L;

    @ExcelProperty(value = "供应商名称")
    private String enterpriseName;

    @ExcelProperty(value = "统一社会信用代码")
    private String socialCreditCode;

    @ExcelProperty(value = "法定代表人")

    private String legalRepresentative;

    @ExcelProperty(value = "联系电话")

    private String adminPhone;

    @ExcelProperty(value = "是否成功")
    @ColumnWidth(10)
    private String state;

    @ExcelProperty(value = "失败原因")
    @ColumnWidth(50)
    private String fail;
}
