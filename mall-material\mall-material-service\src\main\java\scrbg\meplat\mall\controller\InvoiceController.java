package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import scrbg.meplat.mall.config.redis.redisson.NotResubmit;
import scrbg.meplat.mall.entity.EnterpriseInfo;
import scrbg.meplat.mall.entity.SupplierReconciliationDtl;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.service.EnterpriseInfoService;
import scrbg.meplat.mall.service.InvoiceService;
import scrbg.meplat.mall.entity.Invoice;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;
import scrbg.meplat.mall.vo.invoice.AuditRecordVo;
import scrbg.meplat.mall.vo.invoice.DataListPassVo;

import java.util.List;

/**
 * @描述：发票控制类
 * @作者: ye
 * @日期: 2023-11-13
 */
@RestController
@RequestMapping("")
@Api(tags = "发票")
public class InvoiceController {

    @Autowired
    public InvoiceService invoiceService;
    @Autowired
    public EnterpriseInfoService enterpriseInfoService;

    @PostMapping("/invoice/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<Invoice> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = invoiceService.queryPage(jsonObject, new LambdaQueryWrapper<Invoice>());
        return PageR.success(page);
    }

    @PostMapping("/shopManage/invoice/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<Invoice> shopManageListByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = invoiceService.shopManageQueryPage(jsonObject, new LambdaQueryWrapper<Invoice>());
        return PageR.success(page);
    }
    @PostMapping("/platform/invoice/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<Invoice> platformListByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = invoiceService.platformQueryPage(jsonObject, new LambdaQueryWrapper<Invoice>());
        return PageR.success(page);
    }



    @GetMapping("/invoice/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<Invoice> findById(String id) {
        Invoice invoice = invoiceService.getById(id);
        return R.success(invoice);
    }

    @PostMapping("/invoice/create")
    @Transactional
    @NotResubmit
    @ApiOperation(value = "项目部新增")
    public R save(@RequestBody Invoice invoice) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        invoice.setInvoiceClass(1);
        invoice.setEnterpriseId(user.getEnterpriseId());
        invoice.setEnterpriseName(user.getEnterpriseName());
        if (invoice.getEnterpriseName()!=null){
            EnterpriseInfo one = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseName, invoice.getSupplierName())
                    .select(EnterpriseInfo::getEnterpriseId, EnterpriseInfo::getEnterpriseName).one();
            invoice.setSupplierId(one.getEnterpriseId());
            invoice.setSupplierName(one.getEnterpriseName());
        }else {
            throw new BusinessException(500,"供应商公司不存在");
        }
        invoiceService.create(invoice);
        return R.success();
    }

    @PostMapping("/invoice/supplier/create")
    @Transactional
    @NotResubmit
    @ApiOperation(value = "供应商新增开票")
    public R supplierCreate(@RequestBody Invoice invoice) {
        invoice.setInvoiceClass(1);
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        invoice.setSupplierId(user.getEnterpriseId());
        invoice.setSupplierName(user.getEnterpriseName());
        if (invoice.getEnterpriseId()!=null){
            EnterpriseInfo one = enterpriseInfoService.lambdaQuery()
                    .eq(EnterpriseInfo::getInteriorId, invoice.getEnterpriseId())
                    .select(EnterpriseInfo::getEnterpriseId, EnterpriseInfo::getEnterpriseName).one();
            invoice.setEnterpriseId(one.getEnterpriseId());
            invoice.setEnterpriseName(one.getEnterpriseName());
        }else {
            throw new BusinessException(500,"采购员公司不存在");
        }

        invoiceService.create(invoice);
        return R.success();
    }


    @PostMapping("/invoice/update")
    @Transactional
    @NotResubmit
    @ApiOperation(value = "修改")
    public R update(@RequestBody Invoice invoice) {
        invoiceService.update(invoice);
        return R.success();
    }

    @PostMapping("/shopManage/invoice/updateInvoiceState")
    @Transactional
    @ApiOperation(value = "修改")
    public R updateInvoiceState(@RequestBody Invoice invoice) {
        invoiceService.updateInvoiceState(invoice);
        return R.success();
    }


    @PostMapping("/shopManage/invoice/saveFiles")
    @Transactional
    @ApiOperation(value = "修改发票电子版")
    public R saveFiles(@RequestBody Invoice invoice) {
        invoiceService.saveFiles(invoice);
        return R.success();
    }

    @PostMapping("/invoice/changInvoiceState")
    @Transactional
    @NotResubmit
    @ApiOperation(value = "修改(发票作废或者红字申请)")
    public R changInvoiceState(@RequestBody AuditRecordVo auditRecord) {
        invoiceService.changInvoiceState(auditRecord);
        return R.success();
    }

    @GetMapping("/invoice/delete")
    @Transactional
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        invoiceService.delete(id);
        return R.success();
    }


    @PostMapping("/invoice/deleteBatch")
    @Transactional
    @NotResubmit
    @ApiOperation(value = "根据主键批量删除")
    public R deleteBatch(@RequestBody List<String> ids) {
        invoiceService.removeByIds(ids);
        return R.success();
    }

    @PostMapping("/shopManage/invoice/updateBathIds")
    @Transactional
    @ApiOperation(value = "根据主键批量通过")
    public R updateBathIds(@RequestBody DataListPassVo dataListPassVos) {
        invoiceService.updateBathIds(dataListPassVos);
        return R.success();
    }
    @PostMapping("invoice/listBySupplierIdIds")
    @ApiOperation(value = "根据二级对账单id查询可生成发票数据")
    public R<Invoice>  ListByIds(@RequestBody JSONObject jsonObject) {
        Invoice invoice =invoiceService.ListByBillIds(jsonObject,new LambdaQueryWrapper<Invoice>());
        return R.success(invoice);
    }


    @PostMapping("/invoice/twoEnterpriseCreate/create")
    @NotResubmit
    @Transactional
    @ApiOperation(value = "二级对账单发票新增（自营店）")
    public R twoEnterpriseCreate(@RequestBody Invoice invoice) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        invoice.setEnterpriseId(user.getEnterpriseId());
        invoice.setEnterpriseName(user.getEnterpriseName());
        invoiceService.twoEnterpriseCreate(invoice);
        return R.success();
    }


    @PostMapping("/invoice/twoSupplierCreate/create")
    @NotResubmit
    @Transactional
    @ApiOperation(value = "二级对账单发票新增（二级供应商）")
    public R twoSupplierCreate(@RequestBody Invoice invoice) {
        invoiceService.twoSupplierCreate(invoice);
        return R.success();
    }


}

