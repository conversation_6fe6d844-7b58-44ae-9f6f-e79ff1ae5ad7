package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;
import java.util.Date;
/**
 * @描述：
 * @作者: y
 * @日期: 2022-12-07
 */
@ApiModel(value="")
@Data
@TableName("category_columns")
public class CategoryColumns extends MustBaseEntity implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "栏目id")
    private String columnId;

    @ApiModelProperty(value = "栏目编号")

    private String columnNumber;


    @ApiModelProperty(value = "栏目名称")

    private String columnName;


    @ApiModelProperty(value = "栏目状态（1：使用 0：禁用）")

    private Integer state;

    @ApiModelProperty(value = "是否固定（1：固定 0：禁用）")

    private Integer isFixed;

    @ApiModelProperty(value = "编码")

    private String  code;


    @ApiModelProperty(value = "发布时间")

    private Date gmtRelease;






}
