package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.entity.File;
import scrbg.meplat.mall.entity.ShopSupplierRele;
import scrbg.meplat.mall.service.ShopSupplierReleService;

import java.util.List;

/**
 * @描述：店铺供方关联表控制类
 * @作者: ye
 * @日期: 2023-06-05
 */
@RestController
@RequestMapping("/")
@Api(tags = "店铺供方关联表")
public class ShopSupplierReleController {

    @Autowired
    public ShopSupplierReleService shopSupplierReleService;

    @PostMapping("shopSupplierRele/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
    })
    public PageR<ShopSupplierRele> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = shopSupplierReleService.queryPage(jsonObject, new LambdaQueryWrapper<ShopSupplierRele>());
        return PageR.success(page);
    }


    @PostMapping("shopSupplierRele/listByShopId")
    @ApiOperation(value = "根据店铺查询对应关联的供应商")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
            @DynamicParameter(name = "supplierType", value = "供应商类型", dataTypeClass = String.class),
            @DynamicParameter(name = "templateWarehousedIs", value = "定制化模式", dataTypeClass = String.class),
            @DynamicParameter(name = "supplierGroup", value = "群组", dataTypeClass = String.class),
    })
    public PageR<ShopSupplierRele> listByShopId(@RequestBody JSONObject jsonObject) {
        PageUtils page = shopSupplierReleService.listByShopId(jsonObject, new LambdaQueryWrapper<ShopSupplierRele>());
        return PageR.success(page);
    }

    @PostMapping("shopSupplierRele/listBySupplierId")
    @ApiOperation(value = "根据店铺查询对应关联的供应商")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
    })
    public PageR<ShopSupplierRele> listBySupplierId(@RequestBody JSONObject jsonObject) {
        PageUtils page = shopSupplierReleService.listBySupplierId(jsonObject, new LambdaQueryWrapper<ShopSupplierRele>());
        return PageR.success(page);
    }

    @GetMapping("shopSupplierRele/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<ShopSupplierRele> findById(String id) {
        ShopSupplierRele shopSupplierRele = shopSupplierReleService.getById(id);
        return R.success(shopSupplierRele);
    }

    @PostMapping("shopSupplierRele/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody ShopSupplierRele shopSupplierRele) {
        shopSupplierReleService.create(shopSupplierRele);
        return R.success();
    }

    @PostMapping("shopSupplierRele/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody ShopSupplierRele shopSupplierRele) {
        shopSupplierReleService.update(shopSupplierRele);
        return R.success();
    }

    @GetMapping("shopSupplierRele/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        shopSupplierReleService.delete(id);
        return R.success();
    }


    @PostMapping("shopSupplierRele/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")
    public R deleteBatch(@RequestBody List<String> ids) {
        shopSupplierReleService.removeByIds(ids);
        return R.success();
    }

    @PostMapping("shopSupplierRele/updateByBatch")
    @ApiOperation(value = "批量修改排序值")
    public R updateByBatch(@RequestBody List<ShopSupplierRele> shopSupplierRele) {
        shopSupplierReleService.updateBatchById(shopSupplierRele);
        return R.success();
    }


    @PostMapping("supplier/shopSupplierRele/listShopListBySupplierId")
    @ApiOperation(value = "获取本机构下供店铺")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "shopName", value = "店铺名称", dataTypeClass = String.class),
    })
    public PageR<ShopSupplierRele> listShopListBySupplierId(@RequestBody JSONObject jsonObject) {
        PageUtils page = shopSupplierReleService.listShopListBySupplierId(jsonObject, new LambdaQueryWrapper<ShopSupplierRele>());
        return PageR.success(page);
    }

    @PostMapping("supplier/shopSupplierRele/listShopList")
    @ApiOperation(value = "获取下供店铺")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "shopName", value = "店铺名称", dataTypeClass = String.class),
    })
    public PageR<ShopSupplierRele> listShopList(@RequestBody JSONObject jsonObject) {
        PageUtils page = shopSupplierReleService.listShopList(jsonObject, new LambdaQueryWrapper<ShopSupplierRele>());
        return PageR.success(page);
    }


    @GetMapping("shopSupplierRele/shopManage/isTwoSupper")
    @ApiOperation(value = "获取子供应商")
    public R<ShopSupplierRele> isTwoSupper() {
        ShopSupplierRele b = shopSupplierReleService.isTwoSupper();
        return R.success(b);
    }

    @PostMapping("shopSupplierRele/suppliersByCondition")
    @ApiOperation(value = "根据订单条件筛选供应商分页查询")
    @DynamicParameters(name = "根据订单条件筛选供应商", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "shopId", value = "店铺ID", dataTypeClass = String.class),
            @DynamicParameter(name = "startDate", value = "开始日期(yyyy-MM-dd HH:mm:ss)", dataTypeClass = String.class),
            @DynamicParameter(name = "endDate", value = "结束日期(yyyy-MM-dd HH:mm:ss)", dataTypeClass = String.class),
            @DynamicParameter(name = "productType", value = "产品类型(支持单个值如'13'或多个值如'1,10')", dataTypeClass = String.class),
            @DynamicParameter(name = "billType", value = "价格类型(2-固定价格)", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "供应商名称关键字", dataTypeClass = String.class)
    })
    public PageR<ShopSupplierRele> querySuppliersByOrderConditions(@RequestBody JSONObject jsonObject) {
        PageUtils page = shopSupplierReleService.querySuppliersByOrderConditions(jsonObject);
        return PageR.success(page);
    }

    @PostMapping("shopSupplierRele/setSupplierType")
    @ApiOperation(value = "设置供应商类型")
    public R setSupplierType(@RequestBody ShopSupplierRele shopSupplierRele) {
        shopSupplierReleService.setSupplierType(shopSupplierRele);
        return R.success();
    }

    @PostMapping("shopSupplierRele/setSupplierGroup")
    @ApiOperation(value = "设置供应商群组")
    public R setSupplierGroup(@RequestBody ShopSupplierRele shopSupplierRele) {
        shopSupplierReleService.setSupplierGroup(shopSupplierRele);
        return R.success();
    }

    @PostMapping("shopSupplierRele/getFileList/{id}")
    @ApiOperation(value = "获取供应商附件")
    public R querySupplierFile(@PathVariable String id) {
        List<File> files = shopSupplierReleService.querySupplierFile(id);
        return R.success(files);
    }

}

