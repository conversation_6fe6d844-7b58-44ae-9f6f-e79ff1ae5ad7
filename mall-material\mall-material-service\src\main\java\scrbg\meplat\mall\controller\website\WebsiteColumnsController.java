package scrbg.meplat.mall.controller.website;

import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import scrbg.meplat.mall.service.CategoryColumnsService;
import scrbg.meplat.mall.vo.floor.website.WColumnFloorVO;

import java.util.List;

/**
 * @描述：控制类
 * @作者: y
 * @日期: 2022-12-07
 */
@RestController
@RequestMapping("/w/categoryColumns")
@ApiSort(value = 100)
@Api(tags = "首页栏目（前台）")
public class WebsiteColumnsController {

    @Autowired
    public CategoryColumnsService categoryColumnsService;

    @GetMapping("/listBySize")
    @ApiOperation(value = "获取首页栏目")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "size", value = "商品个数", required = true,
                    dataType = "Integer", paramType = "query"),
    })
    public R<List<WColumnFloorVO>> listBySize(Integer size) {
        List<WColumnFloorVO> vos = categoryColumnsService.listBySize(size);
        return R.success(vos);
    }

    @GetMapping("/fixedListBySize")
    @ApiOperation(value = "获取固定首页栏目")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "size", value = "商品个数", required = true,
                    dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "isFixed", value = "固定状态", required = true,
                    dataType = "Integer", paramType = "query"),
    })
    public R<List<WColumnFloorVO>> fixedListBySize(Integer size,Integer isFixed) {
        List<WColumnFloorVO> vos = categoryColumnsService.fixedListBySize(size,isFixed);
        return R.success(vos);
    }

    @GetMapping("/revolvingMaterials")
    @ApiOperation(value = "获取固定首页栏目")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "size", value = "商品个数", required = true,
                    dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "isFixed", value = "固定状态", required = true,
                    dataType = "Integer", paramType = "query"),
    })
    public R<List<WColumnFloorVO>> revolvingMaterials(Integer size,Integer isFixed) {
        List<WColumnFloorVO> vos = categoryColumnsService.revolvingMaterials(size,isFixed);
        return R.success(vos);
    }
}

