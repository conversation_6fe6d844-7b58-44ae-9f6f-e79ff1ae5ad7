package scrbg.meplat.mall.controller;
import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import scrbg.meplat.mall.service.ProductRulesClassService;
import scrbg.meplat.mall.entity.ProductRulesClass;

import java.util.List;

/**
 * @描述：商品预警规则和分类关联表控制类
 * @作者: ye
 * @日期: 2024-03-20
 */
@RestController
@RequestMapping("/productRulesClass")
@Api(tags = "商品预警规则和分类关联表")
public class ProductRulesClassController{

@Autowired
public ProductRulesClassService productRulesClassService;

@PostMapping("/listByEntity")
@ApiOperation(value = "根据实体属性分页查询")
@DynamicParameters(name = "根据实体属性分页查询", properties = {
        @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
        @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
})
public PageR<ProductRulesClass> listByEntity(@RequestBody JSONObject jsonObject){
        PageUtils page= productRulesClassService.queryPage(jsonObject,new LambdaQueryWrapper<ProductRulesClass>());
        return PageR.success(page);
        }

@GetMapping("/findById")
@ApiOperation(value = "根据主键查询")
@ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "ID", required = true,
                dataType = "String", paramType = "query")
})
public R<ProductRulesClass> findById(String id){
    ProductRulesClass productRulesClass = productRulesClassService.getById(id);
        return R.success(productRulesClass);
        }

@PostMapping("/create")
@ApiOperation(value = "新增")
public R save(@RequestBody ProductRulesClass productRulesClass){
    productRulesClassService.create(productRulesClass);
        return R.success();
        }

@PostMapping("/update")
@ApiOperation(value = "修改")
public R update(@RequestBody ProductRulesClass productRulesClass){
    productRulesClassService.update(productRulesClass);
        return R.success();
        }

@GetMapping("/delete")
@ApiOperation(value = "根据主键删除")
@ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "ID", required = true,
                dataType = "String", paramType = "query")
})
public R delete(String id){
    productRulesClassService.delete(id);
        return R.success();
        }


@PostMapping("/deleteBatch")
@ApiOperation(value = "根据主键批量删除")
public R deleteBatch(@RequestBody List<String> ids){
    productRulesClassService.removeByIds(ids);
        return R.success();
        }
}

