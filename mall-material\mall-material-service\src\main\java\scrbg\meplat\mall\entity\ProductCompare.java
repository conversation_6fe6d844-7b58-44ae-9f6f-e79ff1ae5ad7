package scrbg.meplat.mall.entity;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

/**
 * @描述：
 * @作者: ye
 * @日期: 2023-12-12
 */
@ApiModel(value = "")
@Data
@TableName("product_compare")
public class ProductCompare extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String compareId;

    @ApiModelProperty(value = "比价人企业id")

    private String enterpriseId;


    @ApiModelProperty(value = "比价人企业名称（公司名称、供应商公司名称 ）")

    private String enterpriseName;


    @ApiModelProperty(value = "比价提交时间")

    private Date compareTime;

    @ApiModelProperty(value = "比价的商品名称（使用/分割）")
    private String productNames;


    @ApiModelProperty(value = "状态")

    private Integer state;


}