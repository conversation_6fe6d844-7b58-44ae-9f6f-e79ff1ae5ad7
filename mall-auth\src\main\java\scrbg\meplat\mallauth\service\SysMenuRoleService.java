package scrbg.meplat.mallauth.service;

import scrbg.meplat.mallauth.entity.SysMenuRole;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mallauth.entity.SysMenuRole;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.List;
/**
 * @描述：菜单角色表 服务类
 * @作者: ye
 * @日期: 2023-12-20
 */
public interface SysMenuRoleService extends IService<SysMenuRole> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<SysMenuRole> queryWrapper);

        void create(SysMenuRole sysMenuRole);
        void update(SysMenuRole sysMenuRole);
        SysMenuRole getById(String id);

        void delete(String id);

        void deleteBatch( List<String> ids);

    void createByMenuId(List<String> menusIds, String roleId);

        void saveBatchByRoleId(String roleId, List<String> menusIds);

    List<String> getMenuAndSysRoleDateListById(String roleId);

    List<SysMenuRole> roleByMenuId(String menuId);
}
