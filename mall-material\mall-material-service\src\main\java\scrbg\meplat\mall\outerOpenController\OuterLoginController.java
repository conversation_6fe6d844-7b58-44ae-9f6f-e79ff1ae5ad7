package scrbg.meplat.mall.outerOpenController;

import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.dto.outer.login.LoginUser;
import scrbg.meplat.mall.dto.outer.login.ResponseBody;
import scrbg.meplat.mall.dto.outer.login.UserInfo;
import scrbg.meplat.mall.service.UserService;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/**
 * @program: maill_api
 * @description: 提供电子招标投标系统登录api
 * @author: 代文翰
 * @create: 2023-11-20 10:44
 **/
@RestController
@Validated
@RequestMapping("/")
@Api(tags = "电子招标系统登录接口")
public class OuterLoginController {
    // 登录接口
    @Resource
    private UserService userService;
    @PostMapping("/w/login")
    @ApiOperation(value = "登录接口")
    public R<scrbg.meplat.mall.dto.outer.login.ResponseBody> login(@RequestBody @Validated LoginUser loginUser, HttpServletRequest request) {
        ResponseBody responseBody = userService.thirdLogin(loginUser, request);
        return R.success(responseBody);
    }

    @GetMapping("/verifyToken")
    @ApiOperation(value = "获取用户信息")
    public R<UserInfo> verify() {
        UserInfo userInfo = userService.verifyToken();
        return R.success(userInfo);
    }


}
