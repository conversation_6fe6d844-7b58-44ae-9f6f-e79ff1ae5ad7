package scrbg.meplat.mall.controller;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import scrbg.meplat.mall.entity.PlatformFeeFile;
import scrbg.meplat.mall.service.PlatformFeeFileService;

/**
 * @描述：通用附件控制类
 * @作者: ye
 * @日期: 2024-01-24
 */
@RestController
@RequestMapping("/platformFeeFile")
@Api(tags = "通用附件")
public class PlatformFeeFileController{

@Autowired
public PlatformFeeFileService platformFeeFileService;

@PostMapping("/listByEntity")
@ApiOperation(value = "根据实体属性分页查询")
@DynamicParameters(name = "根据实体属性分页查询", properties = {
        @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
        @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
})
public PageR<PlatformFeeFile> listByEntity(@RequestBody JSONObject jsonObject){
        PageUtils page= platformFeeFileService.queryPage(jsonObject,new LambdaQueryWrapper<PlatformFeeFile>());
        return PageR.success(page);
        }

@GetMapping("/findById")
@ApiOperation(value = "根据主键查询")
@ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "ID", required = true,
                dataType = "String", paramType = "query")
})
public R<PlatformFeeFile> findById(String id){
    PlatformFeeFile platformFeeFile = platformFeeFileService.getById(id);
        return R.success(platformFeeFile);
        }

@PostMapping("/create")
@ApiOperation(value = "新增")
public R save(@RequestBody PlatformFeeFile platformFeeFile){
    platformFeeFileService.create(platformFeeFile);
        return R.success();
        }

@PostMapping("/update")
@ApiOperation(value = "修改")
public R update(@RequestBody PlatformFeeFile platformFeeFile){
    platformFeeFileService.update(platformFeeFile);
        return R.success();
        }

@GetMapping("/delete")
@ApiOperation(value = "根据主键删除")
@ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "ID", required = true,
                dataType = "String", paramType = "query")
})
public R delete(String id){
    platformFeeFileService.delete(id);
        return R.success();
        }


@PostMapping("/deleteBatch")
@ApiOperation(value = "根据主键批量删除")
public R deleteBatch(@RequestBody List<String> ids){
    platformFeeFileService.removeByIds(ids);
        return R.success();
        }

@GetMapping("relevance/{type}/{relevanceId}")
public PlatformFeeFile getByRelevance(@PathVariable int type, @PathVariable String relevanceId){
    return platformFeeFileService
                .lambdaQuery()
                .eq(PlatformFeeFile::getRelevanceType, type)
                .eq(PlatformFeeFile::getRelevanceId, relevanceId)
                .one();
    }


}

