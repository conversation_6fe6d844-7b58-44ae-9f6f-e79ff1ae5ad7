package scrbg.meplat.mall.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class ChangStateDto {
    @ApiModelProperty(value = "id",required = true)
    @NotNull(message = "分类id不能为空！")
    private List<String> ids;

    @ApiModelProperty(value = "状态 0停用 1启用",required = true)
    @NotNull(message = "状态不能为空！")
    @Max(value = 1, message = "状态输入错误！")
    @Min(value = 0, message = "状态输入错误！")
    private Integer state;

}
