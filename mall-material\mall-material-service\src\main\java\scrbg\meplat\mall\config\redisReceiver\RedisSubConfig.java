package scrbg.meplat.mall.config.redisReceiver;//package scrbg.meplat.mall.config.redis;
//
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.data.redis.connection.RedisConnectionFactory;
//import org.springframework.data.redis.core.StringRedisTemplate;
//import org.springframework.data.redis.listener.PatternTopic;
//import org.springframework.data.redis.listener.RedisMessageListenerContainer;
//import org.springframework.data.redis.listener.adapter.MessageListenerAdapter;
//
///**
// * <AUTHOR>
// * @create 2022-12-06 10:25
// */
//@Configuration
//public class RedisSubConfig {
//
//    public static final String SUB_KEY = "channel";//频道channel
//
//    /**
//     * redis消息监听器容器
//     * 可以添加多个监听不同话题的redis监听器，只需要把消息监听器和相应的消息订阅处理器绑定，该消息监听器
//     * 通过反射技术调用消息订阅处理器的相关方法进行一些业务处理
//     * @param connectionFactory
//     * @param listenerAdapter
//     * @return
//     */
//    @Bean
//    RedisMessageListenerContainer container(RedisConnectionFactory connectionFactory
//            , MessageListenerAdapter listenerAdapter
//            ,MessageListenerAdapter listenerAdapter2 ) {
//
//        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
//        container.setConnectionFactory(connectionFactory);
//
//        //订阅了一个频道
//        // 配置不同方法监听
//        container.addMessageListener(listenerAdapter, new PatternTopic(RedisSubConfig.SUB_KEY));
//        container.addMessageListener(listenerAdapter2, new PatternTopic(RedisSubConfig.SUB_KEY));
//        return container;
//    }
//
//    /**
//     * 消息监听器适配器，绑定消息处理器，利用反射技术调用消息处理器的业务方法
//     * @param receiver
//     * @return
//     */
//    @Bean
//    MessageListenerAdapter listenerAdapter(RedisReceiver receiver) {
//        return new MessageListenerAdapter(receiver, "receiveMessage");
//    }
//    @Bean
//    MessageListenerAdapter listenerAdapter2(RedisReceiver receiver) {
//        return new MessageListenerAdapter(receiver, "receiveMessage2");
//    }
//    /**
//     * redis 读取内容的template
//     * @param connectionFactory
//     * @return
//     */
//    @Bean
//    StringRedisTemplate template(RedisConnectionFactory connectionFactory) {
//        return new StringRedisTemplate(connectionFactory);
//    }
//}
//
