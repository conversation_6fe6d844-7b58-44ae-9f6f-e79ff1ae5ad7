package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;
import java.util.Date;
/**
 * @描述：保险产品
 * @作者: sund
 * @日期: 2022-11-16
 */
@ApiModel(value="保险产品")
@Data
@TableName("insurance_products")
public class InsuranceProducts extends MustBaseEntity implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "保险id")

        private String insuranceId;

    @ApiModelProperty(value = "描述")

        private String describes;

    @ApiModelProperty(value = "0:交强险 1:车损险 2:三者险")

        private Integer type;

    @ApiModelProperty(value = "详情")

        private String details;

    @ApiModelProperty(value = "0:阳光产险 1:长安保险 2:天安财险 3:亚太财险 4:国任保险 5:浙商保险 6:中国人寿保险 7:阳光信保")

        private Integer company;

    @ApiModelProperty(value = "店铺id")

        private String shopId;

    @ApiModelProperty(value = "项目地区")

        private String projectArea;

    @ApiModelProperty(value = "排序")

        private Integer sort;

    @ApiModelProperty(value = "状态:1发布 0未发布")

        private Integer state;

    @ApiModelProperty(value = "保险名称")

        private String name;

    @ApiModelProperty(value = "0:已投 1:未投")

        private Integer insure;

    @ApiModelProperty(value = "保险年限")

        private String years;

    @ApiModelProperty(value = "承保年龄")

        private String age;

    @ApiModelProperty(value = "承保职业")

        private String occupation;

    @ApiModelProperty(value = "投保须知")

        private String notice;

    @ApiModelProperty(value = "常见问题")

        private String commonProblem;

    @ApiModelProperty(value = "创建时间")

        private Date gmtCreate;

    @ApiModelProperty(value = "更新时间")

        private Date gmtModified;

    @ApiModelProperty(value = "发布时间")

        private Date gmtRelease;

    @ApiModelProperty(value = "逻辑删除 -1: 删除 0:未删除")

        private Integer isDelete;

    @ApiModelProperty(value = "备注")

        private String remarks;


}
