package scrbg.meplat.mall.dto.user.userCenter;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.File;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2022-12-30 15:05
 */
@Data
public class CreateShopExternalDTO {
    @ApiModelProperty(value = "店铺id")
    private String shopId;

    @ApiModelProperty(value = "店铺名称")
    private String shopName;

    @ApiModelProperty(value = "省")
    private String province;

    @ApiModelProperty(value = "市")
    private String city;

    @ApiModelProperty(value = "县、区")
    private String county;

    @ApiModelProperty(value = "详细地址")
    private String detailedAddress;

    @ApiModelProperty(value = "主营业务")
    private String mainBusiness;

    @ApiModelProperty(value = "是否支持内部结算：1：是  0：否")
    private Integer isInternalSettlement;




    @ApiModelProperty(value = "身份证人像面照")
    private String identityCardFace;

    @ApiModelProperty(value = "身份证人像面照id")
    private String identityCardFaceId;

    @ApiModelProperty(value = "身份证国徽面照")
    private String identityCardBadge;

    @ApiModelProperty(value = "身份证国徽面照id")
    private String identityCardBadgeId;

    @ApiModelProperty(value = "姓名")
    private String realName;

    @ApiModelProperty(value = "身份证号")
    private String identityCard;

    @ApiModelProperty(value = "店铺类型 0：个体户  1：企业  2：个人")
    private Integer shopType;

    @ApiModelProperty(value = "其他服务权限")
    private Map<String, Integer> isOtherAuth;

    @ApiModelProperty(value = "附件列表")
    private List<File> files;


}
