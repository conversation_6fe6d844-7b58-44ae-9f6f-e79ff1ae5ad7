package scrbg.meplat.mall.controller;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import scrbg.meplat.mall.config.redis.redisson.NotResubmit;
import scrbg.meplat.mall.dto.AuditDTO;
import scrbg.meplat.mall.dto.fee.DealRecordDto;
import scrbg.meplat.mall.entity.AuditRecord;
import scrbg.meplat.mall.entity.MaterialReconciliation;
import scrbg.meplat.mall.entity.PlatformDealFeeDtl;
import scrbg.meplat.mall.entity.PlatformDealFeeRecord;
import scrbg.meplat.mall.entity.PlatformFeeFile;
import scrbg.meplat.mall.entity.SystemParam;
import scrbg.meplat.mall.enums.PublicEnum;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.service.AuditRecordService;
import scrbg.meplat.mall.service.MaterialReconciliationService;
import scrbg.meplat.mall.service.PlatformDealFeeDtlService;
import scrbg.meplat.mall.service.PlatformDealFeeRecordService;
import scrbg.meplat.mall.service.PlatformFeeFileService;
import scrbg.meplat.mall.service.SystemParamService;


/**
 * @描述：平台交易费缴费记录控制类
 * @作者: ye
 * @日期: 2024-01-24
 */
@RestController
@RequestMapping("/")
@Api(tags = "平台交易费缴费记录")
public class PlatformDealFeeRecordController {

    @Autowired
    private PlatformDealFeeRecordService platformDealFeeRecordService;

    @Autowired
    private PlatformFeeFileService platformFeeFileService;
    @Autowired
    private SystemParamService systemParamService;
    @Autowired
    private AuditRecordService auditRecordService;
    @Autowired
    private MaterialReconciliationService materialReconciliationService;
    @Autowired
    private PlatformDealFeeDtlService platformDealFeeDtlService;


    @PostMapping("platform/platformDealFeeRecord/listByEntity")
    @ApiOperation(value = "平台查询交易费记录")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<PlatformDealFeeRecord> platformListByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = platformDealFeeRecordService.platformListByEntity(jsonObject, new LambdaQueryWrapper<PlatformDealFeeRecord>());
        return PageR.success(page);
    }

    @PostMapping("supplier/platformDealFeeRecord/listByEntity")
    @ApiOperation(value = "供应商查询交易费记录")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public IPage<PlatformDealFeeRecord> supplierListByEntity(@RequestBody DealRecordDto dealRecordDto) {
        return platformDealFeeRecordService.supplierListByEntity(dealRecordDto);
    }

    @GetMapping("supplier/platformDealFeeRecord/system_param")
    @ApiOperation(value = "查询银行开户行等系统配置")
    public R<List<SystemParam>> systemParams() {
        List<SystemParam> systemParams = systemParamService.lambdaQuery().in(SystemParam::getCode, Arrays.asList(
            PublicEnum.PLATFORMFREEYHADDRESS.getRemark(),
            PublicEnum.PLATFORMFREEYHACCOUNT.getRemark(),
            PublicEnum.PLATFORMFREEYHORGNAME.getRemark(),
            PublicEnum.PLATFORM_SHOP_FEE_DEAL_RATIO.getRemark(),
            PublicEnum.PlatformShopYearFeeAmount.getRemark()
            )).list();
        return R.success(systemParams);
    }

    @GetMapping("supplier/platformDealFeeRecord/{id}/audits")
    public R<List<AuditRecord>> audits(@PathVariable String id) {
        List<AuditRecord> auditRecords = auditRecordService.lambdaQuery()
//            .eq(AuditRecord::getRelevanceType, 11)
            .eq(AuditRecord::getRelevanceId, id)
//            .eq(AuditRecord::getAuditType, 2)
            .orderByDesc(AuditRecord::getGmtCreate)
            .list();
        return R.success(auditRecords);
    }

    @PostMapping("supplier/platformDealFeeRecord/{id}/state/{state}")
    public R<Void> changeState(@PathVariable String id, @PathVariable Integer state, @RequestBody PlatformDealFeeRecord platformDealFeeRecord) {
        platformDealFeeRecord.setDealFeeRecordId(id);
        platformDealFeeRecord.setState(state);
        platformDealFeeRecordService.changeState(platformDealFeeRecord);
        return R.success();
    }
    @GetMapping("supplier/platformDealFeeRecord/{id}/file/{type}")
    public R<List<PlatformFeeFile>> getPlatformFeeFile(@PathVariable String id, @PathVariable int type) {
        List<PlatformFeeFile> platformFeeFiles = platformFeeFileService.lambdaQuery()
            .eq(PlatformFeeFile::getRelevanceType, type)
            .eq(PlatformFeeFile::getRelevanceId, id)
            .orderByDesc(PlatformFeeFile::getGmtCreate)
            .list();
        return R.success(platformFeeFiles);
    }
    /**
     * 获取交易服务费对应的所有对账单
     * @param id
     * @return
     */
    @GetMapping("supplier/platformDealFeeRecord/{id}/recons")
    public R<List<MaterialReconciliation>> getRecons(@PathVariable String id) {
        PlatformDealFeeRecord platformDealFeeRecord = platformDealFeeRecordService.getById(id);
        if (platformDealFeeRecord == null) {
            throw new BusinessException("不存在的缴费记录");
        }
        LocalDate quarterStartDate = platformDealFeeRecord.getPeriodStartDate();
        LocalDate quarterEndDate = platformDealFeeRecord.getPeriodEndDate();
        // TODO 这里没有添加RelevanceType为1的限制 如果数据量很大的话这里考虑改成后端分页
        List<PlatformDealFeeDtl> platformDealFeeDtls = platformDealFeeDtlService.getNotPayQuarterlyDealDtlList(
                        platformDealFeeRecord.getEnterpriseId(), 1, quarterStartDate, quarterEndDate);
        if (platformDealFeeDtls.isEmpty()) {
            return R.success(Collections.emptyList());
        }
        List<String> mrIds = platformDealFeeDtls.stream().map(PlatformDealFeeDtl::getRelevanceId).collect(Collectors.toList());
        List<MaterialReconciliation> mrs = materialReconciliationService.lambdaQuery()
            .in(MaterialReconciliation::getReconciliationId, mrIds)
            .orderByDesc(MaterialReconciliation::getEndTime)
            .list();
        List<AuditRecord> auditRecords = auditRecordService.lambdaQuery().eq(AuditRecord::getRelevanceType, 6).in(AuditRecord::getRelevanceId, mrIds).list();
        Map<String,List<AuditRecord>> groupedAuditRecords = auditRecords.stream().collect(Collectors.groupingBy(AuditRecord::getRelevanceId));
        for (MaterialReconciliation mr : mrs) {
            List<AuditRecord> ars = groupedAuditRecords.get(mr.getReconciliationId());
            if (ars==null) {
                ars = Collections.emptyList();
            }
            Collections.sort(ars, (a1,a2)->a2.getGmtCreate().compareTo(a1.getGmtCreate()));
            mr.setAuditRecords(ars);
        }
        return R.success(mrs);
    }
    

    @PostMapping("supplier/platformDealFeeRecord/update")
    @ApiOperation(value = "供应商修改交易费缴费")
    public R supplierUpdateDealFee(@RequestBody PlatformDealFeeRecord platformDealFeeRecord) {
        platformDealFeeRecordService.supplierUpdateDealFee(platformDealFeeRecord);
        return R.success();
    }

    @GetMapping("platformDealFeeRecord/findBySn")
    @ApiOperation(value = "根据编号获取数据")
    @ApiImplicitParams({@ApiImplicitParam(name = "sn", value = "SN", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<PlatformDealFeeRecord> findBySn(String sn) {
        PlatformDealFeeRecord platformDealFeeRecord = platformDealFeeRecordService.findBySn(sn);
        return R.success(platformDealFeeRecord);
    }

    @GetMapping("supplier/platformDealFeeRecord/deleteDealFeeRecord")
    @ApiOperation(value = "根据id删除交易服务缴费记录")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R deleteDealFeeRecord(String id) {
        platformDealFeeRecordService.deleteDealFeeRecord(id);
        return R.success();
    }

    @PostMapping("/platform/platformDealFeeRecord/audit")
    @ApiOperation(value = "审核缴费交易")
    @NotResubmit
//    @IsRole(roleName = RoleEnum.ROLE_8)
    public R audit(@RequestBody AuditDTO dto) {
        platformDealFeeRecordService.audit(dto);
        return R.success();
    }

    @GetMapping("platformDealFeeRecord/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<PlatformDealFeeRecord> findById(String id) {
        PlatformDealFeeRecord platformDealFeeRecord = platformDealFeeRecordService.getById(id);
        return R.success(platformDealFeeRecord);
    }

    @PostMapping("platformDealFeeRecord/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")
    public R deleteBatch(@RequestBody List<String> ids) {
        platformDealFeeRecordService.removeByIds(ids);
        return R.success();
    }
}

