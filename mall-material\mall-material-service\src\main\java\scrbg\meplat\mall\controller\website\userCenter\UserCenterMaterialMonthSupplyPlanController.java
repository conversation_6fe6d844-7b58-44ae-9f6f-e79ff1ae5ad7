package scrbg.meplat.mall.controller.website.userCenter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.config.auth.IsRole;
import scrbg.meplat.mall.config.auth.RoleEnum;
import scrbg.meplat.mall.config.redis.redisson.NotResubmit;
import scrbg.meplat.mall.controller.MaterialReconciliationController;
import scrbg.meplat.mall.dto.plan.*;
import scrbg.meplat.mall.entity.InterfaceLogs;
import scrbg.meplat.mall.entity.MaterialMasterPlan;
import scrbg.meplat.mall.entity.MaterialMonthSupplyPlan;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.service.InterfaceLogsService;
import scrbg.meplat.mall.service.MaterialMonthSupplyPlanService;
import scrbg.meplat.mall.util.LogUtil;
import scrbg.meplat.mall.util.RestTemplateUtils;
import scrbg.meplat.mall.vo.user.userCenter.GetPlanChangeDtlInfoByPlanNoVO;
import scrbg.meplat.mall.vo.user.userCenter.GetPlanDtlInfoByPlanNoVO;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @描述：计划表控制类
 * @作者: ye
 * @日期: 2023-06-27
 */
@RestController
@RequestMapping("/userCenter/materialMonthSupplyPlan")
@Api(tags = "计划表")
public class UserCenterMaterialMonthSupplyPlanController {

    @Autowired
    public MaterialMonthSupplyPlanService materialMonthSupplyPlanService;

    @Autowired
    private InterfaceLogsService interfaceLogsService;


    @Autowired
    MallConfig mallConfig;

    @Autowired
    RestTemplateUtils restTemplateUtils;


    // 回滚反写大宗合同数量接口
    private static String YGURL1 = "/thirdapi/material/rollBackInventory";

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
            @DynamicParameter(name = "planNo", value = "计划编号", dataTypeClass = String.class),
            @DynamicParameter(name = "contractNo", value = "合同编号", dataTypeClass = String.class),
            @DynamicParameter(name = "supplierName", value = "供应商名称", dataTypeClass = String.class),
            @DynamicParameter(name = "states", value = "状态", dataTypeClass = List.class),
            @DynamicParameter(name = "startPlanDate", value = "开始计划时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endPlanDate", value = "结束计划时间", dataTypeClass = String.class),
    })
    public PageR<MaterialMonthSupplyPlan> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = materialMonthSupplyPlanService.queryPage(jsonObject, new LambdaQueryWrapper<MaterialMonthSupplyPlan>());
        return PageR.success(page);
    }


    @PostMapping("/checkTotalNum")
    @ApiOperation(value = "检查总数返回已消耗数量")
    public R checkTotalNum(@RequestBody List<Map> dtos) {
        List<Map> maps = materialMonthSupplyPlanService.checkTotalNum(dtos);
        return R.success(maps);
    }

    @PostMapping("/batchSubmitPlan")
    @ApiOperation(value = "批量提交审核")
    @NotResubmit
    public R batchSubmitPlan(@RequestBody List<String> planIds) {
        materialMonthSupplyPlanService.batchSubmitPlan(planIds);
        return R.success();
    }

    @PostMapping("/batchDeletePlan")
    @ApiOperation(value = "批量删除")
    public R batchDeletePlan(@RequestBody List<String> planIds) {
        materialMonthSupplyPlanService.batchDeletePlan(planIds);
        return R.success();
    }

    @GetMapping("/closeMaterialMonthSupplyPlanByPlanNo")
    @ApiOperation(value = "根据计划编号完结计划")
    public R endMonthPlan( String planNo) {
        String idStr = IdWorker.getIdStr();
        StringBuilder farArg = new StringBuilder();

        try {
            materialMonthSupplyPlanService.closeMaterialMonthSupplyPlanByPlanNo(planNo,idStr,farArg);
        } catch (Exception e) {
            e.printStackTrace();
            if (mallConfig.isContractConsumeNum == 1) {
                LogUtil.writeErrorLog2(idStr, "完结计划", planNo, farArg.toString(), null, e.getMessage(), MaterialReconciliationController.class);
                InterfaceLogs iLog = new InterfaceLogs();
                iLog.setSecretKey(idStr);
                iLog.setClassPackage(UserCenterMaterialMonthSupplyPlanController.class.getName());
                iLog.setMethodName("完结计划");
                iLog.setLocalArguments(JSON.toJSONString(planNo));
                iLog.setFarArguments(farArg.toString());
                iLog.setIsSuccess(0);
                iLog.setLogType(1);
                iLog.setErrorInfo(e.getMessage());
                interfaceLogsService.create(iLog);
                HashMap<String, Object> objectHashMap = new HashMap<>();
                objectHashMap.put("consumptionMethod", "SHOP");
                objectHashMap.put("credential", idStr);
                objectHashMap.put("version", 1);
                restTemplateUtils.postPCWP2PageR(mallConfig.prodPcwp2Url02 + YGURL1, objectHashMap);
            }
            throw new BusinessException(e.getMessage());

    }
        return R.success();
    }



    @PostMapping("/cancellationPlan")
    @ApiOperation(value = "批量作废")
    @NotResubmit
    public R cancellationPlan(@RequestBody List<String> planIds) {
        materialMonthSupplyPlanService.cancellationPlan(planIds);
        return R.success();
    }

    @GetMapping("/getPlanDtlInfoByPlanNo")
    @ApiOperation(value = "根据计划编号获取计划明细")
    public R<GetPlanDtlInfoByPlanNoVO> getPlanDtlInfoByPlanNo(String planNo) {
        GetPlanDtlInfoByPlanNoVO vo = materialMonthSupplyPlanService.getPlanDtlInfoByPlanNo(planNo);
        return R.success(vo);
    }

    @GetMapping("/getSecondLevelPlanDtlInfoByPlanNo")
    @ApiOperation(value = "二级供应商根据计划编号获取计划明细")
    public R<GetPlanDtlInfoByPlanNoVO> getSecondLevelPlanDtlInfoByPlanNo(String planNo) {
        GetPlanDtlInfoByPlanNoVO vo = materialMonthSupplyPlanService.getSecondLevelPlanDtlInfoByPlanNo(planNo);
        return R.success(vo);
    }



    @GetMapping("/getPlanChangeDtlInfoByPlanNo")
    @ApiOperation(value = "根据变更计划编号获取变更数据")
    public R<GetPlanChangeDtlInfoByPlanNoVO> getPlanChangeDtlInfoByPlanNo(String planChangeNo) {
        GetPlanChangeDtlInfoByPlanNoVO vo = materialMonthSupplyPlanService.getPlanChangeDtlInfoByPlanNo(planChangeNo);
        return R.success(vo);
    }

    @PostMapping("/updatePlanChangeDtlByPlanId")
    @ApiOperation(value = "修改变更月供计划")
    @NotResubmit
    public R updatePlanChangeDtlByPlanId(@RequestBody @Valid UpdatePlanChangeDtlByPlanIdDTO dto) {
        materialMonthSupplyPlanService.updatePlanChangeDtlByPlanId(dto);
        return R.success();
    }

    @PostMapping("/cancellationChangePlan")
    @ApiOperation(value = "批量作废变更计划")
    @NotResubmit
    public R cancellationChangePlan(@RequestBody List<String> planChangeIds) {
        materialMonthSupplyPlanService.cancellationChangePlan(planChangeIds);
        return R.success();
    }

    @PostMapping("/updatePlanDtlByPlanId")
    @ApiOperation(value = "修改月供计划")
    @NotResubmit
    public R updatePlanDtlByPlanId(@RequestBody @Valid UpdatePlanDtlByPlanIdDTO dto) {
        materialMonthSupplyPlanService.updatePlanDtlByPlanId(dto);
        return R.success();
    }

    @GetMapping("/deletePlanInfo")
    @ApiOperation(value = "删除计划")
    @NotResubmit
    public R deletePlanInfo(String planId) {
        materialMonthSupplyPlanService.deletePlanInfo(planId);
        return R.success();
    }


    @PostMapping("/createChangePlanAndPlanDtl")
    @ApiOperation(value = "新增变更计划")
    @NotResubmit
    public R createChangePlanAndPlanDtl(@RequestBody CreateChangePlanAndPlanDtlDTO dto) {
        materialMonthSupplyPlanService.createChangePlanAndPlanDtl(dto);
        return R.success();
    }

    @PostMapping("/createPlanAndPlanDtl")
    @ApiOperation(value = "新增计划并且新增计划明细")
    @NotResubmit
    public R createPlanAndPlanDtl(@RequestBody MaterialMonthSupplyPlanDTO dto) {
        materialMonthSupplyPlanService.createPlanAndPlanDtl(dto);
        return R.success();
    }

    // -----------------------------------------------------------------

    @PostMapping("/auditPlan")
    @ApiOperation(value = "审核计划")
    @NotResubmit
    @IsRole(roleName = RoleEnum.ROLE_4)
    public R auditPlan(@RequestBody AuditPlanDTO dto) {
        String idStr = IdWorker.getIdStr();
        StringBuilder farArg = new StringBuilder();
        MaterialMonthSupplyPlan rD = new MaterialMonthSupplyPlan();
        try {
            materialMonthSupplyPlanService.auditPlan(dto,idStr,farArg,rD);
        } catch (Exception e) {
            e.printStackTrace();
            if (mallConfig.isContractConsumeNum == 1) {
            LogUtil.writeErrorLog2(idStr, "auditPlan", dto, farArg.toString(), null, e.getMessage(), MaterialReconciliationController.class);
            InterfaceLogs iLog = new InterfaceLogs();
            iLog.setSecretKey(idStr);
            iLog.setClassPackage(UserCenterMaterialMonthSupplyPlanController.class.getName());
            iLog.setMethodName("auditPlan");
            iLog.setLocalArguments(JSON.toJSONString(dto));
            iLog.setFarArguments(farArg.toString());
            iLog.setIsSuccess(0);
            iLog.setLogType(1);
            iLog.setErrorInfo(e.getMessage());
            interfaceLogsService.create(iLog);
                HashMap<String, Object> objectHashMap = new HashMap<>();
                objectHashMap.put("consumptionMethod", "SHOP");
                objectHashMap.put("credential", idStr);
                objectHashMap.put("version", rD.getPcwpVersion());
                restTemplateUtils.postPCWP2PageR(mallConfig.prodPcwp2Url02 + YGURL1, objectHashMap);
            }
            throw new BusinessException(e.getMessage());
        }
        return R.success();
    }






    @PostMapping("/auditChangePlan")
    @ApiOperation(value = "审核变更计划")
    @NotResubmit
    @IsRole(roleName = RoleEnum.ROLE_4)
    public R auditChangePlan(@RequestBody AuditChangePlanDTO dto) {
        String idStr = IdWorker.getIdStr();
        StringBuilder farArg = new StringBuilder();
        MaterialMonthSupplyPlan rD = new MaterialMonthSupplyPlan();
        try {
            materialMonthSupplyPlanService.auditChangePlan(dto,idStr,farArg,rD);
        } catch (Exception e) {
            e.printStackTrace();
            if (mallConfig.isContractConsumeNum == 1) {
            LogUtil.writeErrorLog2(idStr, "审核变更计划", dto, farArg.toString(), null, e.getMessage(), MaterialReconciliationController.class);
            InterfaceLogs iLog = new InterfaceLogs();
            iLog.setSecretKey(idStr);
            iLog.setClassPackage(UserCenterMaterialMonthSupplyPlanController.class.getName());
            iLog.setMethodName("审核变更计划");
            iLog.setLocalArguments(JSON.toJSONString(dto));
            iLog.setFarArguments(farArg.toString());
            iLog.setIsSuccess(0);
            iLog.setLogType(1);
            iLog.setErrorInfo(e.getMessage());
            interfaceLogsService.create(iLog);
                HashMap<String, Object> objectHashMap = new HashMap<>();
                objectHashMap.put("consumptionMethod", "SHOP");
                objectHashMap.put("credential", idStr);
                objectHashMap.put("version", rD.getPcwpVersion());
                restTemplateUtils.postPCWP2PageR(mallConfig.prodPcwp2Url02 + YGURL1, objectHashMap);
            }
            throw new BusinessException(e.getMessage());
        }
        return R.success();
    }




}

