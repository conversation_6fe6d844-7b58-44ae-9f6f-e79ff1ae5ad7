package scrbg.meplat.mall.mapper;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import scrbg.meplat.mall.entity.OrderReturn;
import scrbg.meplat.mall.vo.user.userCenter.OrderReturnVo;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【order_retuen】的数据库操作Mapper
* @createDate 2023-01-31 14:12:23
* @Entity scrbg.meplat.mall.entity.OrderRetuen
*/
public interface OrderReturnMapper extends BaseMapper<OrderReturn> {

    List<OrderReturn> findByCondition(IPage<OrderReturn> pages, @Param("ew") QueryWrapper<OrderReturn> wrapper);



    List<OrderReturn> shopTwoOrderReturnPage(IPage<OrderReturn> pages,@Param("ew")  QueryWrapper<OrderReturn> wrapper);

    OrderReturn getDataVoById(@Param("id")String id);
}




