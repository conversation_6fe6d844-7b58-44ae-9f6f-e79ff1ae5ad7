package scrbg.meplat.mallauth.config.restTemplateConfig;


import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @create 2022-12-21 15:49
 */
@Configuration
public class RestTemplateConfig {
    @Bean
    @Resource(name="simpleClientHttpRequestFactory") // 在多ClientHttpRequestFactory的时候指定用哪个
//    @LoadBalanced // ribbon的负载均衡 禁止开启，因为内部有第三方服务ip调用
    public RestTemplate restTemplate(ClientHttpRequestFactory factory) {
        RestTemplate restTemplate = new RestTemplate(factory);
        restTemplate.getInterceptors().add(new LoggingInterceptor());
        return restTemplate;
    }

    @Bean
    public ClientHttpRequestFactory simpleClientHttpRequestFactory() {
        SSL factory = new SSL();
        factory.setReadTimeout(60000);
        factory.setConnectTimeout(30000);//单位为ms
        return factory;
    }

}