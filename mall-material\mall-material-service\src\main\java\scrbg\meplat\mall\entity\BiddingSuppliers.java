package scrbg.meplat.mall.entity;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

/**
 * @描述：
 * @作者: ye
 * @日期: 2023-07-19
 */
@ApiModel(value = "")
@Data
@TableName("bidding_suppliers")
public class BiddingSuppliers extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "竞价供应商id")
    private String biddingSuppliersId;

    @ApiModelProperty(value = "竞价采购id")

    private String biddingId;


    @ApiModelProperty(value = "竞价采购编号")

    private String biddingSn;


    @ApiModelProperty(value = "供应商id")

    private String supplierId;


    @ApiModelProperty(value = "供应商名称")

    private String supplierName;


    @ApiModelProperty(value = "联系人")

    private String contactPerson;


    @ApiModelProperty(value = "联系电话")

    private String contactPhone;


    @ApiModelProperty(value = "状态")

    private Integer state;


    @ApiModelProperty(value = "类型（1报名2邀请）")

    private Integer type;


    @ApiModelProperty(value = "报名或邀请确认时间")

    private Date applyAffirmTime;


    @ApiModelProperty(value = "是否竞价（1是0否）")

    private Integer isBiding;

    @ApiModelProperty(value = "竞价次数")

    private Integer bidingCount;
}