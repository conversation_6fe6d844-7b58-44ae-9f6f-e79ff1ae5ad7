package scrbg.meplat.mall.entity;

import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
/**
 * @描述：
 * @作者: ye
 * @日期: 2023-11-13
 */
@ApiModel(value="")
@Data
@TableName("invoice_dtl")
public class InvoiceDtl extends MustBaseEntity implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "发票项")
    private String invoiceDtlId;

    @ApiModelProperty(value = "发票id")

    private String invoiceId;


    @ApiModelProperty(value = "发票编号")

    private String invoiceNo;


    @ApiModelProperty(value = "商品名称")

    private String materialName;



    @ApiModelProperty(value = "对账单id")

    private String reconciliationId;
    @ApiModelProperty(value = "对账单编号")

    private String reconciliationNo;


    @ApiModelProperty(value = "对账单项id")

    private String reconciliationDtlId;


    @ApiModelProperty(value = "含税金额")

    private BigDecimal acceptanceAmount;


    @ApiModelProperty(value = "不含税金额")

    private BigDecimal noRateAmount;


    @ApiModelProperty(value = "含税单价")

    private BigDecimal price;


    @ApiModelProperty(value = "不含税单价")

    private BigDecimal noPrice;


    @ApiModelProperty(value = "规格")

    private String spec;


    @ApiModelProperty(value = "材质")

    private String texture;


    @ApiModelProperty(value = "对账数量")

    private BigDecimal quantity;


    @ApiModelProperty(value = "单位")

    private String unit;
    @ApiModelProperty(value = "税额")

    private BigDecimal taxRate;




}
