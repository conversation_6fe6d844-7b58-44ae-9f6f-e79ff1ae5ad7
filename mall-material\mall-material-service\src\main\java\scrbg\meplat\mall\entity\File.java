package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;
import java.util.Date;

/**
 * @描述：通用附件
 * @作者: y
 * @日期: 2022-11-30
 */
@ApiModel(value = "通用附件")
@Data
@TableName("file")
public class File extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "附件id")
    private String fileId;

    @ApiModelProperty(value = "附件名称",required = true)

    private String name;


    @ApiModelProperty(value = "关联id",required = true)

    private String relevanceId;


    @ApiModelProperty(value = "关联类型（1商品2问答3消息4店铺,5内容6企业注册7平台消息8用户注册9开店单据 10 发货单 15竞价采购,16发票）",required = true)

    private Integer relevanceType;


    @ApiModelProperty(value = "附件地址",required = true)

    private String url;


    @ApiModelProperty(value = "是否主图，1是0否")

    private Integer isMain;


    @ApiModelProperty(value = "媒体类型 1:图片2视频3附件",required = true)

    private Integer fileType;


    @ApiModelProperty(value = "附件远程id",required = true)

    private String fileFarId;


    @ApiModelProperty(value = "图片类型（0普通图片1小图）")

    private Integer imgType;

    @ApiModelProperty(value = "栏目Key")

    private String programaKey;
    @ApiModelProperty(value = "副栏目Key")

    private String programaKeyTwo;

    @ApiModelProperty(value = "有效期开始日期")
    private Date startTime;

    @ApiModelProperty(value = "有效期结束日期")
    private Date endTime;

    @ApiModelProperty(value = "文件类别")
    private String category;


}
