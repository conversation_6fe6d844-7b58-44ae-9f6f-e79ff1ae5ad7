package scrbg.meplat.mall.dto.outer.login;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.vo.user.OrganizationVO;

import java.util.List;

/**
 * @program: maill_api
 * @description: 登录响应对象
 * @author: 代文翰
 * @create: 2023-11-20 11:40
 **/
@Data
public class ResponseBody {
    @ApiModelProperty(value = "登录token")
    public  String token;
    @ApiModelProperty(value = "是否是内部用户（0否1是）")
    private Integer isInterior;
    @ApiModelProperty(value = "组织列表")
    private List<OrganizationVO> organizationVOS;

    @ApiModelProperty(value = "当前组织名称")
    private String orgName;

    @ApiModelProperty(value = "当前组织id")
    private String orgId;
}
