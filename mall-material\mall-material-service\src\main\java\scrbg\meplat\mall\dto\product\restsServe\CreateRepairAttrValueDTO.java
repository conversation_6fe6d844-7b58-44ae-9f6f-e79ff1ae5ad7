package scrbg.meplat.mall.dto.product.restsServe;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-11-29 12:55
 */
@Data
public class CreateRepairAttrValueDTO {

    @ApiModelProperty(value = "服务区域")
    private String coverage;

    @ApiModelProperty(value = "客户电话")
    private String clientPhone;

    @ApiModelProperty(value = "服务范围")
    private String serveScope;

    @ApiModelProperty(value = "信息来源")
    private String infoSource;



}
