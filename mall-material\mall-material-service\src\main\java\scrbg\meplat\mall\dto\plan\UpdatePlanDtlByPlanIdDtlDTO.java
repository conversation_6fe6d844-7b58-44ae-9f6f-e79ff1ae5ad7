package scrbg.meplat.mall.dto.plan;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2023-06-30 14:06
 */
@Data
public class UpdatePlanDtlByPlanIdDtlDTO {

    @ApiModelProperty(value = "计划明细id")
    private String planDtlId;

    @ApiModelProperty(value = "本期计划数量")
    private BigDecimal thisPlanQty;


}
