package scrbg.meplat.mall.pcwp.auth;


import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import scrbg.meplat.mall.pcwp.PcwpClient;
import scrbg.meplat.mall.pcwp.PcwpRes;

@FeignClient(name = "pcwp-organization-service", url = "${mall.prodPcwp2Url}")
public interface PcwpOrganizationClient extends PcwpClient {

    @PostMapping(value = "/hr/user/changeUserPassword",consumes = "application/x-www-form-urlencoded")
    PcwpRes<Void> changeUserPassword(
            @RequestParam("userId") String userId,
            @RequestParam("oldPwd") String oldPwd,
            @RequestParam("newPwd") String newPwd,
            @RequestHeader("org") String orgHeader,
            @RequestHeader("sysCode") String sysCode,
            @RequestHeader("token") String token
    );

    /**
     * 重置密码
     * 没有token获取途径，暂时无用
     * @param resetPwd
     * @param userId
     * @param org
     * @param sysCode
     * @param token
     * @param xClientTokenUser
     * @return
     */
    @PostMapping("/user/resetUserPassword")
    PcwpRes<Void> resetUserPassword(
            @RequestParam("resetPwd") String resetPwd,
            @RequestParam("userId") String userId,
            @RequestHeader(value = "org", required = false) String org,
            @RequestHeader(value = "sysCode", required = false) String sysCode,
            @RequestHeader(value = "token", required = false) String token,
            @RequestHeader(value = "x-client-token-user", required = false) String xClientTokenUser
    );
}
