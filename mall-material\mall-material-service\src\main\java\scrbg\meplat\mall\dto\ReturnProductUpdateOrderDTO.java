package scrbg.meplat.mall.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2023-06-20 15:36
 */
@Data
public class ReturnProductUpdateOrderDTO {

    @ApiModelProperty(value = "订单id",required = true)
    @NotEmpty(message = "订单id不能为空!")
    private String orderId;

    @ApiModelProperty(value = "订单项id",required = true)
    @NotEmpty(message = "当订单项id不能为空!")
    private String orderItemId;

    @ApiModelProperty(value = "退货数量",required = true)
    @NotNull(message = "退货数量不能为空!")
    private BigDecimal number;
}
