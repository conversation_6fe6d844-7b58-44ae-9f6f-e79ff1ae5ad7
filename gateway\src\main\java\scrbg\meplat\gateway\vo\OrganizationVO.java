package scrbg.meplat.gateway.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-12-21 14:39
 */
@Data
public class OrganizationVO {

    @ApiModelProperty(value = "组织名称")
    private String orgName;

    @ApiModelProperty(value = "组织id")
    private String orgId;

    @ApiModelProperty(value = "机构简码")
    private String shortCode;

    @ApiModelProperty(value = "机构类型(1:集团|2:分子公司|4:经理部|5:项目部|6:股份|7:事业部)")
    private Integer orgType;
}
