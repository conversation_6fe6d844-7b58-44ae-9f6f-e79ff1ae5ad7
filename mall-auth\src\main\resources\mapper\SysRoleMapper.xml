<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mallauth.mapper.SysRoleMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mallauth.entity.SysRole" id="SysRoleMap">
        <result property="roleId" column="role_id"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="sort" column="sort"/>
        <result property="state" column="state"/>
        <result property="remarks" column="remarks"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="isDelete" column="is_delete"/>
        <result property="mallType" column="mall_type"/>
        <result property="keyword" column="keyword"/>
        <result property="orgSearch" column="org_search"/>
    </resultMap>


</mapper>