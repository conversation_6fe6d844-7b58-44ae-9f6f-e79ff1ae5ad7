package scrbg.meplat.mall.entity;

import java.time.LocalDate;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import lombok.Data;
import lombok.EqualsAndHashCode;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
/**
 * 计划附件
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class PlanAttachment extends MustBaseEntity{
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    private String planId;
    private String name;
    private Integer fileSize;
    private String fileFarId;
    private LocalDate uploadDate;
}
