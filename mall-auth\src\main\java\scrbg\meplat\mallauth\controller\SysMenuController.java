package scrbg.meplat.mallauth.controller;
import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import scrbg.meplat.mallauth.entity.SysRole;
import scrbg.meplat.mallauth.service.SysMenuService;
import scrbg.meplat.mallauth.entity.SysMenu;
import scrbg.meplat.mallauth.vo.ChangStateVo;

import java.util.List;

/**
 * @描述：菜单表控制类
 * @作者: ye
 * @日期: 2023-12-20
 */
@RestController
@RequestMapping("/sysMenu")
@Api(tags = "菜单表")
public class SysMenuController{

@Autowired
public SysMenuService sysMenuService;

@PostMapping("/listByEntity")
@ApiOperation(value = "根据实体属性分页查询")
@DynamicParameters(name = "根据实体属性分页查询", properties = {
        @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
        @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
        @DynamicParameter(name = "classCode", value = "所属平台（1后台管理平台2供应商管理平台3履约管理 4 自营店供应商", dataTypeClass = Integer.class)
})
public R<List<SysMenu>> listByEntity(@RequestBody JSONObject jsonObject){
    List<SysMenu> list = sysMenuService.queryPage(jsonObject, new LambdaQueryWrapper<SysMenu>());
    return R.success(list);
        }


    @PostMapping("/selectParentMenus")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public R<List<SysMenu>> selectParentMenus(@RequestBody JSONObject jsonObject){
        List<SysMenu> list= sysMenuService.selectParentMenus(jsonObject,new LambdaQueryWrapper<SysMenu>());
        return R.success(list);
    }


    @GetMapping("/findById")
@ApiOperation(value = "根据主键查询")
@ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "ID", required = true,
                dataType = "String", paramType = "query")
})
public R<SysMenu> findById(String id){
    SysMenu sysMenu = sysMenuService.getById(id);
        return R.success(sysMenu);
        }

@PostMapping("/create")
@ApiOperation(value = "新增")
public R save(@RequestBody SysMenu sysMenu){
    sysMenuService.create(sysMenu);
        return R.success();
        }



    @PostMapping("/updateState")
    @ApiOperation(value = "修改是否展示菜单")
    public R updateState(@RequestBody ChangStateVo changStateVo){
        sysMenuService.updateState(changStateVo);
        return R.success();
    }

    @PostMapping("/changeSortValue")
    @ApiOperation(value = "批量修改排序值")
    public R changeSortValue(@RequestBody List<SysMenu> ids){
        sysMenuService.changeSortValue(ids);
        return R.success();
    }


    @PostMapping("/updateShowDev")
    @ApiOperation(value = "修改是展示测试")
    public R updateShowDev(@RequestBody ChangStateVo changStateVo){
        sysMenuService.updateShowDev(changStateVo);
        return R.success();
    }
@PostMapping("/update")
@ApiOperation(value = "修改")
public R update(@RequestBody SysMenu sysMenu){
    sysMenuService.update(sysMenu);
        return R.success();
        }

@GetMapping("/delete")
@ApiOperation(value = "根据主键删除")
@ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "ID", required = true,
                dataType = "String", paramType = "query")
})
public R delete(String id){
    sysMenuService.delete(id);
        return R.success();
        }


@PostMapping("/deleteBatch")
@ApiOperation(value = "根据主键批量删除")
public R deleteBatch(@RequestBody List<String> ids){
    sysMenuService.removeByIds(ids);
        return R.success();
        }
}

