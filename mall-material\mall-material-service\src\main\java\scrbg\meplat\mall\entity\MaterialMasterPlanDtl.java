package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @描述：物资总计划明细
 * @作者: y
 * @日期: 2022-11-13
 */
@ApiModel(value = "物资总计划明细")
@Data
@TableName("material_master_plan_dtl")
public class MaterialMasterPlanDtl extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "物资总计划明细id")

    private String dtlId;

    @ApiModelProperty(value = "物资总计划id")

    private String billId;

    @ApiModelProperty(value = "工程计划明细id")

    private String projectPlanDtlId;

    @ApiModelProperty(value = "物资id")

    private String materialId;

    @ApiModelProperty(value = "物资名称")

    private String materialName;

    @ApiModelProperty(value = "物资类别id(1级类别id/2级类别id/..)")

    private String materialClassId;

    @ApiModelProperty(value = "物资类别名称(1级类别名称/2级类别名称/..)")

    private String materialClassName;

    @ApiModelProperty(value = "规格型号")

    private String spec;

    @ApiModelProperty(value = "材质")

    private String texture;

    @ApiModelProperty(value = "计量单位")

    private String unit;

    @ApiModelProperty(value = "计划数量")

    private BigDecimal planQuantity;

    @ApiModelProperty(value = "设计数量")

    private BigDecimal designQuantity;

    @ApiModelProperty(value = "预算单价(元)")

    private BigDecimal budgetPrice;

    @ApiModelProperty(value = "预算金额(元)")

    private BigDecimal budgetAmount;

    @ApiModelProperty(value = "市场单价(元)")

    private BigDecimal marketPrice;

    @ApiModelProperty(value = "市场金额(元)")

    private BigDecimal marketAmount;

    @ApiModelProperty(value = "采购单位id")

    private String purchasingUnitId;

    @ApiModelProperty(value = "采购单位名称")

    private String purchasingUnitName;

    @ApiModelProperty(value = "采购方式")

    private Integer purchaseType;

    @ApiModelProperty(value = "已收料数量")

    private BigDecimal receivedQuantity;

    @ApiModelProperty(value = "已汇总数量")

    private BigDecimal summarizedQuantity;

    @ApiModelProperty(value = "已使用数量(招标)")

    private BigDecimal usedQuantityTender;

    @ApiModelProperty(value = "已使用数量(合同)")

    private BigDecimal usedQuantityContract;

    @ApiModelProperty(value = "是否已汇总(0:否;1:是)")

    private Integer isSummarized;

    @ApiModelProperty(value = "订单项id")

    private String orderItemId;


    @ApiModelProperty(value = "状态")

    private Integer state;

    @ApiModelProperty(value = "商品类型：0物资 1设备 2周材（设备租赁） 3周材（设备） 4二手设备 5租赁设备")
    private Integer productType;
}
