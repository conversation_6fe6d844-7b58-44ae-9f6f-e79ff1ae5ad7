package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;

/**
 * @描述：招标类型表
 * @作者: y
 * @日期: 2022-11-13
 */
@ApiModel(value = "招标类型表")
@Data
@TableName("bidding_type")
public class BiddingType extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "类型id")

    private String biddingTypeId;

    @ApiModelProperty(value = "类型名称")

    private String biddingTypeName;

    @ApiModelProperty(value = "状态：1启用 0停用")

    private Integer state;


}
