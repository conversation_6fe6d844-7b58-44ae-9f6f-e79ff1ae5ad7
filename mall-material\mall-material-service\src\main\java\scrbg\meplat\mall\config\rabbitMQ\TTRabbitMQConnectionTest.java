package scrbg.meplat.mall.config.rabbitMQ;

import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * TT待办系统RabbitMQ连接测试类
 * 用于验证RabbitMQ连接配置是否正确
 */
@Slf4j
@Component
public class TTRabbitMQConnectionTest {

    @Autowired
    @Qualifier("ttRabbitTemplate")
    private RabbitTemplate ttRabbitTemplate;

    @Resource
    private TTToDoRabbitMQUtil ttToDoRabbitMQUtil;

    /**
     * 测试RabbitMQ连接
     */
    public void testConnection() {
        log.info("开始测试TT待办系统RabbitMQ连接...");
        
        try {
            // 测试连接是否正常
            if (ttRabbitTemplate != null) {
                log.info("✓ ttRabbitTemplate注入成功");
            } else {
                log.error("✗ ttRabbitTemplate注入失败");
                return;
            }

            // 测试TTToDoRabbitMQUtil是否初始化
            if (TTToDoRabbitMQUtil.isInitialized()) {
                log.info("✓ TTToDoRabbitMQUtil初始化成功");
            } else {
                log.error("✗ TTToDoRabbitMQUtil初始化失败");
                return;
            }

            // 创建测试待办
            ToDoMessageBody testTodo = createTestTodo();
            
            // 测试发送消息
            log.info("开始测试发送待办消息...");
            TTToDoRabbitMQUtil.sendSingleToDo(testTodo);
            log.info("✓ 待办消息发送成功");

            // 测试完成待办
            log.info("开始测试完成待办消息...");
            TTToDoRabbitMQUtil.completeSingleToDo(
                testTodo.getToDoId(),
                testTodo.getEmployeeNumber(),
                testTodo.getUserId()
            );
            log.info("✓ 完成待办消息发送成功");

            log.info("🎉 TT待办系统RabbitMQ连接测试全部通过！");

        } catch (Exception e) {
            log.error("✗ TT待办系统RabbitMQ连接测试失败: {}", e.getMessage(), e);
            
            // 详细错误分析
            analyzeError(e);
        }
    }

    /**
     * 创建测试待办
     */
    private ToDoMessageBody createTestTodo() {
        return TTToDoRabbitMQUtil.createTodoBody(
            "TEST_CONNECTION_" + System.currentTimeMillis(),
            "036529",  // 指定账号
            "391E2FB8-F295-4045-8CDC-340AD3DE6700",  // 指定用户ID
            "RabbitMQ连接测试",
            "这是一个RabbitMQ连接测试消息，请忽略",
            "http://test.example.com/connection-test"
        );
    }

    /**
     * 分析错误原因
     */
    private void analyzeError(Exception e) {
        String errorMessage = e.getMessage();
        
        if (errorMessage.contains("ACCESS_REFUSED")) {
            log.error("🔐 认证失败 - 请检查RabbitMQ用户名和密码是否正确");
            log.error("   当前配置应该是: username=wzsc, password=FABF26F6-7615-EB92-E158-09F3125DC089");
        } else if (errorMessage.contains("Connection refused")) {
            log.error("🌐 连接被拒绝 - 请检查RabbitMQ服务器地址和端口是否正确");
            log.error("   当前配置应该是: host=**************, port=5672");
        } else if (errorMessage.contains("NOT_FOUND")) {
            log.error("📁 虚拟主机不存在 - 请检查虚拟主机配置是否正确");
            log.error("   当前配置应该是: virtual-host=ToDoList");
        } else if (errorMessage.contains("PRECONDITION_FAILED")) {
            log.error("📮 Exchange不存在 - 请检查Exchange配置是否正确");
            log.error("   当前配置应该是: exchange=TT_ToDoList");
        } else {
            log.error("❓ 未知错误 - 请检查网络连接和RabbitMQ服务状态");
        }
    }

    /**
     * 验证配置信息
     */
    public void verifyConfiguration() {
        log.info("=== TT待办系统配置信息验证 ===");
        log.info("RabbitMQ连接配置:");
        log.info("  Host: **************");
        log.info("  Port: 5672");
        log.info("  Username: wzsc");
        log.info("  Virtual Host: ToDoList");
        log.info("  Exchange: TT_ToDoList");
        log.info("  Routing Key: Increment/Full");
        
        log.info("指定账号信息:");
        log.info("  Employee Number: 036529");
        log.info("  User ID: 391E2FB8-F295-4045-8CDC-340AD3DE6700");
        log.info("  登录账号: 036529");
        log.info("  登录密码: znjztest@036529");
        log.info("  TT下载地址: app.scrbg.com");
        log.info("================================");
    }

    /**
     * 批量测试
     */
    public void batchTest() {
        log.info("开始批量测试...");
        
        try {
            // 创建多个测试待办
            ToDoMessageBody todo1 = createTestTodo();
            todo1.setTitle("批量测试待办1");
            
            ToDoMessageBody todo2 = createTestTodo();
            todo2.setTitle("批量测试待办2");
            todo2.setToDoId("BATCH_TEST_2_" + System.currentTimeMillis());
            
            // 批量发送待办
            TTToDoRabbitMQUtil.sendIncrementToDo(Arrays.asList(todo1, todo2));
            log.info("✓ 批量待办发送成功");
            
            // 批量完成待办
            TTToDoRabbitMQUtil.sendIncrementDone(Arrays.asList(todo1, todo2));
            log.info("✓ 批量完成待办发送成功");
            
            log.info("🎉 批量测试全部通过！");
            
        } catch (Exception e) {
            log.error("✗ 批量测试失败: {}", e.getMessage(), e);
        }
    }
}
