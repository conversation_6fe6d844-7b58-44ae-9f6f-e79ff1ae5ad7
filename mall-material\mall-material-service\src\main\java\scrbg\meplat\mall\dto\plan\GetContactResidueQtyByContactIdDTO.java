package scrbg.meplat.mall.dto.plan;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2023-12-28 14:04
 */
@Data
public class GetContactResidueQtyByContactIdDTO {
    // 接口地址：http://182.151.206.110:15103/doc.html#/%E7%AC%AC%E4%B8%89%E6%96%B9%E6%8E%A5%E5%8F%A3/%E7%89%A9%E8%B5%84%E5%90%88%E5%90%8C%E6%8E%A5%E5%8F%A3/getMaterialContractInfoListUsingPOST
    // 目前只使用了一些属性，后续自己加

    @ApiModelProperty(value = "合同ID")
    private String BillId;


    @ApiModelProperty(value = "合同明细id")
    private String DtlId;


    @ApiModelProperty(value = "合同剩余数量")
    private BigDecimal ResidueQty;


}
