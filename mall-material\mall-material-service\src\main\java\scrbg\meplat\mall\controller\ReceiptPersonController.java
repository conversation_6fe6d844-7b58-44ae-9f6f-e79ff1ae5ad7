package scrbg.meplat.mall.controller;

import com.alibaba.druid.sql.visitor.functions.Char;
import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import scrbg.meplat.mall.service.ReceiptPersonService;
import scrbg.meplat.mall.entity.ReceiptPerson;

import java.util.List;

/**
 * @描述：控制类
 * @作者: ye
 * @日期: 2023-07-13
 */
@RestController
@RequestMapping("/receiptPerson")
@Api(tags = "")
public class ReceiptPersonController {

    @Autowired
    public ReceiptPersonService receiptPersonService;

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "name", value = "收料员姓名", dataTypeClass = String.class),
            @DynamicParameter(name = "phone", value = "收料员电话", dataTypeClass = String.class),
            @DynamicParameter(name = "state", value = "启用状态", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = Integer.class),

    })
    public PageR<ReceiptPerson> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = receiptPersonService.queryPage(jsonObject, new LambdaQueryWrapper<ReceiptPerson>());
        return PageR.success(page);
    }

    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<ReceiptPerson> findById(String id) {
        ReceiptPerson receiptPerson = receiptPersonService.getById(id);
        return R.success(receiptPerson);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody ReceiptPerson receiptPerson) {
        receiptPersonService.create(receiptPerson);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody ReceiptPerson receiptPerson) {
        receiptPersonService.update(receiptPerson);
        return R.success();
    }

    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        receiptPersonService.delete(id);
        return R.success();
    }


    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")
    public R deleteBatch(@RequestBody List<String> ids) {
        receiptPersonService.removeByIds(ids);
        return R.success();
    }

    @PostMapping("/updateByPublish")
    @ApiOperation(value = "批量启用")
    public R updatePublish(@RequestBody List<String> ids) {
        receiptPersonService.updateByPublish(ids, "1");
        return R.success();
    }

    @PostMapping("/updateNotPublish")
    @ApiOperation(value = "批量停用")
    public R updateNotPublish(@RequestBody List<String> ids) {
        receiptPersonService.updateByPublish(ids, "0");
        return R.success();
    }
}

