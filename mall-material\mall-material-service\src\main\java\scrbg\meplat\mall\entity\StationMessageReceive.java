package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;
import java.util.Date;

/**
 * @描述：站点接收消息
 * @作者: y
 * @日期: 2022-11-25
 */
@ApiModel(value = "站点接收消息")
@Data
@TableName("station_message_receive")
public class StationMessageReceive extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "消息接收id")
    private String stationMessageReceiveId;

    @ApiModelProperty(value = "收件人id")

    private String receiveId;


    @ApiModelProperty(value = "收件人名称")

    private String receiveName;


    @ApiModelProperty(value = "收件人类型（0店铺1用户2平台）")

    private Integer receiveType;


    @ApiModelProperty(value = "收件人消息账号")

    private String receiveCode;


    @ApiModelProperty(value = "是否已读（0否1是）")

    private Integer isRead;


    @ApiModelProperty(value = "已读时间")

    private Date readDate;


    @ApiModelProperty(value = "消息id")

    private String stationMessageId;


}
