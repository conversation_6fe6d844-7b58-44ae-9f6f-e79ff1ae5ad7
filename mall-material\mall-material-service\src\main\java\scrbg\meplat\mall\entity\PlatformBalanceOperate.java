package scrbg.meplat.mall.entity;

import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
/**
 * @描述：平台交易余额操作记录
 * @作者: ye
 * @日期: 2024-01-31
 */
@ApiModel(value="平台交易余额操作记录")
@Data
@TableName("platform_balance_operate")
public class PlatformBalanceOperate extends MustBaseEntity implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "金额操作id")
    private String platformBalanceOperateId;

    @ApiModelProperty(value = "操作标题")
    private String title;

    @ApiModelProperty(value = "关联id")
    private String relevanceId;

    @ApiModelProperty(value = "关联类型（1平台交易）")
    private Integer relevanceType;

    @ApiModelProperty(value = "变动金额（新增正数减少负数）")
    private BigDecimal amount;

    @ApiModelProperty(value = "修改前金额")
    private BigDecimal beforeAmount;

    @ApiModelProperty(value = "修改后金额")
    private BigDecimal afterAmount;

    @ApiModelProperty(value = "操作关联id（导致金额变化的id）")
    private String operateId;

    @ApiModelProperty(value = "操作关联编号（如果是类型是2，这里的编号是关联明细的编号也就是对账单的编号）")
    private String operateUn;

    @ApiModelProperty(value = "操作关联类型（1缴费主表id2平台交易费用明细id3对账单id)）")
    private Integer operateType;

    @ApiModelProperty(value = "缴费明细记录JSON（如果类型是2存在值，作废退回余额自动缴费）")
    private String freeDtl;
}