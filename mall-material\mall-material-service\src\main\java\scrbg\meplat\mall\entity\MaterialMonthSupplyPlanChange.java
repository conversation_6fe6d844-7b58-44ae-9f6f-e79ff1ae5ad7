package scrbg.meplat.mall.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
/**
 * @描述：计划变更表
 * @作者: ye
 * @日期: 2023-07-04
 */
@ApiModel(value="计划变更表")
@Data
@TableName("material_month_supply_plan_change")
public class MaterialMonthSupplyPlanChange extends MustBaseEntity implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "变更计划Id")
    private String planChangeId;

    @ApiModelProperty(value = "变更计划编号")

    private String planChangeNo;


    @ApiModelProperty(value = "计划日期")
    @JsonFormat(pattern = "yyyy-MM")
    private Date planDate;


    @ApiModelProperty(value = "计划创建日期")

    private Date planCreateDate;


    @ApiModelProperty(value = "业务类型（物资采购合同0、事实合同1、零星采购计划2）")

    private Integer businessType;


    @ApiModelProperty(value = "合同id")

    private String contractId;


    @ApiModelProperty(value = "合同编号")

    private String contractNo;


    @ApiModelProperty(value = "供应商id（pcwp）")

    private String supplierId;


    @ApiModelProperty(value = "供应商名称")

    private String supplierName;


    @ApiModelProperty(value = "机构id（pcwp）")

    private String orgId;


    @ApiModelProperty(value = "机构名称")

    private String orgName;


    @ApiModelProperty(value = "计划状态（0草稿1待审核2通过3未通过4已作废）")

    private Integer state;


    @ApiModelProperty(value = "源计划id")

    private String planId;


    @ApiModelProperty(value = "源计划编号")

    private String planNo;


    @ApiModelProperty(value = "本地供应商id")

    private String localSupplierId;


    @ApiModelProperty(value = "本地机构id")

    private String localOrgId;

    //乐观锁
    @Version
    private Integer version;

}