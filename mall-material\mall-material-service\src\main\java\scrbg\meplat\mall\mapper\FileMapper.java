package scrbg.meplat.mall.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import scrbg.meplat.mall.entity.File;

import java.util.List;

/**
 * @描述：通用附件 Mapper 接口
 * @作者: y
 * @日期: 2022-11-24
 */
@Mapper
@Repository
public interface FileMapper extends BaseMapper<File> {

    @Delete("delete from file where relevance_id = #{relevanceId} and relevance_type = #{relevanceType}")
    void deleteBatchFileByRelevanceIdAndType(String relevanceId, Integer relevanceType);

    @Delete("delete from file where file_id = #{id}")
    void deleteFileById(String id);

    @Delete("delete from file where relevance_id = #{relevanceId} and relevance_type = #{relevanceType} and programa_key = #{programaKey}")
    void deleteBatchFileByRelevanceIdAndTypeAndProgramaKey(String relevanceId, Integer relevanceType, String programaKey);

    @Delete("delete from file where relevance_id = #{relevanceId}  and programa_key = #{programaKey}")
    void deleteBatchFileByRelevanceIdAndProgramaKey(String relevanceId, String programaKey);

    @Delete("<script>" +
            "delete from file where relevance_type = #{relevanceType} and relevance_id in " +
            "<foreach collection='ids' open='(' item='id_' separator=',' close=')'> #{id_}" +
            "</foreach>" +
            "</script>")
    void removeRealByProductIdIds(Integer relevanceType,List<String> ids);


    @Select("SELECT file_id " +
            "FROM file " +
            "where is_delete = -1")
    List<String> getDeleteIds();
}
