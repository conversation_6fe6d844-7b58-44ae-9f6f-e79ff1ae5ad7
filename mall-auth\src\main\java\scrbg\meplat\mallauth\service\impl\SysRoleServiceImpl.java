package scrbg.meplat.mallauth.service.impl;

import com.alibaba.druid.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import scrbg.meplat.mallauth.entity.SysMenuRole;
import scrbg.meplat.mallauth.entity.SysRole;
import scrbg.meplat.mallauth.entity.parent.MustBaseEntity;
import scrbg.meplat.mallauth.exception.BusinessException;
import scrbg.meplat.mallauth.mapper.SysRoleMapper;
import scrbg.meplat.mallauth.service.SysMenuRoleService;
import scrbg.meplat.mallauth.service.SysRoleService;
import scrbg.meplat.mallauth.config.redis.redisson.NotResubmit;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;

import java.util.*;

/**
 * @描述：角色表 服务类
 * @作者: ye
 * @日期: 2023-12-20
 */
@Service
public class SysRoleServiceImpl extends ServiceImpl<SysRoleMapper, SysRole> implements SysRoleService {


    @Autowired
    SysMenuRoleService sysMenuRoleService;

    @Override

    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<SysRole> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String keywords = (String) innerMap.get("keywords");

        String code = (String) innerMap.get("code");
        String name = (String) innerMap.get("name");
        Integer state = (Integer) innerMap.get("type");
        Integer categoryType = (Integer) innerMap.get("categoryType");
        queryWrapper.eq(SysRole::getCategoryType, categoryType);
        if (org.apache.commons.lang.StringUtils.isNotBlank(keywords)) {
            queryWrapper.and((t) -> {
                t.like(SysRole::getName, keywords)
                        .or()
                        .like(SysRole::getCode, keywords);

            });
        }

        if (!StringUtils.isEmpty(name)) {
            queryWrapper.eq(SysRole::getName, name);
        }
        if (!StringUtils.isEmpty(code)) {
            queryWrapper.eq(SysRole::getCode, code);
        }
        queryWrapper.orderByDesc(MustBaseEntity::getSort,MustBaseEntity::getGmtCreate);
        queryWrapper.eq(state != null, SysRole::getState, name);
        IPage<SysRole> page = this.page(
                new Query<SysRole>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void create(SysRole sysRole) {

        Integer count = lambdaQuery().eq(SysRole::getName, sysRole.getName())
                .eq(SysRole::getCode, sysRole.getCode())
                .eq(SysRole::getCategoryType, sysRole.getCategoryType()).count();
        if (count > 0) {
            throw new BusinessException(500, "角色名称重复");
        } else {
            super.save(sysRole);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void update(SysRole sysRole) {
        SysRole one = lambdaQuery().eq(SysRole::getName, sysRole.getName())
                .eq(SysRole::getCode, sysRole.getCode())
                .eq(SysRole::getCategoryType, sysRole.getCategoryType()).one();
         if (one==null){
             super.updateById(sysRole);
         }else {
             if (one.getRoleId().equals(sysRole.getRoleId())) {
                 super.updateById(sysRole);
             }else {
                 throw new BusinessException(500,"角色名称重复");
             }
         }



    }


    @Override
    public SysRole getById(String id) {
        return super.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void delete(String id) {
        super.removeById(id);
        sysMenuRoleService.saveBatchByRoleId(id, null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void deleteBatch(List<String> ids) {
        super.removeByIds(ids);
    }

    @Override
    public void saveMenuAndSysRole(SysRole sysRole) {
        List<String> menusIds = sysRole.getMenusIds();
        sysMenuRoleService.saveBatchByRoleId(sysRole.getRoleId(),menusIds);

    }

    @Override
    public List<String> getMenuAndSysRoleDateListById(String roleId) {
        List<String> menuIds=sysMenuRoleService.getMenuAndSysRoleDateListById(roleId);
        return menuIds;
    }


    @Override
    public void changeSortValue(List<SysRole> ids) {
        List<SysRole> sysRoles = new ArrayList<>();
        if (ids!=null&&ids.size()>0){
            for (SysRole id : ids) {
                SysRole byId = getById(id);
                byId.setSort(id.getSort());
                sysRoles.add(byId);
            }
            updateBatchById(sysRoles);
        }
    }
}
