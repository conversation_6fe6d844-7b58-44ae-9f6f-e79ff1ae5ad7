package scrbg.meplat.mall.config.rabbitMQ;//package scrbg.meplat.mall.config.rabbitMQ;
//
//
//import org.springframework.amqp.core.Binding;
//import org.springframework.amqp.core.BindingBuilder;
//import org.springframework.amqp.core.FanoutExchange;
//import org.springframework.amqp.core.Queue;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
///**
// * <AUTHOR>
// * @create 2023-01-05 13:15
// */
//@Configuration
//public class FanoutConfig {
//    /**
//     * 声明交换机
//     * @return Fanout类型交换机
//     */
//    @Bean
//    public FanoutExchange fanoutExchange(){
//        return new FanoutExchange("SMS_Service");
//    }
//
//    /**
//     * 第1个队列
//     */
//    @Bean
//    public Queue fanoutQueue1(){
//        return new Queue("SMS_Service_Demo");
//    }
//
//    /**
//     * 绑定队列和交换机
//     */
//    @Bean
//    public Binding bindingQueue1(Queue fanoutQueue1, FanoutExchange fanoutExchange){
//        return BindingBuilder.bind(fanoutQueue1).to(fanoutExchange);
//    }
//
//    /**
//     * 第2个队列
//     */
//    @Bean
//    public Queue fanoutQueue2(){
//        return new Queue("fanout.queue2");
//    }
//
//    /**
//     * 绑定队列和交换机
//     */
//    @Bean
//    public Binding bindingQueue2(Queue fanoutQueue2, FanoutExchange fanoutExchange){
//        return BindingBuilder.bind(fanoutQueue2).to(fanoutExchange);
//    }
//}