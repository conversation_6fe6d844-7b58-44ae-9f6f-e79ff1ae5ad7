package scrbg.meplat.mall.controller;/**
 * <AUTHOR>
 * @date 2023/7/20
 */

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.R;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.config.logRecording.LogRecord;
import scrbg.meplat.mall.config.logRecording.enums.BusinessType;
import scrbg.meplat.mall.config.logRecording.enums.OperatorType;
import scrbg.meplat.mall.config.redis.redisson.NotResubmit;
import scrbg.meplat.mall.service.MyBiddingService;

import javax.servlet.http.HttpServletResponse;

/**
 * @program: maill_api
 * @description: 我参与的竞价
 * @author: 代文翰
 * @create: 2023-07-20 00:26
 **/
@RestController
@RequestMapping("/myBidding")
public class MyBiddingController {
    @Autowired
    private MyBiddingService myBiddingService;

    /***
     * 分页查询我参与的竞价列表
     * @param jsonObject
     * @return
     */
    @PostMapping("/list")
    public PageR getMyBiddingList(@RequestBody JSONObject jsonObject) {
        return PageR.success(myBiddingService.queryMyBidList(jsonObject));
    }

    /***
     * 查询报价信息(竞价采购bidding_sn)
     */
    @PostMapping("/getOfferBid")
    public R getMyBidOffer(@RequestBody JSONObject biddingSn) {
        String sn = (String) biddingSn.get("biddingSn");
        return R.success(myBiddingService.queryMyOfferPrice(sn));
    }

    /**
     * 更新报价信息
     */
    @PostMapping("/udateOfferBid")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "bidPrice", value = "不含税到场单价", required = true,
                    dataType = "String"),
            @ApiImplicitParam(name = "taxRate", value = "税率", required = true,
                    dataType = "String"),
            @ApiImplicitParam(name = "remark", value = "含税总金额", required = true,
                    dataType = "String")
    })
    @NotResubmit
    @LogRecord(title = "报价管理",businessType = BusinessType.UPDATE,operatorType = OperatorType.MANAGE)

    public R udateBidOffer(@RequestBody JSONObject jsonObject, String biddingSn) {
        myBiddingService.udateMyOfferPrice(jsonObject, biddingSn);
        return R.success();
    }

    /**
     * 临购报价
     * @param jsonObject
     * @param biddingSn
     * @return
     */
    @PostMapping("/udateLgOfferBid")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "biddingSn", value = "竞价编号", required = true,
                    dataType = "String"),
    })
    @NotResubmit
    @LogRecord(title = "报价管理",businessType = BusinessType.UPDATE,operatorType = OperatorType.MANAGE)

    public R udateLgOfferBid(@RequestBody JSONObject jsonObject, String biddingSn) {
        myBiddingService.udateMyLgOfferPrice(jsonObject, biddingSn);
        return R.success();
    }

    /**
     * 下载我的竞价函
     */
    @NotResubmit
    @GetMapping("/exportBidDataById")
    @ApiOperation(value = "根据竞价采购Id导出数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "biddingSn", value = "竞价采购编号", required = true,
                    dataType = "String", paramType = "query")
    })
    public void downLoadMyBidLetter(String biddingSn, HttpServletResponse response) {
        myBiddingService.exportBidLetterExcel(biddingSn, response);

    }

    /***
     * 保存报价记录saveBid
     */
    @PostMapping("/saveBid")
    @LogRecord(title = "报价管理",businessType = BusinessType.EXPORT,operatorType = OperatorType.MANAGE)

    @ApiOperation(value = "根据竞价采购Id导出数据")
    @NotResubmit
    @ApiImplicitParams({
            @ApiImplicitParam(name = "biddingRecordId", value = "竞价采购记录ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R saveBid(String biddingRecordId, @RequestBody JSONObject jsonObject) {
        myBiddingService.saveFileAndStatus(biddingRecordId, jsonObject);
        return R.success();
    }

    @PostMapping("/getProductInfo")
    @ApiOperation(value = "根据商品ID获取商品全明细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "biddingSn", value = "竞价采购编号", required = true,
                    dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "biddingProductId", value = "竞价采购商品编号", required = true,
                    dataType = "String", paramType = "query")
    })
    public R getProductInfo(@RequestBody JSONObject jsonObject) {
        return R.success(myBiddingService.getProductInfo(jsonObject));
    }


}
