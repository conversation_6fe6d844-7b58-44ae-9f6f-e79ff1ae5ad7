package scrbg.meplat.mall.controller.ymx.website.userCenter;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.scrbg.common.utils.R;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import scrbg.meplat.mall.component.OrderDetailChecker;
import scrbg.meplat.mall.config.auth.IsRole;
import scrbg.meplat.mall.config.auth.RoleEnum;
import scrbg.meplat.mall.config.redis.redisson.NotResubmit;
import scrbg.meplat.mall.dto.order.ProductBuyInfoDTO;
import scrbg.meplat.mall.entity.plan.Plan;
import scrbg.meplat.mall.service.order.OrderService;
import scrbg.meplat.mall.service.plan.PlanService;
import scrbg.meplat.mall.vo.product.website.SubmitOrderByPlanVO;

/**
 * 订单相关接口
 */
@RestController
@RequestMapping("userCenter/plans")
@Api(tags = "计划列表下订单")
public class OrderController {
    @Autowired
    private PlanService planService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private OrderDetailChecker orderDetailChecker;



    @PostMapping("{planId}/order")
    @ApiOperation(value = "一键下单")
    @NotResubmit
    @IsRole(roleName = RoleEnum.ROLE_3)
    public R<Void> oneClickOrdering(@RequestBody List<ProductBuyInfoDTO> dtos, @PathVariable String planId) {
        Plan plan = planService.getById(planId);
        orderService.createMaterialOrder(dtos, plan);
        return R.success();
    }

    @GetMapping("{planId}/order/check")
    @ApiOperation(value = "预检查计划数据")
    @NotResubmit
    @IsRole(roleName = RoleEnum.ROLE_3)
    public R<List<SubmitOrderByPlanVO>> check(@PathVariable String planId) {
        Plan plan = planService.getById(planId);
        return R.success(orderDetailChecker.checkPlanDetails(plan));
    }
}
