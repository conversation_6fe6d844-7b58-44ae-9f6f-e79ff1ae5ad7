package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import scrbg.meplat.mall.dto.DeviceDemand.UpdateDemandCheckStateDTO;
import scrbg.meplat.mall.entity.DeviceDemand;
import scrbg.meplat.mall.service.DeviceDemandService;

import javax.validation.Valid;

/**
 * @描述：设备需求控制类
 * @作者: y
 * @日期: 2022-11-21
 */
@RestController
@RequestMapping("/deviceDemand")
@ApiSort(value = 500)
@Api(tags = "设备需求")
public class DeviceDemandController {

    @Autowired
    public DeviceDemandService deviceDemandService;

    @PostMapping("/listByParameters")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字（需求名称，规格型号，分类名称，品牌名称）", dataTypeClass = String.class),
            @DynamicParameter(name = "classId", value = "分类ID", dataTypeClass = String.class),
            @DynamicParameter(name = "demandName", value = "需求名称", dataTypeClass = String.class),
            @DynamicParameter(name = "spec", value = "规格型号", dataTypeClass = String.class),
            @DynamicParameter(name = "brandName", value = "品牌名称", dataTypeClass = String.class),
            @DynamicParameter(name = "checkState", value = "审核状态（0:待审核1:通过2:不通过20:通过、不通过）", dataTypeClass = Integer.class),
            @DynamicParameter(name = "demandType", value = "需求类型（1租赁设备2二手设备）", dataTypeClass = Integer.class),
            @DynamicParameter(name = "releaseType", value = "发布类型（0店铺1用户2平台）", dataTypeClass = Integer.class),
            @DynamicParameter(name = "relevanceId", value = "关联id", dataTypeClass = String.class),
            @DynamicParameter(name = "orderBy", value = "排序字段(1:按修改时间排序2:按创建时间排序)", dataTypeClass = Integer.class),
            @DynamicParameter(name = "belowPrice", value = "以下价格（预算金额）", dataTypeClass = String.class),
            @DynamicParameter(name = "abovePrice", value = "以上价格（预算金额）", dataTypeClass = String.class),
            @DynamicParameter(name = "startCreateDate", value = "创建时间开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endCreateDate", value = "创建时间结束时间", dataTypeClass = String.class), @DynamicParameter(name = "startCreateDate", value = "创建时间开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "startModifiedDate", value = "修改时间开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endModifiedDate", value = "修改时间结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "isMy", value = "是否查询我发起的需求（1是0否）", dataTypeClass = Integer.class),
    })
    public PageR<DeviceDemand> listByParameters(@RequestBody JSONObject jsonObject) {
        PageUtils page = deviceDemandService.queryPage(jsonObject, Wrappers.lambdaQuery(DeviceDemand.class));
        return PageR.success(page);
    }



    @PostMapping("/updateBatch/checkState")
    @ApiOperation(value = "根据id批量修改审核状态")
    public R updateBatchCheckState(@Valid @RequestBody UpdateDemandCheckStateDTO dto) {
        deviceDemandService.updateBatchCheckState(dto);
        return R.success();
    }

//    @GetMapping("/findById")
//    @ApiOperation(value = "根据主键查询")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "id", value = "ID", required = true,
//                    dataType = "String", paramType = "query")
//    })
//    public R<DeviceDemand> findById(String id) {
//        DeviceDemand deviceDemand = deviceDemandService.getById(id);
//        return R.success(deviceDemand);
//    }
//
//    @PostMapping("/create")
//    @ApiOperation(value = "新增")
//    public R save(@RequestBody DeviceDemand deviceDemand) {
//        deviceDemandService.create(deviceDemand);
//        return R.success();
//    }
//
//    @PostMapping("/update")
//    @ApiOperation(value = "修改")
//    public R update(@RequestBody DeviceDemand deviceDemand) {
//        deviceDemandService.update(deviceDemand);
//        return R.success();
//    }
//
//    @GetMapping("/delete")
//    @ApiOperation(value = "根据主键删除")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "id", value = "ID", required = true,
//                    dataType = "String", paramType = "query")
//    })
//    public R delete(String id) {
//        deviceDemandService.delete(id);
//        return R.success();
//    }
//
//
//    @PostMapping("/deleteBatch")
//    @ApiOperation(value = "根据主键批量删除")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "ids", value = "IDS", required = true,
//                    dataType = "list", paramType = "query")
//    })
//    public R deleteBatch(@RequestBody List<String> ids) {
//        deviceDemandService.removeByIds(ids);
//        return R.success();
//    }
}
