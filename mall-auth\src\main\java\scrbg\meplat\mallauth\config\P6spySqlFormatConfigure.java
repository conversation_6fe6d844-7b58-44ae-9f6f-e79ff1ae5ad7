package scrbg.meplat.mallauth.config;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.p6spy.engine.spy.appender.MessageFormattingStrategy;

import java.time.LocalDateTime;

public class P6spySqlFormatConfigure implements MessageFormattingStrategy {

    @Override
    public String formatMessage(int connectionId, String now, long elapsed, String category, String prepared, String sql, String url) {
        if(StringUtils.isNotBlank(sql)){
            return  "\n--------------------------" +  DateUtil.formatLocalDateTime(LocalDateTime.now()) + " | 耗时 " + elapsed + " ms | SQL 语句：" + "--------------------------" + "\n" + sql.replaceAll("[\\s]+", " ");
        }else {
            return StringUtils.EMPTY;
        }
    }


}
