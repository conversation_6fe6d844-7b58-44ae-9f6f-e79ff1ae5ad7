package scrbg.meplat.mall.dto.plan;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 大宗零购计划(物资贸易平台)
 */
@ApiModel(value="大宗零购计划")
@Data
public class BulkRetailPlanEX {

    @ApiModelProperty(value = "计划id")

    private String billId;

    @ApiModelProperty(value = "计划编号")

    private String billNo;

    @ApiModelProperty(value = "创建时间")

    private String gmtCreate;

    @ApiModelProperty(value = "更新时间")

    private String gmtModified;

    @ApiModelProperty(value = "创建人Id")

    private String founderId;

    @ApiModelProperty(value = "创建人名称")

    private String founderName;

    @ApiModelProperty(value = "单据机构id")

    private String orgId;

    @ApiModelProperty(value = "单据机构名称")

    private String orgName;

    @ApiModelProperty(value = "不含税计划金额")

    private BigDecimal amount;

    @ApiModelProperty(value = "税率")

    private BigDecimal taxRate;

    @ApiModelProperty(value = "税额")

    private BigDecimal taxAmount;

    @ApiModelProperty(value = "计划金额")

    private BigDecimal totalAmount;

    @ApiModelProperty(value = "计划日期")

    private String planDate;

    @ApiModelProperty(value = "备注")

    private String remarks;

    @ApiModelProperty(value = "填报人id")

    private String preparerId;
    @ApiModelProperty(value = "商品供应商机构名称")

    private String supplierName;

    @ApiModelProperty(value = "填报人名称")

    private String preparer;


    @ApiModelProperty(value = "清单类型(1:浮动价格;2:固定价格)")

    private Integer type;

    @ApiModelProperty(value = "商城单据id")

    private String sourceBillId;

    @ApiModelProperty(value = "商城单据编号")

    private String sourceBillNo;

    @ApiModelProperty(value = "大宗零购计划明细实体集合类")

    private List<BulkRetailPlanDtlEX> details;


    @ApiModelProperty(value = "信用代码")

    private String creditCode;
    @ApiModelProperty(value = "供应商机构短码")

    private String orgShort;


}
