package scrbg.meplat.mall.config.rabbitMQ;

import com.alibaba.fastjson.JSON;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

/**
 * TT待办RabbitMQ静态工具类
 * https://www.yuque.com/henrypu/xgutx5/pg8ebd?singleDoc# 《MDM主数据v2说明文档》 密码：spzr
 * https://www.yuque.com/henrypu/xgutx5/bizbor?singleDoc# 《第三方RabbitMQ接入TT待办》 密码：ucx9
 */
@Slf4j
@Component
public class TTToDoRabbitMQUtil {

    @Autowired
    @Qualifier("ttRabbitTemplate")
    private RabbitTemplate rabbitTemplate;

    // 静态引用
    private static TTToDoRabbitMQUtil instance;

    // RabbitMQ配置
    private static final String EXCHANGE_NAME = "TT_ToDoList";
    private static final String ROUTING_KEY_INCREMENT = "Increment";  // 增量
    private static final String ROUTING_KEY_FULL = "Full";            // 全量
    private static final String SYSTEM_NAME = "物资采购平台";

    /**
     * 初始化静态实例
     */
    @PostConstruct
    public void init() {
        instance = this;
    }

    /**
     * 获取静态实例
     */
    public static TTToDoRabbitMQUtil getInstance() {
        if (instance == null) {
            throw new RuntimeException("TTToDoRabbitMQUtil未初始化，请确保Spring容器已启动");
        }
        return instance;
    }

    // ========================= 静态方法 =========================

    /**
     * 1. 实时推送待办 - 静态方法
     */
    public static void sendIncrementToDo(List<ToDoMessageBody> todoList) {
        getInstance().doSendIncrementToDo(todoList);
    }

    /**
     * 2. 实时推送已办 - 静态方法
     */
    public static void sendIncrementDone(List<ToDoMessageBody> todoList) {
        getInstance().doSendIncrementDone(todoList);
    }

    /**
     * 3. 全量推送待办 - 静态方法
     */
    public static void sendFullToDo(List<ToDoMessageBody> todoList) {
        getInstance().doSendFullToDo(todoList);
    }

    /**
     * 4. 全量推送已办 - 静态方法
     */
    public static void sendFullDone(List<ToDoMessageBody> todoList) {
        getInstance().doSendFullDone(todoList);
    }

    /**
     * 5. 推送单个待办 - 静态方法
     */
    public static void sendSingleToDo(ToDoMessageBody todo) {
        getInstance().doSendSingleToDo(todo);
    }

    /**
     * 6. 完成单个待办 - 静态方法
     */
    public static void completeSingleToDo(String toDoId, String employeeNumber, String userId) {
        getInstance().doCompleteSingleToDo(toDoId, employeeNumber, userId);
    }

    // ========================= 实例方法（供静态方法调用） =========================

    /**
     * 实时推送待办 - 实例方法
     */
    private void doSendIncrementToDo(List<ToDoMessageBody> todoList) {
        sendMessage(todoList, ROUTING_KEY_INCREMENT);
        log.info("实时推送待办数量: {}", todoList.size());
    }

    /**
     * 实时推送已办 - 实例方法
     */
    private void doSendIncrementDone(List<ToDoMessageBody> todoList) {
        // 设置所有状态为已完成
        todoList.forEach(todo -> {
            todo.setStatus(1);
            todo.setLastupdateTime(getCurrentTime());
        });
        sendMessage(todoList, ROUTING_KEY_INCREMENT);
        log.info("实时推送已办数量: {}", todoList.size());
    }

    /**
     * 全量推送待办 - 实例方法
     */
    private void doSendFullToDo(List<ToDoMessageBody> todoList) {
        sendMessage(todoList, ROUTING_KEY_FULL);
        log.info("全量推送待办数量: {}", todoList.size());
    }

    /**
     * 全量推送已办 - 实例方法
     */
    private void doSendFullDone(List<ToDoMessageBody> todoList) {
        // 设置所有状态为已完成
        todoList.forEach(todo -> {
            todo.setStatus(1);
            todo.setLastupdateTime(getCurrentTime());
        });
        sendMessage(todoList, ROUTING_KEY_FULL);
        log.info("全量推送已办数量: {}", todoList.size());
    }

    /**
     * 推送单个待办 - 实例方法
     */
    private void doSendSingleToDo(ToDoMessageBody todo) {
        List<ToDoMessageBody> todoList = Arrays.asList(todo);
        sendMessage(todoList, ROUTING_KEY_INCREMENT);
        log.info("推送单个待办成功: {}", todo.getToDoId());
    }

    /**
     * 完成单个待办 - 实例方法
     */
    private void doCompleteSingleToDo(String toDoId, String employeeNumber, String userId) {
        ToDoMessageBody todo = new ToDoMessageBody();
        todo.setToDoId(toDoId);
        todo.setOrginSystem(SYSTEM_NAME);
        todo.setModule("物资采购-计划审核");
        todo.setEmployeeNumber(employeeNumber);
        todo.setUserId(userId);
        todo.setTodoType("计划审核");
        todo.setTitle("物资采购计划审核");
        todo.setDescription("计划审核已完成");
        todo.setStatus(1); // 已完成
        todo.setLastupdateTime(getCurrentTime());
        todo.setAppId("wzsc.scrbg.com");
        todo.setWebUrl("");
        todo.setUwpUrl("");
        todo.setIosUrl("");
        todo.setAndroidUrl("");

        List<ToDoMessageBody> todoList = Arrays.asList(todo);
        sendMessage(todoList, ROUTING_KEY_INCREMENT);
        log.info("完成单个待办成功: {}", toDoId);
    }

    /**
     * 核心发送方法
     */
    private void sendMessage(List<ToDoMessageBody> todoList, String routingKey) {
        try {
            ToDoMessage message = new ToDoMessage();
            message.setMessage_id(UUID.randomUUID().toString());
            message.setMessage_system(SYSTEM_NAME);
            message.setMessage_date(getCurrentTime());
            message.setMessage_body(todoList);

            String jsonMessage = JSON.toJSONString(message);
            rabbitTemplate.convertAndSend(EXCHANGE_NAME, routingKey, jsonMessage);

            log.info("发送TT消息成功 - 路由键: {}, 数量: {}", routingKey, todoList.size());
        } catch (Exception e) {
            log.error("发送TT消息失败 - 路由键: {}, 错误: {}", routingKey, e.getMessage(), e);
            throw new RuntimeException("发送TT消息失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前时间
     */
    private String getCurrentTime() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"));
    }

    // ========================= 纯静态工具方法 =========================

    /**
     * 创建待办消息体
     */
    public static ToDoMessageBody createTodoBody(String toDoId, String employeeNumber, String userId,
                                                 String title, String description, String webUrl) {
        ToDoMessageBody todo = new ToDoMessageBody();
        todo.setToDoId(toDoId);
        todo.setOrginSystem(SYSTEM_NAME);
        todo.setModule("物资采购-计划审核");
        todo.setEmployeeNumber(employeeNumber);
        todo.setUserId(userId);
        todo.setTodoType("计划审核");
        todo.setTitle(title);
        todo.setDescription(description);
        todo.setStatus(0); // 待办
        todo.setLastupdateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss")));
        todo.setAppId("wzsc.scrbg.com");
        todo.setWebUrl(webUrl);
        todo.setUwpUrl("");
        todo.setIosUrl("");
        todo.setAndroidUrl("");
        return todo;
    }

    /**
     * 检查工具类是否已初始化
     */
    public static boolean isInitialized() {
        return instance != null;
    }
}

// ========================= 使用示例 =========================
/*
// 1. 推送单个待办
ToDoMessageBody todo = TTToDoRabbitMQUtil.createTodoBody(
    "PLAN_REVIEW_123",
    "036529",
    "391E2FB8-F295-4045-8CDC-340AD3DE6700",
    "物资采购计划审核",
    "您有一个物资采购计划需要审核",
    "http://domain.com/plan/review?planId=123"
);
TTToDoRabbitMQUtil.sendSingleToDo(todo);

// 2. 完成待办
TTToDoRabbitMQUtil.completeSingleToDo(
    "PLAN_REVIEW_123",
    "036529",
    "391E2FB8-F295-4045-8CDC-340AD3DE6700"
);

// 3. 批量推送待办 - Java 8 兼容写法
List<ToDoMessageBody> todoList = Arrays.asList(todo1, todo2, todo3);
TTToDoRabbitMQUtil.sendIncrementToDo(todoList);

// 4. 批量推送已办
TTToDoRabbitMQUtil.sendIncrementDone(todoList);

// 5. 检查是否已初始化
if (TTToDoRabbitMQUtil.isInitialized()) {
    // 可以安全使用静态方法
}

// 6. Java 8 创建单元素列表的其他方式
List<ToDoMessageBody> singleList1 = Arrays.asList(todo);                    // 推荐
List<ToDoMessageBody> singleList2 = Collections.singletonList(todo);       // 不可变列表
List<ToDoMessageBody> singleList3 = new ArrayList<>(Arrays.asList(todo));  // 可变列表
*/