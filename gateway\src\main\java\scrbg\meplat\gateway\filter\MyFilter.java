package scrbg.meplat.gateway.filter;

import com.scrbg.common.utils.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import scrbg.meplat.gateway.config.IgnoreUrlsConfig;
import scrbg.meplat.gateway.config.MallConfig;
import scrbg.meplat.gateway.exception.BusinessException;

import java.util.List;
import java.util.Map;

@Component
public class MyFilter implements GlobalFilter, Ordered {


    @Autowired
    private MallConfig mallConfig;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private RestTemplate restTemplate;

     @Autowired
     private IgnoreUrlsConfig ignoreUrlsConfig;

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        // 白名单处理
        String path = request.getURI().getPath();
        PathMatcher pathMatcher = new AntPathMatcher();
        List<String> urls = ignoreUrlsConfig.getUrls();
        for (String url : urls) {
            if (pathMatcher.match(url, path)) {
                request = exchange.getRequest().mutate().header(HttpHeaders.AUTHORIZATION, "").build();
                exchange = exchange.mutate().request(request).build();
                return chain.filter(exchange);
            }
        }
        return chain.filter(exchange);
//        String token = request.getHeaders().getFirst("token");
//        if (token == null) {
//            throw new BusinessException(400, "请先登陆");
//        } else {
//            String url = mallConfig.prodPcwp2Url + "/identity/auth/verifyToken?token=" + token;
//            R<Map> r = restTemplate.postForObject(url, null, R.class);
//            if (r.getCode() == 200) {
//                return chain.filter(exchange);
//            } else {
//                throw new BusinessException(r.getCode(), r.getMessage());
//            }
//        }
//        return exchange.getResponse().setComplete();// 拦截
    }
 
    /**
     * 过滤器排序
     * @return 数值越小 越先执行
     */
    @Override
    public int getOrder() {
        return -22;
    }
}