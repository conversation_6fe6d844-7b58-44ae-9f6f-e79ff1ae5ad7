package scrbg.meplat.mall.mapper;

import org.apache.ibatis.annotations.Param;
import scrbg.meplat.mall.entity.ProductRulesClass;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @描述：商品预警规则和分类关联表 Mapper 接口
 * @作者: ye
 * @日期: 2024-03-20
 */
@Mapper
@Repository
public interface ProductRulesClassMapper extends BaseMapper<ProductRulesClass> {

    List<ProductRulesClass> selcetDataByClassIdAndMaterialId( @Param("classId")String classId,  @Param("relevanceId")String relevanceId);
}