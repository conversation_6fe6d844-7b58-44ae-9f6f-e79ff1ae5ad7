package scrbg.meplat.mall.dto.reconciliation;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.MaterialReconciliationDtl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-07-31 8:42
 */
@Data
public class MaterialReconciliationUpdateDTO {

    @ApiModelProperty(value = "对账单ID")
    private String reconciliationId;

    @ApiModelProperty(value = "对账开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    @ApiModelProperty(value = "对账结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "是否提交")
    private int isSubmit;

    @ApiModelProperty(value = "对账明细")
    private List<MaterialReconciliationDtl> dtl;

    @ApiModelProperty(value = "keyId")
    private String keyId;

    @ApiModelProperty(value = "对账类型（1浮动价格对账单2固定价格对账单）")
    private Integer type;

    @ApiModelProperty(value = "对账总金额（含税）")
    private BigDecimal reconciliationAmount;

    @ApiModelProperty(value = "对账总金额（不含税）")
    private BigDecimal reconciliationNoRateAmount;

    @ApiModelProperty(value = "税额")
    private BigDecimal taxAmount;

    @ApiModelProperty(value = "税率(%)")
    private BigDecimal taxRate;


}
