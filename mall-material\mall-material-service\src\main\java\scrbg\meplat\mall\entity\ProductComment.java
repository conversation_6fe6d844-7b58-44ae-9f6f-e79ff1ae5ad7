package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @描述：商品评价
 * @作者: y
 * @日期: 2022-11-13
 */
@ApiModel(value = "商品评价")
@Data
@TableName("product_comment")
public class ProductComment extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "评论id")

    private String commentId;

    @ApiModelProperty(value = "商品id")

    private String productId;

    @ApiModelProperty(value = "评论用户id")

    private String userId;

    @ApiModelProperty(value = "父评论id")

    private String parentId;

    @ApiModelProperty(value = "订单id")

    private String orderId;
    @ApiModelProperty(value = "订单项id")

    private String orderItemId;

    @ApiModelProperty(value = "商品名称")

    private String productName;

    @ApiModelProperty(value = "是否匿名（1:是，0:否）")

    private Integer isAnonymous;

    @ApiModelProperty(value = "评价类型（1好评，2中评，3差评）")

    private Integer commentType;

    @ApiModelProperty(value = "评价分数-商品品质")

    private Integer commentLevel;

    @ApiModelProperty(value = "评价分数-保供能力")

    private Integer commentSupply;

    @ApiModelProperty(value = "评价分数-诚信履约")

    private Integer commentIntegrity;

    @ApiModelProperty(value = "评价分数-服务水平")

    private Integer commentService;

    @ApiModelProperty(value = "评价内容")

    private String commentContent;

    @ApiModelProperty(value = "评价晒图(JSON {img1:url1,img2:url2}  )")

    private String commentImgs;

    @ApiModelProperty(value = "评价时间")

    private Date evaluateTime;

    @ApiModelProperty(value = "是否回复（0:未回复，1:已回复）")

    private Integer isReply;

    @ApiModelProperty(value = "回复内容")

    private String replyContent;

    @ApiModelProperty(value = "回复时间")

    private Date replyTime;

    @ApiModelProperty(value = "是否显示（1:是，0:否）")

    private Integer isShow;


    @ApiModelProperty(value = "状态")

    private Integer state;

    @ApiModelProperty(value = "商品类型：0物资 1设备 2周材（设备租赁） 3周材（设备） 4二手设备 5租赁设备")
    private Integer productType;

    @ApiModelProperty(value = "评分结束时间")
    @TableField(exist = false)

    private Date commentEndDate;

    @ApiModelProperty(value = "评分开始时间")
    @TableField(exist = false)

    private Date commentStartDate;

    @ApiModelProperty(value = "订单id数组")
    @TableField(exist = false)

    private List<String> orderIds;

    @ApiModelProperty(value = "商品评价数组")
    @TableField(exist = false)

    private List<OrderItem> orderItemComments;

    @ApiModelProperty(value = "评价图")
    @TableField(exist = false)

    private List<File> commentFile;

}
