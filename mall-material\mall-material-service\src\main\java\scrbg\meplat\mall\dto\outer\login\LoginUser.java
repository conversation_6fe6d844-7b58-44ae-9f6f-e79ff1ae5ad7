package scrbg.meplat.mall.dto.outer.login;

import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @program: maill_api
 * @description: 用于电子招标平台登录参数携带
 * @author: 代文翰
 * @create: 2023-11-20 10:57
 **/
@Data
@Builder
public class LoginUser {
    @NotBlank(message = "用户账号不能为空")
    private String userName;
    @NotBlank(message = "用户密码不能为空")
    private String password;
}
