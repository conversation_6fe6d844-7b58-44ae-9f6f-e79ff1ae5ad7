package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.entity.FloorGoods;
import scrbg.meplat.mall.service.FloorGoodsService;

import java.util.List;

/**
 * @描述：楼层显示的商品控制类
 * @作者: 胡原武
 * @日期: 2022-11-10
 */
@RestController
@RequestMapping("/platform/floorGoods")
@ApiSort(value = 500)
@Api(tags = "楼层显示的商品（后台）")
public class FloorGoodsController {

    @Autowired
    public FloorGoodsService floorGoodsService;

//    @PostMapping("/listByEntity")
//    @ApiOperation(value = "根据实体属性分页查询")
//    @DynamicParameters(name = "根据实体属性分页查询", properties = {
//            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
//            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
//    })
//    public PageR<FloorGoods> listByEntity(@RequestBody JSONObject jsonObject) {
//        PageUtils page = floorGoodsService.queryPage(jsonObject, new LambdaQueryWrapper<FloorGoods>());
//        return PageR.success(page);
//    }

    @PostMapping("/listByEntityByCondition")
    @ApiOperation(value = "根据条件查询并分页")
    @DynamicParameters(name = "根据条件查询并分页", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "floorId", value = "显示楼层id", dataTypeClass = String.class),
            @DynamicParameter(name = "founderName", value = "创建人名称", dataTypeClass = String.class),
            @DynamicParameter(name = "state", value = "显示状态", dataTypeClass = Integer.class)
    })
    public PageR<FloorGoods> listByEntityByCondition(@RequestBody JSONObject jsonObject) {
        PageUtils page = floorGoodsService.queryFlooerGoodsPage(jsonObject, new LambdaQueryWrapper<>());
        return PageR.success(page);
    }

    @PostMapping("/updateByPublish")
    @ApiOperation(value = "批量发布")
    public R updatePublish(@RequestBody List<String> ids) {
        floorGoodsService.updateByPublish(ids, "1");
        return R.success();
    }

    @PostMapping("/updateNotPublish")
    @ApiOperation(value = "批量取消发布")
    public R updateNotPublish(@RequestBody List<String> ids) {
        floorGoodsService.updateByPublish(ids, "0");
        return R.success();
    }

    /**
     * 批量更新栏目信息
     *
     * @param
     * @return
     */
    @PostMapping("/updateBatchById")
    @ApiOperation(value = "批量更新栏目信息")
    public R update(@RequestBody List<FloorGoods> floorList) {
        floorGoodsService.updateBatchById(floorList);
        return R.success();
    }

    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<FloorGoods> findById(String id) {
        FloorGoods floorGoods = floorGoodsService.getById(id);
        return R.success(floorGoods);
    }

    @PostMapping("/batchCreate")
    @ApiOperation(value = "批量新增")
    public R batchCreate(@RequestBody List<FloorGoods> floorGoods) {
        floorGoodsService.batchCreate(floorGoods);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody FloorGoods floorGoods) {
        floorGoodsService.update(floorGoods);
        return R.success();
    }

    @PostMapping("/batchUpdate")
    @ApiOperation(value = "批量修改")
    public R batchUpdate(@RequestBody List<FloorGoods> floorGoodss) {
        floorGoodsService.updateBatchById(floorGoodss);
        return R.success();
    }

    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        floorGoodsService.delete(id);
        return R.success();
    }


    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")

    public R deleteBatch(@RequestBody List<String> ids) {
        floorGoodsService.removeByIds(ids);
        return R.success();
    }
}

