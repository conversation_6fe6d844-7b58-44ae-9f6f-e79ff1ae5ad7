package scrbg.meplat.mall.entity.excelTemplate;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2023-03-07 9:19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@HeadRowHeight(40)
@ColumnWidth(25)
@ContentRowHeight(40)
public class LcMaterial implements Serializable {

//        private static final long serialVersionUID = -5144055068797033748L;

        private Long id;

        @ExcelProperty(value = "商品名称（必填）", index = 1)
        private String productName;

        @ExcelProperty(value = "分类名称(XXX/XXX/XXX)（必填）", index = 2)
        private String classNamePath;

        @ExcelProperty(value = "物料编号（必填）", index = 3)
        private String materialNo;

        @ExcelProperty(value = "物料名称（必填）", index = 4)
        private String materialName;

        @ExcelProperty(value = "最低价", index = 5)
        private BigDecimal productMinPrice;

        @ExcelProperty(value = "品牌名称", index = 6)
        private String brandName;

        @ExcelProperty(value = "排序值", index = 7)
        private Integer shopSort;

        @ExcelProperty(value = "规格（必填）", index = 8)
        private String skuName;

        @ExcelProperty(value = "成本价（必填）", index = 9)
        private BigDecimal costPrice;

        @ExcelProperty(value = "库存", index = 10)
        private BigDecimal stock;

        @ExcelProperty(value = "计量单位", index = 11)
        private String unit;

        @ExcelProperty(value = "原价（必填）", index = 12)
        private BigDecimal originalPrice;

        @ExcelProperty(value = "销售价格（必填）", index = 13)
        private BigDecimal sellPrice;

        @ApiModelProperty(value = "商品材质")
        @ExcelProperty(value = "商品材质", index = 14)
        private String productTexture;

        @ExcelProperty(value = "副单位系数", index = 15)
        private BigDecimal secondUnitNum;

        @ExcelProperty(value = "副计量单位", index = 16)
        private String secondUnit;

        @ExcelProperty(value = "商品描述（可html代码）", index = 17)
        @ColumnWidth(50)
        private String productDescribe;

}


