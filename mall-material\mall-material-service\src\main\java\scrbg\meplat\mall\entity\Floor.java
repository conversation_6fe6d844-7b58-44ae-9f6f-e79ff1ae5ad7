package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @描述：客户端商品展示楼层
 * @作者: y
 * @日期: 2022-12-09
 */
@ApiModel(value="客户端商品展示楼层")
@Data
@TableName("floor")
public class Floor extends MustBaseEntity implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "楼层id")
    private String floorId;

    @ApiModelProperty(value = "栏目id")

    private String columnId;


    @ApiModelProperty(value = "栏目名称")

    private String columnName;


    @ApiModelProperty(value = "楼层名称")

    private String floorName;


    @ApiModelProperty(value = "楼层名称后小字")

    private String floorNameText;


    @ApiModelProperty(value = "主图id")

    private String imgUrlId;


    @ApiModelProperty(value = "楼层主图链接")

    private String imgUrl;

    @ApiModelProperty(value = "楼层背景图链接")

    private String backgroundUrl;


    @ApiModelProperty(value = "点击主图链接地址")

    private String mainImgUrl;


    @ApiModelProperty(value = "商品类别")

    private String floorProductType;


    @ApiModelProperty(value = "使用页面（枚举类型）待讨论")

    private Integer usePage;

    @ApiModelProperty(value = "商品分类ID")

    private String classId;

    @TableField(exist = false)
    @ApiModelProperty(value = "商品分类path数组，只用于接收前端分类数组")

    private List<String> classPaths;
    @ApiModelProperty(value = "商品分类path")

    private String classPath;
    @ApiModelProperty(value = "楼层商品分类名称")

    private String className;
    @ApiModelProperty(value = "发布时间")

    private Date gmtRelease;

    @ApiModelProperty(value = "楼层状态（1：显示  0：不显示）")

    private Integer state;















}
