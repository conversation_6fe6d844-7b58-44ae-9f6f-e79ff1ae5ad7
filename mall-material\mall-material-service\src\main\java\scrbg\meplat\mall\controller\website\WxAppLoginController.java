package scrbg.meplat.mall.controller.website;

import com.scrbg.common.utils.R;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.service.WxAppLoginService;
import scrbg.meplat.mall.vo.user.WxLoginVo;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2023/7/14
 */
@RestController
@RequestMapping("/w/wxuser")
@Api(tags = "微信小程序登录接口")
public class WxAppLoginController {
    @Value("${spring.profiles.active}")
    private String env;

    @Autowired
    private WxAppLoginService wxAppLoginService;

    @GetMapping("/getOpenId")
    @ApiOperation(value = "收料员登录通过code获取微信openid")
    public R getOpenidByCode(@RequestParam String code) {
        return R.success(wxAppLoginService.getSlWxOpenIdByCode(code));
    }

    @GetMapping("/getSupplierOpenId")
    @ApiOperation(value = "收料员登录通过code获取微信openid")
    public R getSupplierOpenidByCode(@RequestParam String code) {
        return R.success(wxAppLoginService.getSupplierOpenIdByCode(code));
    }

    @PostMapping("/sl/wxOpenIdLogin")
    @ApiOperation(value = "收料员微信openId登录")
    public R loginSlByOpenID(@RequestBody WxLoginVo wxLoginVo) {
        return R.success(wxAppLoginService.loginSlByOpenId(wxLoginVo.getOpenid()));
    }

    @PostMapping("/sl/loginSendCode")
    @ApiOperation(value = "收料登陆发送手机验证码")
    public R loginSendSlCode(@RequestBody WxLoginVo wxLoginVo, HttpServletRequest request) {
        wxAppLoginService.sendWxLoginCode(wxLoginVo, "slCode",request);
        return R.success();
    }

    @PostMapping("/sl/phoneLogin")
    @ApiOperation("收料员手机号登陆")
    @ApiImplicitParams({
            @ApiImplicitParam(dataType = "string", name = "phone", value = "用户登录账号", required = true),
            @ApiImplicitParam(dataType = "string", name = "code", value = "验证码", required = true),
            @ApiImplicitParam(dataType = "string", name = "openid", value = "微信openId", required = true),
    })
    public R phoneSlLogin(@RequestBody WxLoginVo wxLoginVo) {
        return R.success(wxAppLoginService.wxLoginSlByPhoneCode(wxLoginVo.getPhone(), wxLoginVo.getCode(), wxLoginVo.getOpenid()));
    }

    @PostMapping("/supplier/loginSendCode")
    @ApiOperation(value = "供应商登陆发送手机验证码")
    public R loginSendSupplierCode(@RequestBody WxLoginVo wxLoginVo,HttpServletRequest request) {
        wxAppLoginService.sendWxLoginCode(wxLoginVo, "supplierCode", request);
        return R.success();
    }

    @PostMapping("/supplier/phoneLogin")
    @ApiOperation("供应商微信手机号登陆")
    @ApiImplicitParams({
            @ApiImplicitParam(dataType = "string", name = "phone", value = "用户登录账号", required = true),
            @ApiImplicitParam(dataType = "string", name = "code", value = "验证码", required = true),
            @ApiImplicitParam(dataType = "string", name = "openid", value = "微信openId", required = true),
    })
    public R phoneSupplierLogin(@RequestBody WxLoginVo wxLoginVo) {
        wxAppLoginService.wxLoginSupplierByPhoneCode(wxLoginVo.getPhone(), wxLoginVo.getCode(), wxLoginVo.getOpenid());
        return R.success();
    }

    @PostMapping("/sl/logout")
    @ApiOperation("收料微信退出登录")
    @ApiParam(type = "string", name = "phone", value = "退出登录手机号", required = true)
    public R logoutSl(@RequestBody WxLoginVo wxLoginVo) {
        wxAppLoginService.logout(wxLoginVo, "slUser");
        return R.success();
    }

    @PostMapping("/supplier/wxOpenIdLogin")
    @ApiOperation(value = "供货商微信openId登录")
    public R loginSupplierByOpenID(@RequestBody WxLoginVo wxLoginVo) {
        return R.success(wxAppLoginService.loginSupplierByOpenId(wxLoginVo.getOpenid()));
    }

    @PostMapping("/supplier/logout")
    @ApiOperation("收料微信退出登录")
    @ApiParam(type = "string", name = "phone", value = "退出登录手机号", required = true)
    public R logoutSupplier(@RequestBody WxLoginVo wxLoginVo) {
        wxAppLoginService.logout(wxLoginVo, "supplierUser");
        return R.success();
    }

    /**
     * 收料员手机号一键登录
     * @param wxLoginVo
     * @return
     */
    @PostMapping("/sl/login")
    @ApiOperation("收料微信手机号一键登录")
    public R loginSlByPhone(@RequestBody WxLoginVo wxLoginVo){
//        if (!env.equalsIgnoreCase("dev")){
//            throw new BusinessException("当前环境不可用");
//        }
        return R.success(wxAppLoginService.loginByPhone(wxLoginVo.getPhone()));
    }

    /**
     * 供货商手机号一件登录
     * @param wxLoginVo
     * @return
     */
    @PostMapping("/supplier/login")
    @ApiOperation("收料微信手机号一键登录")
    public R loginsupplierByPhone(@RequestBody WxLoginVo wxLoginVo){
        if (!env.equalsIgnoreCase("dev")){
            throw new BusinessException("当前环境不可用");
        }
        return R.success(wxAppLoginService.loginSupplierByPhone(wxLoginVo.getPhone()));
    }




}
