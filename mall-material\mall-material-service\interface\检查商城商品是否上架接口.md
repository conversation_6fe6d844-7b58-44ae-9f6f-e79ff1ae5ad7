## 检查商品是否上架

**这是测试环境的ip后续会更改**

**接口地址**:`http://*************:9011/w/product/pcwpContract/checkIsPutaway`

**请求方式**:`POST`

**请求数据类型**:`application/json`

**响应数据类型**:`*/*`

**请求示例**:


```javascript
{
  "products": [
    {
      "productName": "",
      "size": ""
    }
  ],
  "socialCreditCode": "",
  "type": 0
}
```


**请求参数**:


**请求参数**:


| 参数名称                            | 参数说明                         | 请求类型 | 是否必须 | 数据类型           | schema             |
| ----------------------------------- | -------------------------------- | -------- | -------- | ------------------ | ------------------ |
| dto                                 | dto                              | body     | true     | CheckIsPutawayPDTO | CheckIsPutawayPDTO |
| &emsp;&emsp;products                | 商品                             |          | true     | array              | CheckIsPutawayDTO  |
| &emsp;&emsp;&emsp;&emsp;productName | 商品名称                         |          | true     | string             |                    |
| &emsp;&emsp;&emsp;&emsp;size        | 规格型号                         |          | true     | string             |                    |
| &emsp;&emsp;socialCreditCode        | 统一社会信用代码                 |          | true     | string             |                    |
| &emsp;&emsp;type                    | 合同类型（7采购合同，8租赁合同） |          | true     | integer(int32)     |                    |


**响应状态**:


| 状态码 | 说明         | schema                    |
| ------ | ------------ | ------------------------- |
| 200    | OK           | R«List«CheckIsPutawayVO»» |
| 201    | Created      |                           |
| 401    | Unauthorized |                           |
| 403    | Forbidden    |                           |
| 404    | Not Found    |                           |


**响应参数**:


| 参数名称                | 参数说明              | 类型           | schema           |
| ----------------------- | --------------------- | -------------- | ---------------- |
| code                    | 返回状态码            | integer(int32) | integer(int32)   |
| data                    | 返回对象              | array          | CheckIsPutawayVO |
| &emsp;&emsp;errCode     | 错误码 404:装备未上架 | integer(int32) |                  |
| &emsp;&emsp;productName | 商品名称              | string         |                  |
| &emsp;&emsp;size        | 规格型号              | string         |                  |
| message                 | 返回消息              | string         |                  |


**响应示例**:
```javascript
{
	"code": 0,
	"data": [
		{
			"errCode": 0,
			"productName": "",
			"size": ""
		}
	],
	"message": ""
}
```

## 接口返回说明：

情况1：如果所有的商品都已经上架:返回结果

```txt
{
  "code": 200,
  "message": "操作成功",
  "data": []
}
```

情况2：未开店、供应商未注册
50010：供应商未注册
50011：该供应商未开店

```txt
{
  "code": 50010,
  "message": "供应商未注册！",
  "data": null
}
```

情况3：部分商品未上架，只会返回未上架的信息和errCode
404则表示商品没有在商城上架

```txt
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "errCode": 404,
      "productName": "轮胎装1载机",
      "size": "XC300"
    }
  ]
}
```

情况4：未携带参数

```txt
{
    "code": 405,
    "message": "参数检验失败",
    "data": {
        "type": "合同类型不能为空！",
        "socialCreditCode": "统一社会信用代码不能为空！",
        "products": "商品不能为空！"
    }
}
```

