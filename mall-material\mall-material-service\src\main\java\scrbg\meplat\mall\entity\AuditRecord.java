package scrbg.meplat.mall.entity;

import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
/**
 * @描述：审核记录
 * @作者: ye
 * @日期: 2023-06-27
 */
@ApiModel(value="审核记录")
@Data
@TableName("audit_record")
public class AuditRecord extends MustBaseEntity implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "审核id")
    private String auditRecordId;

    @ApiModelProperty(value = "关联id")
    private String relevanceId;

    @ApiModelProperty(value = "结果类型（1通过2未通过）")
    private Integer resultType;

    @ApiModelProperty(value = "审核类型（1提交审核2变更审核6竞价中标审核7作废审核 8红字审核 9开票审核）")
    private Integer auditType;

    @ApiModelProperty(value = "关联类型（1月供计划2月供变更计划3竞价采购提交4竞价采购中标" +
            "5对账单6二级对账单 7大宗临购清单 8发票 9大宗临购供应商拒绝审核 10年度服务费缴费审核 11交易服务费缴费审核12交易服务费对账单确认审核）")
    private Integer relevanceType;

    @ApiModelProperty(value = "审核结果")
    private String auditResult;
}
