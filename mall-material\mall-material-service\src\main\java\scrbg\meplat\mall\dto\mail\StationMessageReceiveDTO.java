package scrbg.meplat.mall.dto.mail;

import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.util.Date;

@Data
public class StationMessageReceiveDTO extends MustBaseEntity {

    private Integer start;
    private Integer limit;
    private String stationMessageId;
    private String keywords;
    private Integer state;
    private Date sendDate;
    private String title;
    private String sendName;
    private Integer orderBy;
}
