package scrbg.meplat.mall.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2023-08-02 10:31
 */
@Data
public class AddSettlementAmountDTO {

    @ApiModelProperty(value = "对账单id",required = true)
    @NotEmpty(message = "对账单id不能为空！")
    private String reconciliationId;

    @ApiModelProperty(value = "金额（不管作废还是推送都是正数）")
    private BigDecimal amount;


//    @ApiModelProperty(value = "物资类别id(1级类别id/2级类别id/..)")
//
//    private String materialClassId;
//
//
//    @ApiModelProperty(value = "物资类别名称(1级类别名称/2级类别名称/..)")
//
//    private String materialClassName;


    @ApiModelProperty(value = "物资id")

    private String materialId;


    @ApiModelProperty(value = "物资名称")

    private String materialName;


    @ApiModelProperty(value = "规格型号")

    private String spec;


    @ApiModelProperty(value = "计量单位")

    private String unit;


    @ApiModelProperty(value = "材质")

    private String texture;
}
