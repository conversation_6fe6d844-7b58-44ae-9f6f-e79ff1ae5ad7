package scrbg.meplat.mall.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-08-02 13:33
 */
@Data
public class AddSettlementAmountParentDTO {

//    @ApiModelProperty(value = "对账单id（废弃）",required = true)
//    private String reconciliationId;

    @ApiModelProperty(value = "数据",required = true)
    @Valid
    private List<AddSettlementAmountDTO> dtos;

    @ApiModelProperty(value = "keyId",required = true)
    @NotEmpty(message = "keyId不能为空！")
    private String keyId;


    @ApiModelProperty(value = "标识（1推送推送2作废推送）",required = true)
    @NotNull(message = "标识（1推送推送2作废推送）不能为空！")
    private Integer flag;


    @ApiModelProperty(value = "结算id（pcwp）",required = true)
    private String settleAccountsId;


}
