package scrbg.meplat.mall.config.rabbitMQ;//package scrbg.meplat.mall.config.rabbitMQ;
//
//import org.springframework.amqp.rabbit.annotation.RabbitListener;
//import org.springframework.stereotype.Component;
//
///**
// * <AUTHOR>
// * @create 2023-01-05 13:25
// */
//@Component
//public class SpringRabbitListener {
//
//    @RabbitListener(queues = "SMS_Service_Demo")
//    public void listenFanoutQueue1(String msg) {
//        System.out.println("消费者1接收到Fanout消息：【" + msg + "】");
//    }
//
//    @RabbitListener(queues = "fanout.queue2")
//    public void listenFanoutQueue2(String msg) {
//        System.out.println("消费者2接收到Fanout消息：【" + msg + "】");
//    }
//}