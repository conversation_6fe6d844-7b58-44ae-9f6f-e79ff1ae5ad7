package scrbg.meplat.mall.config.cache;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.interceptor.CacheOperationInvocationContext;
import org.springframework.cache.interceptor.SimpleCacheResolver;
import org.springframework.core.annotation.AnnotatedElementUtils;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.data.redis.cache.RedisCache;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;

/**
 * <AUTHOR>
 * @create 2022-12-09 16:16
 */
@Slf4j
public class RedisExpireCacheResolver extends SimpleCacheResolver {

    public RedisExpireCacheResolver(CacheManager cacheManager){
        super(cacheManager);
    }

    @Override
    public Collection<? extends Cache> resolveCaches(CacheOperationInvocationContext<?> context) {
        Collection<String> cacheNames = getCacheNames(context);
        if (cacheNames == null) {
            return Collections.emptyList();
        }
        Collection<Cache> result = new ArrayList<>(cacheNames.size());
        for (String cacheName : cacheNames) {
            Cache cache = getCacheManager().getCache(cacheName);
            if (cache == null) {
                throw new IllegalArgumentException("Cannot find cache named '" +
                        cacheName + "' for " + context.getOperation());
            }
            // 获取到Cache对象后，开始解析 @CacheExpire
            parseCacheExpire(cache,context);
            result.add(cache);
        }
        return result;
    }

    private void parseCacheExpire(Cache cache, CacheOperationInvocationContext<?> context){
        Method method= context.getMethod();

        // 方法上是否标注了CacheExpire
        if(AnnotatedElementUtils.isAnnotated(method,CacheExpire.class)){
            // 获取对象
            CacheExpire cacheExpire= AnnotationUtils.getAnnotation(method,CacheExpire.class);
            log.info("CacheExpire ttl:{}, CacheExpire unit:{}",cacheExpire.ttl(), cacheExpire.unit());
            // 将 cache强制转换成 RedisCacheUtil，准备替换掉 配置
            RedisCache redisCache=(RedisCache) cache;
            Duration duration= Duration.ofMillis(cacheExpire.unit().toMillis(cacheExpire.ttl()));
            // 替换RedisCacheConfiguration 对象
            setRedisCacheConfiguration(redisCache,duration);
        }
    }
    // 替换RedisCacheConfiguration 对象
    private void setRedisCacheConfiguration(RedisCache redisCache, Duration duration){
        RedisCacheConfiguration defaultConfiguration=redisCache.getCacheConfiguration();
        RedisCacheConfiguration configuration = RedisCacheConfiguration.defaultCacheConfig();
        configuration = configuration.serializeValuesWith
                        (defaultConfiguration.getValueSerializationPair())
                .entryTtl(duration); // 设置时间
//                .prefixKeysWith("Qw3RedisCache::Expire::"); // 设置前缀，此处设置会替换掉注解声明的前缀
//                .prefixCacheNameWith("Qw3RedisCache::Expire::"); // 废弃

        // 实践发现可以替换掉 private final  的field值
        //反射设置新的值
        Field configField = ReflectionUtils.findField(RedisCache.class,"cacheConfig", RedisCacheConfiguration.class);
        configField.setAccessible(true);
        ReflectionUtils.setField(configField,redisCache,configuration);

    }
}
