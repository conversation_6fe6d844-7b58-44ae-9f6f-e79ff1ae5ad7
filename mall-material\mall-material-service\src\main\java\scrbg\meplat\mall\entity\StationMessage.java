package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
/**
 * @描述：站点消息
 * @作者: y
 * @日期: 2022-11-24
 */
@ApiModel(value="站点消息")
@Data
@TableName("station_message")
public class StationMessage extends MustBaseEntity implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "消息id")
    private String stationMessageId;

    @ApiModelProperty(value = "发件人id")

    private String sendId;


    @ApiModelProperty(value = "发件人名称")

    private String sendName;


    @ApiModelProperty(value = "发件人类型（0店铺1用户2平台）")

    private Integer sendType;


    @ApiModelProperty(value = "发件人消息账号")

    private String sendCode;


    @ApiModelProperty(value = "是否全部已读(0否1是)")

    private Integer allRead;


    @ApiModelProperty(value = "是否有附件(0否1是)")

    private Integer isFile;


    @ApiModelProperty(value = "标题")

    private String title;


    @ApiModelProperty(value = "内容")

    private String content;


    @ApiModelProperty(value = "发送时间")

    private Date sendDate;


    @ApiModelProperty(value = "消息类型（0普通消息,1平台消息,2系统消息）")

    private Integer messageType;


    @ApiModelProperty(value = "状态")

    private Integer state;

    @ApiModelProperty(value = "附件")
    @TableField(exist = false)
    private List<File> files;
    @ApiModelProperty(value = "是否弹窗提醒(0否，1是)")

    private Integer remind;


}
