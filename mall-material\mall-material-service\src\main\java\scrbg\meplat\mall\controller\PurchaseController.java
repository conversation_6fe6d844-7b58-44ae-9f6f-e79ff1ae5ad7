package scrbg.meplat.mall.controller;

import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import scrbg.meplat.mall.dto.TheFirstLoginInfo;
import scrbg.meplat.mall.service.PurchaseService;

@RestController
@RequestMapping("/purchase")
@ApiSort(value = 500)
@Api(tags = "采购平台")
public class PurchaseController {

    @Autowired
    PurchaseService purchaseService;

    @PostMapping("/the-first-login")
    @ApiOperation(value = "初次登录")
    public R theFirstLogin(@RequestBody TheFirstLoginInfo info) {
        purchaseService.theFirstLogin(info);
        return R.success();
    }
}
