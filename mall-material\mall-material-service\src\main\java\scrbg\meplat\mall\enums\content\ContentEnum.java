package scrbg.meplat.mall.enums.content;

/**
 * Description: 内容枚举类
 * date: 2022/11/7 9:54
 * @author: sund
 * @since JDK 1.8
 */
public enum ContentEnum {
    IS_DELETE_YES(-1,"删除"),
    IS_DELETE_NO(0,"未删除"),

    IS_YES(1,"是"),
    IS_NO(0,"否"),

    STATE_STAY_PUTAWAY(0,"待发布"),
    STATE_PUTAWAY(1,"已发布"),
    STATE_SOLD_OUT(2,"未发布"),

    STATE_OPEN(1,"启用"),
    STATE_STOP(0,"停用"),

    TYPE_MATERIAL(0,"物资"),
    TYPE_EQUIMENT(1,"设备");
//    PRODUCT_TYPE_(2,"周材");

    /**
     * 编码
     */
    private int code;

    /**
     * 说明
     */
    private String remark;

    ContentEnum() {
    }

    ContentEnum(int code, String remark) {
        this.code = code;
        this.remark = remark;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}




