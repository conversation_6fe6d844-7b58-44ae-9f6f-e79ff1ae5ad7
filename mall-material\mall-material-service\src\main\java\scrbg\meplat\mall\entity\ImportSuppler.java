package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @描述：导入供应商失败的结果
 * @作者: ye
 * @日期: 2023-05-09
 */
@ApiModel(value="导入供应商失败的结果")
@Data
@TableName("import_suppler")
public class ImportSuppler implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "")
    private String importSupplerId;

    @ApiModelProperty(value = "供应商名称")

    private String enterpriseName;


    @ApiModelProperty(value = "统一社会信用代码")

    private String socialCreditCode;


    @ApiModelProperty(value = "法定代表人")

    private String legalRepresentative;


    @ApiModelProperty(value = "联系电话")

    private String adminPhone;


    @ApiModelProperty(value = "结果")

    private String state;


    @ApiModelProperty(value = "失败原因")

    private String fail;



}