package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;

/**
 * @描述：sku销售属性&值
 * @作者: y
 * @日期: 2022-11-13
 */
@ApiModel(value = "sku销售属性&值")
@Data
@TableName("sku_sale_attribute_value")
public class SkuSaleAttributeValue extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "sku销售id")

    private String skuSaleAttributeId;

    @ApiModelProperty(value = "sku_id")

    private String skuId;

    @ApiModelProperty(value = "属性id")

    private String attributeId;

    @ApiModelProperty(value = "销售属性名")

    private String attributeName;

    @ApiModelProperty(value = "销售属性值")

    private String attributeValue;

    @ApiModelProperty(value = "状态")

    private Integer state;

    @ApiModelProperty(value = "商品类型：0物资 1设备 2周材（设备租赁） 3周材（设备） 4二手设备 5租赁设备")
    private Integer productType;
}
