package scrbg.meplat.mall.dto.free;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@HeadRowHeight(40)
@ColumnWidth(40)
@ContentRowHeight(30)
@ContentFontStyle(fontHeightInPoints=16)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
public class FeeInputTemplateVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "序号（自增即可）" )
    private Long id;

    @ExcelProperty(value = "单位名称")
    private String enterpriseName;

    @ExcelProperty(value = "缴费金额")
    private BigDecimal payAmount;

    @ExcelProperty(value = "缴费时长（单位：年）")
    private Integer paymentDuration;

    @ExcelProperty(value = "缴费记类型数字（1：店铺年服务费 2：招标年服务费 3：店铺交易服务费 4：合同履约服务费用）")
    private Integer recordType;

    @ExcelProperty(value = "导入结果（成功/失败）")
    private String result;

    @ExcelProperty(value = "失败原因")
    private String failReason;
}
