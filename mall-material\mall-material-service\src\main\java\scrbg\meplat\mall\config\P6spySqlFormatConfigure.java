package scrbg.meplat.mall.config;

import cn.hutool.core.date.DateUtil;
import com.p6spy.engine.spy.appender.MessageFormattingStrategy;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;

public class P6spySqlFormatConfigure implements MessageFormattingStrategy {


    @Override
    public String formatMessage(int connectionId, String now, long elapsed, String category, String prepared, String sql, String url) {
        if(StringUtils.isNotBlank(sql)){
            return StringUtils.LF +  "--------------------------" +  DateUtil.formatLocalDateTime(LocalDateTime.now()) + " | 耗时 " + elapsed + " ms | SQL 语句：" + "--------------------------" + StringUtils.LF + sql.replaceAll("[\\s]+", StringUtils.SPACE);
        }else {
            return StringUtils.EMPTY;
        }
    }


}
