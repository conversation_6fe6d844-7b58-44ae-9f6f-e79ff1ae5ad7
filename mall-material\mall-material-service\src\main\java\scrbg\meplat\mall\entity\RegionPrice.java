package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@ApiModel(value = "商品区域价格")
@Data
@TableName("region_price")
public class RegionPrice  extends MustBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "商品id")
    private String productId;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "区域价格id")
    private String regionPriceId;

    @ApiModelProperty(value = "区域名称")
    private String regionName;

    @ApiModelProperty(value = "销售价格（含税）")
    private BigDecimal taxInPrice;

    @ApiModelProperty(value = "销售价格（不含税）")
    private BigDecimal price;

    @ApiModelProperty(value = "区域省市")
    private String area;

    @ApiModelProperty(value = "区域省市编码")
    private String areaCode;

    @ApiModelProperty(value = "自营店商品-账期")
    private Integer accountPeriod;

    @ApiModelProperty(value = "自营店商品-先款后货")
    private BigDecimal payBeforeDelivery;

    @ApiModelProperty(value = "年化率")
    private BigDecimal annualizedRate;

    @ApiModelProperty(value = "加成率")
    private BigDecimal markUpNum;

    @ApiModelProperty(value = "加成含税销售价")
    private BigDecimal bonusTaxInPrice;

    @ApiModelProperty(value = "加成不含税销售价")
    private BigDecimal bonusPrice;

    @TableField(exist = false)
    private List<String> detailAddress;

    @TableField(exist = false)
    private List<String> selectAddressOptionsAll;

    @TableField(exist = false)
    private List<List<String>> selectAddressOptions;
}
