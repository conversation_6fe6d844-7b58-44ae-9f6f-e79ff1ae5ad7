package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import scrbg.meplat.mall.entity.FileRecordRelated;
import scrbg.meplat.mall.service.FileRecordRelatedService;

@RestController
@RequestMapping("/file-record-related")
public class FileRecordRelatedController {

    @Autowired
    public FileRecordRelatedService fileRecordRelatedService;

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<FileRecordRelated> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = fileRecordRelatedService.queryPage(jsonObject, new LambdaQueryWrapper<FileRecordRelated>());
        return PageR.success(page);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody FileRecordRelated recordRelated) {
        fileRecordRelatedService.create(recordRelated);
        return R.success();
    }
}
