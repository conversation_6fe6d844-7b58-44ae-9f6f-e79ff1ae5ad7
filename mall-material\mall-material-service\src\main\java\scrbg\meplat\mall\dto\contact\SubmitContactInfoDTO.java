package scrbg.meplat.mall.dto.contact;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023-04-21 9:34
 */
@Data
public class SubmitContactInfoDTO {

    @ApiModelProperty(value = "供应商信用代码")

    private String bCretidCode;

    @ApiModelProperty(value = "合同id")

    private String billId;

    @ApiModelProperty(value = "合同名称")

    private String billName;

    @ApiModelProperty(value = "合同编号")

    private String billNo;

    @ApiModelProperty(value = "合同类型")

    private Integer type;


}
