package scrbg.meplat.mall.dto.product.material;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @create 2023-03-23 14:16
 */
@Data
public class ImportProductVO {

    @ApiModelProperty(value = "附件名称",required = true)
    @NotEmpty(message = "附件名称不能为空！")
    private String name;

    @ApiModelProperty(value = "附件路径（去除ip端口）",required = true)
    @NotEmpty(message = "附件路径不能为空！")
    private String url;
    @ApiModelProperty(value = "附件id(对应-》文件上传recordId)",required = true)
    @NotEmpty(message = "附件id不能为空！")
    private String fileFarId;



}
