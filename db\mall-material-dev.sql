/*
 Navicat Premium Data Transfer

 Source Server         : ************
 Source Server Type    : MySQL
 Source Server Version : 80025
 Source Host           : ************:3306
 Source Schema         : mall-material

 Target Server Type    : MySQL
 Target Server Version : 80025
 File Encoding         : 65001

 Date: 15/04/2025 14:06:09
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for account
-- ----------------------------
DROP TABLE IF EXISTS `account`;
CREATE TABLE `account`  (
                            `bill_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '单据ID',
                            `bill_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '单据编号',
                            `bill_date` datetime NULL DEFAULT NULL COMMENT '对账日期',
                            `work_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '流程ID',
                            `state` int NULL DEFAULT NULL COMMENT '状态',
                            `org_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '机构',
                            `org_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '机构ID',
                            `storage_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商',
                            `storage_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商ID',
                            `source_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '源单编号',
                            `source_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '源单Id',
                            `source_date` datetime NULL DEFAULT NULL COMMENT '源单日期',
                            `source_source_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '源单的源单编号',
                            `source_source_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT ' 源单的源单Id',
                            `picker` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商经办人',
                            `pickerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商经办人Id',
                            `receiver_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '收料/退库人员Id',
                            `receiver_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '收料/退库人员名称',
                            `totala_mount` decimal(10, 2) NULL DEFAULT NULL COMMENT '金额合计',
                            `remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
                            `recorder_time` datetime NULL DEFAULT NULL COMMENT '录入时间',
                            `recorder_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '录入人',
                            `recorder` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '录入人ID',
                            `last_modifier` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT ' 最后修改人',
                            `last_modifier_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '最后修改人ID',
                            `last_modify_time` datetime NULL DEFAULT NULL COMMENT '最后修改时间',
                            `last_auditor_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '最后审核人ID',
                            `last_auditor` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '最后审核人',
                            `last_audit_time` datetime NULL DEFAULT NULL COMMENT '最后审核时间',
                            `credit_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商社会信用代码',
                            `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                            `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                            PRIMARY KEY (`bill_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'pcwp1对账单' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for account_dtl
-- ----------------------------
DROP TABLE IF EXISTS `account_dtl`;
CREATE TABLE `account_dtl`  (
                                `dtl_id` varchar(233) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '设置明细ID',
                                `bill_id` varchar(233) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '设置单据ID',
                                `item_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '设置物料名称',
                                `item_Id` varchar(233) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '设置物料ID',
                                `item_model` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '规格',
                                `item_unit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '单位',
                                `cai_zhi` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '设置材质',
                                `qty` decimal(10, 4) NULL DEFAULT NULL COMMENT '数量',
                                `listing_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '挂牌价',
                                `floating_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '下浮费用',
                                `fixed_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '固定价',
                                `price` decimal(10, 2) NULL DEFAULT NULL COMMENT '合计单价',
                                `amount` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '合计金额',
                                `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '卸货地址',
                                `item_classId` varchar(233) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '类别ID',
                                `item_className` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '类别名称',
                                `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                PRIMARY KEY (`dtl_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'pcwp1对账单明细' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for account_statement
-- ----------------------------
DROP TABLE IF EXISTS `account_statement`;
CREATE TABLE `account_statement`  (
                                      `bill_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '对账单id',
                                      `bill_no` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '对账单编号',
                                      `org_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '所属机构id',
                                      `org_name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '所属机构名称',
                                      `site_receiving_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '收料单id',
                                      `site_receiving_name` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '收料单编号',
                                      `receiving_personnel_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '收料人员id',
                                      `receiving_personnel_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '收料人员名称',
                                      `supplier_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商id',
                                      `supplier_name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商名称',
                                      `personnel_information_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商经办人id',
                                      `personnel_information_name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商经办人名称',
                                      `total_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '金额合计',
                                      `account_date` datetime NULL DEFAULT NULL COMMENT '对账日期',
                                      `state` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '状态（0：已发布，1.作废）',
                                      `remarks` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
                                      `source_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '源单id',
                                      `source_number` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '源单编号',
                                      `work_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '流程id',
                                      `supplier_return_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '退货单id',
                                      `supplier_return_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '退货单编号',
                                      `warehouse_keeper_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '库管人员id',
                                      `warehouse_keeper_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '库管人员名称',
                                      `generation_date` datetime NULL DEFAULT NULL COMMENT '退货/收料日期',
                                      `account_source` char(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '对账单来源（1.收料 2.退货）',
                                      `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                      `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                      `credit_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '统一社会信用代码',
                                      `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                      `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                      PRIMARY KEY (`bill_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'pcwp2对账单' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for account_statement_dtl
-- ----------------------------
DROP TABLE IF EXISTS `account_statement_dtl`;
CREATE TABLE `account_statement_dtl`  (
                                          `dtl_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '对账单明细id',
                                          `bill_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '对账单id',
                                          `material_class_id` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物资类别id(1级类别id/2级类别id/..)',
                                          `material_class_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物资类别名称(1级类别名称/2级类别名称/..)',
                                          `spec` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '规格型号',
                                          `texture` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '材质',
                                          `unit` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '计量单位',
                                          `quantity` decimal(18, 4) NULL DEFAULT NULL COMMENT '收料数量',
                                          `listing_price` decimal(18, 4) NULL DEFAULT NULL COMMENT '挂牌价',
                                          `floating_cost` decimal(18, 4) NULL DEFAULT NULL COMMENT '下浮费用',
                                          `fixed_expenses` decimal(18, 4) NULL DEFAULT NULL COMMENT '固定费',
                                          `total_unit_price` decimal(18, 4) NULL DEFAULT NULL COMMENT '合计单价',
                                          `total_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '合计金额',
                                          `unloading_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '卸货地址',
                                          `material_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物资id',
                                          `material_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物资名称',
                                          `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                          `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                          PRIMARY KEY (`dtl_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '对账单明细' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ad_picture
-- ----------------------------
DROP TABLE IF EXISTS `ad_picture`;
CREATE TABLE `ad_picture`  (
                               `picture_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '图片id',
                               `picture_url_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '图片记录id',
                               `picture_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '存放地址',
                               `picture_type` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '链接类型（1：无  2：内部链接地址 3：外部链接地址）',
                               `picture_link_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '点击图片链接地址',
                               `use_type` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '面显示位置（1，平台首页，2，商城首页',
                               `sort` int NULL DEFAULT NULL COMMENT '排序',
                               `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                               `gmt_modified` datetime NULL DEFAULT NULL COMMENT '修改时间',
                               `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人id',
                               `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                               `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '图片备注信息',
                               `state` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '状态（1：发布  2：未发布）',
                               `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城',
                               `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                               `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                               `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                               PRIMARY KEY (`picture_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '广告图片' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ask_answer
-- ----------------------------
DROP TABLE IF EXISTS `ask_answer`;
CREATE TABLE `ask_answer`  (
                               `ask_answer_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '问答id',
                               `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '标题',
                               `type` tinyint NULL DEFAULT NULL COMMENT '信息类型（0求购1求租2招标）',
                               `send_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '发件人Id',
                               `send_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '发件人名称',
                               `send_type` tinyint NULL DEFAULT NULL COMMENT '发件人类型（0店铺1用户2平台）',
                               `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '内容',
                               `relevance_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '关联id',
                               `send_date` datetime NULL DEFAULT NULL COMMENT '发件时间',
                               `accept_ids` tinyint NULL DEFAULT NULL COMMENT '收件人ids（逗号分割）',
                               `state` tinyint NULL DEFAULT NULL COMMENT '状态（0显示1不显示）默认显示',
                               `sort` int NULL DEFAULT NULL COMMENT '排序',
                               `is_delete` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                               `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                               `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                               `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                               `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                               `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                               `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                               `is_file` tinyint NULL DEFAULT NULL COMMENT '是否附件（0否1是）',
                               `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                               `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                               PRIMARY KEY (`ask_answer_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '问答' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for audit_record
-- ----------------------------
DROP TABLE IF EXISTS `audit_record`;
CREATE TABLE `audit_record`  (
                                 `audit_record_id` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '审核id',
                                 `relevance_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '关联id',
                                 `relevance_type` tinyint NOT NULL COMMENT '关联类型（1月供计划2月供变更计划）',
                                 `audit_type` tinyint NOT NULL COMMENT '审核类型（1普通审核2变更审核）',
                                 `result_type` tinyint NOT NULL COMMENT '结果类型（1通过2未通过）',
                                 `audit_result` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '审核结果',
                                 `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                 `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                 `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                 `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                 `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                 `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                                 `sort` int NULL DEFAULT NULL COMMENT '排序',
                                 `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                                 `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                 `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                 PRIMARY KEY (`audit_record_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '审核记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for bidding_bid_record
-- ----------------------------
DROP TABLE IF EXISTS `bidding_bid_record`;
CREATE TABLE `bidding_bid_record`  (
                                       `bid_record_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '竞价记录id',
                                       `bid_record_sn` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '竞价记录编号',
                                       `bidding_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '竞价采购id',
                                       `bidding_sn` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '竞价采购编号',
                                       `contact_phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系电话',
                                       `contact_person` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系人',
                                       `supplier_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商id（本地）',
                                       `supplier_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '供应商名称（本地）',
                                       `bid_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '不含税总金额',
                                       `bid_rate_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '含税总金额',
                                       `bid_time` datetime NULL DEFAULT NULL COMMENT '竞价时间',
                                       `reject_reason` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '驳回原因',
                                       `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间	',
                                       `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间	',
                                       `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                       `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                       `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '竞价方备注',
                                       `sort` int NULL DEFAULT NULL COMMENT '排序',
                                       `state` tinyint NULL DEFAULT 0 COMMENT '状态(0默认1已提交5中标待审核6中标审核通过7审核失败)',
                                       `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型',
                                       `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除：（ -1: 删除  0:未删除）',
                                       `submit_date` datetime NULL DEFAULT NULL COMMENT '提交时间',
                                       `bill_type` tinyint NULL DEFAULT NULL COMMENT '价格类型（1浮动价格2固定价格）大宗临购使用',
                                       `tax_rate` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '税率',
                                       `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                       `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                       PRIMARY KEY (`bid_record_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '竞价记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for bidding_bid_record_item
-- ----------------------------
DROP TABLE IF EXISTS `bidding_bid_record_item`;
CREATE TABLE `bidding_bid_record_item`  (
                                            `bid_record_item_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '竞价记录明细id',
                                            `bid_record_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '竞价记录id',
                                            `bidding_product_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '竞价采购商品id',
                                            `bidding_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '竞价采购id',
                                            `bid_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '不含税到场单价',
                                            `tax_rate` decimal(10, 2) NULL DEFAULT NULL COMMENT '税率',
                                            `bid_rate_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '含税到场单价',
                                            `bid_rate_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '含税总金额',
                                            `bid_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '不含税总金额',
                                            `bid_time` datetime NULL DEFAULT NULL COMMENT '竞价时间',
                                            `reject_reason` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '驳回原因',
                                            `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间	',
                                            `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间	',
                                            `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                            `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                            `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '竞价人备注',
                                            `sort` int NULL DEFAULT NULL COMMENT '排序',
                                            `state` tinyint NULL DEFAULT NULL COMMENT '状态',
                                            `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型',
                                            `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除：（ -1: 删除  0:未删除）',
                                            `net_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '网价（浮动价格使用）',
                                            `fixation_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '固定费用（浮动价格使用）',
                                            `out_factory_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '出厂价（固定价格使用）',
                                            `transport_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '运杂费（固定价格使用）',
                                            `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                            `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                            PRIMARY KEY (`bid_record_item_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '竞价记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for bidding_invitation_relevance
-- ----------------------------
DROP TABLE IF EXISTS `bidding_invitation_relevance`;
CREATE TABLE `bidding_invitation_relevance`  (
                                                 `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间	',
                                                 `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间	',
                                                 `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                                 `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                                 `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                                 `sort` int NULL DEFAULT NULL COMMENT '排序',
                                                 `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：（ 0物资商场  1设备商城 ）',
                                                 `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除：（ -1: 删除  0:未删除）',
                                                 `bid_invitation_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '竞价邀请ID',
                                                 `bidding_sn` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '竞价采购编号',
                                                 `supplier_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商id',
                                                 `supplier_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商名称',
                                                 `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                                 `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                                 PRIMARY KEY (`bid_invitation_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '邀请竞价关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for bidding_product
-- ----------------------------
DROP TABLE IF EXISTS `bidding_product`;
CREATE TABLE `bidding_product`  (
                                    `bidding_product_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '竞价采购商品id',
                                    `bidding_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '竞价采购id',
                                    `bidding_sn` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '竞价采购编号',
                                    `order_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '订单id',
                                    `order_sn` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '订单编号',
                                    `order_item_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '订单明细id',
                                    `product_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '商品名称',
                                    `brand` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '品牌名称',
                                    `spec` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '规格型号',
                                    `unit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '计量单位',
                                    `product_texture` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '商品材质',
                                    `delivery_date` datetime NULL DEFAULT NULL COMMENT '送货时间',
                                    `delivery_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '送货地址',
                                    `num` decimal(10, 4) NULL DEFAULT NULL COMMENT '数量',
                                    `reference_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '参考单价（最高价）',
                                    `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间	',
                                    `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间	',
                                    `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                    `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                    `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                    `sort` int NULL DEFAULT NULL COMMENT '排序',
                                    `state` tinyint NULL DEFAULT NULL COMMENT '状态',
                                    `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型',
                                    `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除：（ -1: 删除  0:未删除）',
                                    `class_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '分类id',
                                    `class_path_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '分类路径名称（xxx/xxxx/xxx）',
                                    `create_org_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建机构id',
                                    `create_org_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建机构名称',
                                    `product_sn` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '商品编号',
                                    `synthesize_temporary_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '大宗临采单id',
                                    `synthesize_temporary_sn` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '大宗临采单编号',
                                    `synthesize_temporary_dtl_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '大宗临采单明细id',
                                    `net_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '网价（浮动价格使用）',
                                    `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                    `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                    PRIMARY KEY (`bidding_product_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '竞价商品信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for bidding_purchase
-- ----------------------------
DROP TABLE IF EXISTS `bidding_purchase`;
CREATE TABLE `bidding_purchase`  (
                                     `bidding_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '竞价采购id',
                                     `bidding_sn` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '竞价采购编号',
                                     `bidding_source_type` tinyint NULL DEFAULT NULL COMMENT '竞价来源类型（1订单2商品）',
                                     `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '标题',
                                     `type` tinyint NULL DEFAULT NULL COMMENT '竞价采购类型（1公开竞价2邀请竞价）暂时废弃',
                                     `start_time` datetime NULL DEFAULT NULL COMMENT '发布时间',
                                     `end_time` datetime NULL DEFAULT NULL COMMENT '截止时间',
                                     `link_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系人名称',
                                     `link_phone` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系电话',
                                     `bidding_explain` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '竞价说明',
                                     `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间	',
                                     `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间	',
                                     `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                     `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                     `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                     `sort` int NULL DEFAULT NULL COMMENT '排序',
                                     `state` tinyint NULL DEFAULT 0 COMMENT '状态（0待提交1待审核2审核失败5审核通过6中标待审核7已中标8中标审核失败9已流标',
                                     `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型',
                                     `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除：（ -1: 删除  0:未删除）',
                                     `shop_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '店铺id',
                                     `bidding_state` tinyint NULL DEFAULT 1 COMMENT '单据时间状态（1未开始2进行中3已结束）',
                                     `publicity_state` tinyint NULL DEFAULT 0 COMMENT '公示类型（0未发布1已发布）',
                                     `create_org_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建机构id',
                                     `create_org_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建机构名称',
                                     `is_hit_bidding` tinyint NULL DEFAULT 0 COMMENT '是否中标（1是0否）',
                                     `product_type` tinyint NOT NULL DEFAULT 0 COMMENT '商品类型 0 低值易耗品 1大宗临购  ',
                                     `bill_type` tinyint NULL DEFAULT NULL COMMENT '价格类型（1浮动价格2固定价格）大宗临购使用',
                                     `bidding_notice` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '竞价函说明',
                                     `synthesize_temporary_sn` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '大宗临采单清单编号',
                                     `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                     `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                     PRIMARY KEY (`bidding_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '竞价采购表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for bidding_suppliers
-- ----------------------------
DROP TABLE IF EXISTS `bidding_suppliers`;
CREATE TABLE `bidding_suppliers`  (
                                      `bidding_suppliers_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '竞价供应商id',
                                      `bidding_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '竞价采购id',
                                      `bidding_sn` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '竞价采购编号',
                                      `supplier_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商id',
                                      `supplier_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商名称',
                                      `contact_person` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系人',
                                      `contact_phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系电话',
                                      `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间	',
                                      `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间	',
                                      `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                      `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                      `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                      `sort` int NULL DEFAULT NULL COMMENT '排序',
                                      `state` tinyint NULL DEFAULT NULL COMMENT '状态',
                                      `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型',
                                      `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '逻辑删除：（ -1: 删除  0:未删除）',
                                      `type` tinyint NULL DEFAULT NULL COMMENT '类型（1报名2邀请）',
                                      `apply_affirm_time` datetime NULL DEFAULT NULL COMMENT '报名或邀请确认时间',
                                      `is_biding` tinyint NULL DEFAULT 0 COMMENT '是否竞价（1是0否）',
                                      `biding_count` int NULL DEFAULT NULL COMMENT '竞价次数',
                                      `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                      `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                      PRIMARY KEY (`bidding_suppliers_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for bidding_win_record
-- ----------------------------
DROP TABLE IF EXISTS `bidding_win_record`;
CREATE TABLE `bidding_win_record`  (
                                       `win_record_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '竞价中标记录id',
                                       `bid_record_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '竞价记录id',
                                       `bidding_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '竞价采购id',
                                       `supplier_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '供应商id',
                                       `supplier_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '供应商名称',
                                       `bid_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '不含税总金额',
                                       `bid_rate_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '含税总金额',
                                       `win_time` datetime NULL DEFAULT NULL COMMENT '中标时间',
                                       `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间	',
                                       `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间	',
                                       `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                       `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                       `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                       `sort` int NULL DEFAULT NULL COMMENT '排序',
                                       `state` tinyint NULL DEFAULT NULL COMMENT '状态',
                                       `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型',
                                       `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除：（ -1: 删除  0:未删除）',
                                       `reject_reason` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '驳回原因',
                                       `create_org_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建机构id',
                                       `create_org_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建机构名称',
                                       `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                       `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                       PRIMARY KEY (`win_record_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '竞价中标记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for brand
-- ----------------------------
DROP TABLE IF EXISTS `brand`;
CREATE TABLE `brand`  (
                          `brand_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '品牌id',
                          `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '品牌名',
                          `logo` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '品牌logo地址',
                          `descript` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '介绍',
                          `state` tinyint NOT NULL DEFAULT 0 COMMENT '显示状态[0-不显示；1-显示]',
                          `sort` int NULL DEFAULT NULL COMMENT '排序',
                          `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                          `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                          `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                          `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                          `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                          `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                          `product_type` tinyint NULL DEFAULT NULL COMMENT '商品类型：0物资 1设备 2周材（物资） 3周材（设备） 4二手设备 5租赁设备',
                          `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                          `class_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分类id',
                          `class_name` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分类名称',
                          `class_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分类路径',
                          `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                          `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                          PRIMARY KEY (`brand_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '品牌' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for category_brand_relation
-- ----------------------------
DROP TABLE IF EXISTS `category_brand_relation`;
CREATE TABLE `category_brand_relation`  (
                                            `category_brand_relation_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '关联id',
                                            `brand_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '品牌id',
                                            `class_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分类id',
                                            `brand_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '品牌名称',
                                            `class_name` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分类名称',
                                            `is_delete` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                                            `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                            `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                            `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                            `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                            `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                            `sort` int NULL DEFAULT NULL COMMENT '排序',
                                            `state` tinyint NULL DEFAULT NULL COMMENT '状态',
                                            `product_type` tinyint NULL DEFAULT NULL COMMENT '商品类型：0物资 1设备 2周材（物资） 3周材（设备） 4二手设备 5租赁设备 ',
                                            `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                                            `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                            `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                            PRIMARY KEY (`category_brand_relation_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '品牌分类关联' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for category_columns
-- ----------------------------
DROP TABLE IF EXISTS `category_columns`;
CREATE TABLE `category_columns`  (
                                     `column_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '栏目id',
                                     `column_number` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '栏目编号',
                                     `column_name` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '栏目名称',
                                     `sort` int NULL DEFAULT NULL COMMENT '排序值',
                                     `state` tinyint NULL DEFAULT NULL COMMENT '栏目状态（1：启用 0：停用）',
                                     `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                     `gmt_modified` datetime NULL DEFAULT NULL COMMENT '修改时间',
                                     `gmt_release` datetime NULL DEFAULT NULL COMMENT '发布时间',
                                     `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人id',
                                     `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                     `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注信息',
                                     `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除（-1：删除  0：未删除）',
                                     `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型（0：物资 1：装备）',
                                     `is_fixed` tinyint NULL DEFAULT NULL COMMENT '是否固定（1：固定  0不固定）',
                                     `code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '编码',
                                     `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                     `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                     PRIMARY KEY (`column_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for column_floor
-- ----------------------------
DROP TABLE IF EXISTS `column_floor`;
CREATE TABLE `column_floor`  (
                                 `id` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '栏目-楼层id',
                                 `column_id` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '栏目id',
                                 `floor` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '楼层id',
                                 `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                 `sort` int NULL DEFAULT NULL COMMENT '排序',
                                 `state` tinyint NULL DEFAULT NULL COMMENT '楼层状态（1：显示  0：不显示）',
                                 `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                                 `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                 `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                 `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人id',
                                 `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                 `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                                 `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                 `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for content
-- ----------------------------
DROP TABLE IF EXISTS `content`;
CREATE TABLE `content`  (
                            `content_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '内容id',
                            `file_name` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '文件名',
                            `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '内容',
                            `source` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '源码',
                            `state` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '状态',
                            `comments` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '评论',
                            `sort` int NULL DEFAULT NULL COMMENT '排序',
                            `default_picture_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '默认图片路径',
                            `big_picture_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '大图片路径',
                            `small_picture_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '小图片路径',
                            `tiny_picture_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '微小图片路径',
                            `home` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '主页',
                            `top` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '1' COMMENT '顶部',
                            `title` varchar(400) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '标题',
                            `info_type` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '信息类型',
                            `subtitle` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '副标题',
                            `author` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '作者',
                            `file_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '文件路径',
                            `home_picture_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '主图片路径',
                            `view_count` int NULL DEFAULT NULL COMMENT '浏览次数',
                            `programa_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '栏目id',
                            `programa_key` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '栏目Key',
                            `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                            `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                            `gmt_release` datetime NULL DEFAULT NULL COMMENT '发布时间',
                            `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                            `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                            `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                            `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                            `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                            `summary` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '概述',
                            `agreement_type` varchar(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '协议类型（1.个人，2个体户，3企业）',
                            `banner_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '新闻显示图',
                            `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                            `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                            PRIMARY KEY (`content_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '内容' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for content_catalogue
-- ----------------------------
DROP TABLE IF EXISTS `content_catalogue`;
CREATE TABLE `content_catalogue`  (
                                      `catalogue_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '内容目录id',
                                      `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '名称',
                                      `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '类型',
                                      `state` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '状态',
                                      `comments` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '注解',
                                      `sort` int NULL DEFAULT NULL COMMENT '排序',
                                      `programa_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '程序代码',
                                      `Is_home` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '是否为主页',
                                      `Is_navigation` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '是否为导航',
                                      `page_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '页面地址',
                                      `parent_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '父页id',
                                      `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                      `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                      `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                      `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                      `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                                      `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                      `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                                      `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                      `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                      PRIMARY KEY (`catalogue_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '内容目录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for contract
-- ----------------------------
DROP TABLE IF EXISTS `contract`;
CREATE TABLE `contract`  (
                             `contract_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '合同id',
                             `contract_no` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '合同编号',
                             `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '合同名称',
                             `type` tinyint NULL DEFAULT NULL COMMENT '合同类型（1入驻合同）',
                             `party_a_org_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '甲方机构id',
                             `party_a_org_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '甲方机构名称',
                             `party_b_org_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '乙方机构id',
                             `party_b_org_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '乙方机构名称',
                             `sort` int NULL DEFAULT NULL COMMENT '排序',
                             `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                             `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                             `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                             `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                             `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                             `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                             `state` tinyint NULL DEFAULT NULL COMMENT '状态',
                             `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                             `contract_json` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '合同JSON完整数据',
                             `contract_year` int NULL DEFAULT NULL COMMENT '合同签订年份',
                             `contract_year_index` int(4) UNSIGNED ZEROFILL NULL DEFAULT NULL COMMENT '当年合同签订序号',
                             PRIMARY KEY (`contract_id`) USING BTREE,
                             UNIQUE INDEX `uk_contract_no`(`contract_no` ASC) USING BTREE COMMENT '合同编号唯一索引'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '合同' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for deal_order_info
-- ----------------------------
DROP TABLE IF EXISTS `deal_order_info`;
CREATE TABLE `deal_order_info`  (
                                    `deal_order_info_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '结算id',
                                    `shop_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '店铺id',
                                    `shop_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '店铺名称',
                                    `buy_org_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '买方本机机构id',
                                    `bug_org_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '买方机构名称',
                                    `supplier_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商id',
                                    `supplier_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商I名称',
                                    `order_sn` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '订单编号',
                                    `order_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '订单id',
                                    `product_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '商品id',
                                    `product_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '商品名称',
                                    `number` decimal(18, 4) NULL DEFAULT NULL COMMENT '交易数量',
                                    `amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '交易金额',
                                    `cost_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '交易成本价',
                                    `finish_date` datetime NULL DEFAULT NULL COMMENT '交易完成时间',
                                    `sku_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '规格名称',
                                    `unit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '计量单位',
                                    `deal_type` tinyint NULL DEFAULT NULL COMMENT '结算类型（1零星采购）',
                                    `order_finish_date` datetime NULL DEFAULT NULL COMMENT '订单完成时间',
                                    `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间	',
                                    `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间	',
                                    `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                    `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                    `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                    `sort` int NULL DEFAULT NULL COMMENT '排序',
                                    `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：（ 0物资商场  1设备商城 ）',
                                    `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '逻辑删除：（ -1: 删除  0:未删除）',
                                    `settle_accounts_id` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '结算id',
                                    `reconciliation_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '对账单总金额',
                                    `reconciliation_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '对账单id',
                                    `tax_rate` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '税率',
                                    `price` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '单价',
                                    `net_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '网价（浮动价格使用）',
                                    `fixation_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '固定费用（浮动价格使用）',
                                    `out_factory_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '出厂价（固定价格使用）',
                                    `transport_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '运杂费（固定价格使用）',
                                    `bill_type` tinyint NULL DEFAULT NULL COMMENT '清单类型（1浮动价格2固定价格）',
                                    `business_type` tinyint NULL DEFAULT NULL COMMENT '业务类型（1合同2计划3调拨4甲供5暂估6大宗临采）',
                                    `no_rate_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '不含税单价',
                                    `no_rate_amount` decimal(10, 0) NULL DEFAULT NULL COMMENT '不含税金额',
                                    `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                    `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                    PRIMARY KEY (`deal_order_info_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '结算订单明细' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for deposit_bank
-- ----------------------------
DROP TABLE IF EXISTS `deposit_bank`;
CREATE TABLE `deposit_bank`  (
                                 `bank_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '开户银行id',
                                 `bank_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '银行名称',
                                 `bank_count` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '银行账号',
                                 `phone` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系电话',
                                 `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                 `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                 `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                 `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                 `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                 `enterprise_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '企业id',
                                 `is_default` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '是否是该企业默认银行账户 1: 是 0:否',
                                 `is_delete` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                                 `state` tinyint NULL DEFAULT NULL COMMENT '状态',
                                 `sort` int NULL DEFAULT NULL COMMENT '排序',
                                 `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                                 `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                 `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                 PRIMARY KEY (`bank_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '开户银行信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for device_demand
-- ----------------------------
DROP TABLE IF EXISTS `device_demand`;
CREATE TABLE `device_demand`  (
                                  `demand_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '需求id',
                                  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '名称',
                                  `spec` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '规格型号',
                                  `keyword` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '关键字',
                                  `relevance_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '关联id（根据发布类型存储不同id）',
                                  `class_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分类id',
                                  `class_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分类名称',
                                  `brand_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '品牌id',
                                  `brand_name` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '品牌名称',
                                  `province` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '省份',
                                  `city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '城市',
                                  `county` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '县区',
                                  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '地址',
                                  `longitude` decimal(11, 7) NULL DEFAULT NULL COMMENT '经度',
                                  `latitude` decimal(11, 7) NULL DEFAULT NULL COMMENT '纬度',
                                  `num` int NULL DEFAULT NULL COMMENT '数量',
                                  `demand_type` tinyint NULL DEFAULT NULL COMMENT '需求类型（1租赁设备2二手设备）',
                                  `num_unit` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '计量单位',
                                  `duration` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '租赁时长',
                                  `enter_date` datetime NULL DEFAULT NULL COMMENT '进场时间',
                                  `linkman` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系人',
                                  `linkman_phone` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系电话',
                                  `release_date` datetime NULL DEFAULT NULL COMMENT '发布时间',
                                  `stop_date` datetime NULL DEFAULT NULL COMMENT '截止时间',
                                  `budget_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '预算金额',
                                  `sort` int NULL DEFAULT NULL COMMENT '排序',
                                  `state` tinyint NULL DEFAULT NULL COMMENT '需求状态（0待审核1已审核2询价中3报价中）',
                                  `check_state` tinyint NULL DEFAULT NULL COMMENT '审核状态（0待审核1通过2不通过）',
                                  `is_delete` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                                  `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                  `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                  `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                                  `release_type` tinyint NULL DEFAULT NULL COMMENT '发布类型（0店铺1用户2平台）',
                                  `class_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分类路径（/分割）',
                                  `fail_reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '审核失败原因',
                                  `launch` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '发起方(企业)',
                                  `start_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '出发地',
                                  `end_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '目的地',
                                  `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                  `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                  PRIMARY KEY (`demand_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '设备需求' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for enterprise_info
-- ----------------------------
DROP TABLE IF EXISTS `enterprise_info`;
CREATE TABLE `enterprise_info`  (
                                    `enterprise_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '企业id',
                                    `enterprise_number` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '企业编号',
                                    `enterprise_name` varchar(125) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '企业名称（公司名称、供应商公司名称 ）',
                                    `social_credit_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '统一社会信用代码',
                                    `legal_representative` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '法定代表人',
                                    `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '企业邮箱',
                                    `operator` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '经营者',
                                    `creation_time` datetime NULL DEFAULT NULL COMMENT '注册日期',
                                    `place_of_business` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '经营场所',
                                    `license_term` datetime NULL DEFAULT NULL COMMENT '营业执照有效期',
                                    `registered_capital` decimal(18, 2) NULL DEFAULT NULL COMMENT '注册资本(万)',
                                    `provinces` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '注册省份',
                                    `city` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '注册市级',
                                    `county` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '注册县、区',
                                    `detailed_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '注册详细地址',
                                    `main_business` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '经营范围',
                                    `business_license` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '营业执照（地址）',
                                    `business_license_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '营业执照（记录id）',
                                    `card_portrait_face_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '身份证人像面(记录id)',
                                    `card_portrait_face` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '身份证人像面(存地址)',
                                    `card_portrait_national_emblem_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '身份证国徽面（记录id）',
                                    `card_portrait_national_emblem` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '身份证国徽面（存地址）',
                                    `admin_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '管理员姓名',
                                    `admin_phone` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '管理员电话',
                                    `admin_number` varchar(125) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '管理员身份证号',
                                    `income_call_time` datetime NULL DEFAULT NULL COMMENT '来电时间',
                                    `income_call_content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '来电内容',
                                    `processing_departnment` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '处理部门',
                                    `processing_time` datetime NULL DEFAULT NULL COMMENT '处理时间',
                                    `handling_result` tinyint NULL DEFAULT NULL COMMENT '办理状态： 0:办理中 1:办理成功',
                                    `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                                    `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                    `gmt_modified` datetime NULL DEFAULT NULL COMMENT '修改时间',
                                    `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                    `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                    `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                    `sort` int NULL DEFAULT NULL COMMENT '排序',
                                    `state` tinyint NULL DEFAULT NULL COMMENT '状态 1：启用  0:停用',
                                    `is_supplier` tinyint NULL DEFAULT 0 COMMENT '是否为供应商：0 : 否 1 ：待定  2：是',
                                    `enterprise_type` tinyint NULL DEFAULT NULL COMMENT '企业类型：0：个体户  1：企业  2：个人',
                                    `enterprise_business_type` tinyint NULL DEFAULT NULL COMMENT '企业营业状态 ：0： 停业 1：营业',
                                    `mall_type` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '商城类型：0：物资商场, 1：设备商城 ',
                                    `interior_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '内部企业id',
                                    `is_material_mall` tinyint NOT NULL DEFAULT 0 COMMENT '是否是物资商城企业',
                                    `is_device_mall` tinyint NOT NULL DEFAULT 0 COMMENT '是否是装备商城企业',
                                    `is_no_supplier_audit` tinyint NOT NULL DEFAULT 0 COMMENT '是否未通过供应商审核',
                                    `import_type` tinyint NOT NULL DEFAULT 0 COMMENT '导入类型：1excel',
                                    `audit_fail_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '审核失败原因',
                                    `short_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '机构简码',
                                    `is_pcwp` tinyint NOT NULL DEFAULT 0 COMMENT '是否为pcwp入库供应商(1.是，0 否)',
                                    `admin_company` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '管理员公司',
                                    `admin_department` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '管理员单位',
                                    `admin_gender` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '管理员性别',
                                    `admin_user_sn` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '管理员编号',
                                    `admin_position` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '管理员职位',
                                    `tax_rate` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '税率',
                                    `arrearage` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '可欠费额度',
                                    `arrearage_date_num` int NULL DEFAULT NULL COMMENT '欠费过期时间时长',
                                    `arrearage_date_type` tinyint NULL DEFAULT NULL COMMENT '欠费过期时间类型（1天2月3年）',
                                    `shu_dao_flag` tinyint NULL DEFAULT 0 COMMENT '是否为蜀道企业(1是，0否)',
                                    `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                    `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                    `is_arrearage` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '是否欠费',
                                    `is_production` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '是否是生产商（0 是，1不是）',
                                    `is_trader` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '是否是贸易商（0 是，1不是）',
                                    `taxpayer_category` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '纳税人类别\'',
                                    `work_provinces` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '固定工作注册省份',
                                    `work_city` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '固定工作注册市级',
                                    `work_county` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '固定工作注册县、区',
                                    `work_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '工作详细地址',
                                    `enterprise_level` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '分类层级 1:一级大分类 2:二级分类 3:三级小分类',
                                    `parent_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '父层级id',
                                    `enterprise_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分类路径',
                                    `org_type` int NULL DEFAULT NULL COMMENT '机构类型(1:集团|2:分子公司|4:经理部|5:项目部|6:股份|7:事业部)',
                                    PRIMARY KEY (`enterprise_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '企业附加信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for error_info
-- ----------------------------
DROP TABLE IF EXISTS `error_info`;
CREATE TABLE `error_info`  (
                               `error_info_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '异常id',
                               `business_type` tinyint NOT NULL COMMENT '异常业务类型',
                               `error_rq_json` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '错误接口请求JSON',
                               `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                               `is_dispose` tinyint NOT NULL DEFAULT 0 COMMENT '是否已解决',
                               `method_name` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '错误方法名称',
                               `error_info` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '错误信息',
                               `user_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用户id',
                               `user_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用户名称',
                               `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                               `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                               PRIMARY KEY (`error_info_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '异常信息记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for file
-- ----------------------------
DROP TABLE IF EXISTS `file`;
CREATE TABLE `file`  (
                         `file_Id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '附件id',
                         `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '附件名称',
                         `relevance_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '关联id',
                         `relevance_type` tinyint NULL DEFAULT NULL COMMENT '关联类型（1商品2问答3消息4店铺,5内容6注册7平台消息）',
                         `url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '附件地址',
                         `sort` int NULL DEFAULT NULL COMMENT '排序',
                         `is_main` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否主图，1是0否',
                         `file_type` tinyint NULL DEFAULT NULL COMMENT '媒体类型 1:图片2视频3附件',
                         `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                         `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                         `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                         `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                         `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                         `remarks` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
                         `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                         `file_far_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '附件远程id',
                         `img_type` tinyint NOT NULL DEFAULT 0 COMMENT '图片类型（0普通图片1小图）',
                         `programa_key` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '栏目Key',
                         `programa_key_two` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '符栏目key',
                         `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                         `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                         PRIMARY KEY (`file_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '通用附件' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for file_record
-- ----------------------------
DROP TABLE IF EXISTS `file_record`;
CREATE TABLE `file_record`  (
                                `record_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '记录id',
                                `object_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '对象名称',
                                `object_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '对象路径',
                                `non_ip_object_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '不含IP/域名的对象路径',
                                `bucket_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '桶名称',
                                `object_size_kb` decimal(18, 2) NULL DEFAULT NULL COMMENT '对象大小kb',
                                `object_size_mb` decimal(18, 2) NULL DEFAULT NULL COMMENT '对象大小mb',
                                `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间	',
                                `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间	',
                                `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                `sort` int NULL DEFAULT NULL COMMENT '排序',
                                `state` tinyint NULL DEFAULT NULL COMMENT '状态',
                                `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型',
                                `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除：（ -1: 删除  0:未删除）',
                                `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                PRIMARY KEY (`record_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '文件上传记录信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for file_record_delete
-- ----------------------------
DROP TABLE IF EXISTS `file_record_delete`;
CREATE TABLE `file_record_delete`  (
                                       `file_record_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '记录id',
                                       `record_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '删除记录id',
                                       `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                       `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                       `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                       `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                       `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                       `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                       PRIMARY KEY (`file_record_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '附件删除表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for financial_products
-- ----------------------------
DROP TABLE IF EXISTS `financial_products`;
CREATE TABLE `financial_products`  (
                                       `financial_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '金融产品id',
                                       `financial_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '金融产品名称',
                                       `picture_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '图片路径',
                                       `products_type` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '产品类型',
                                       `total_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '订单总价格',
                                       `interest_rate` decimal(18, 2) NULL DEFAULT NULL COMMENT '产品利率',
                                       `financing_period` char(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '融资期限',
                                       `repayment_type` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '还款方式',
                                       `Lending_duration` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '放款时长',
                                       `application_materials` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '申请材料',
                                       `application_conditions` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '申请条件',
                                       `product_introduction` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '产品简介',
                                       `use_area` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '使用区域',
                                       `product_features` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '产品特色',
                                       `application_scenario` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '应用场景',
                                       `shop_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '店铺id',
                                       `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                       `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                       `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除： -1: 删除 0:未删除',
                                       `state` tinyint NULL DEFAULT NULL COMMENT '0:已发布 1:未发布',
                                       `sort` int NULL DEFAULT NULL COMMENT '排序',
                                       `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                       `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                       `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                       `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城  ',
                                       `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                       `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                       PRIMARY KEY (`financial_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '金融产品' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for floor
-- ----------------------------
DROP TABLE IF EXISTS `floor`;
CREATE TABLE `floor`  (
                          `floor_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '楼层id',
                          `column_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '栏目id',
                          `column_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '栏目名称',
                          `floor_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '楼层名称',
                          `floor_name_text` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '楼层名称后小字',
                          `img_url_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '主图id',
                          `img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '楼层主图链接',
                          `main_img_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '点击主图链接地址',
                          `floor_product_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '商品类别',
                          `use_page` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '使用页面（枚举类型）待讨论',
                          `gmt_release` datetime NULL DEFAULT NULL COMMENT '发布时间',
                          `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                          `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                          `state` tinyint NULL DEFAULT NULL COMMENT '楼层状态（1：显示  0：不显示）',
                          `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人id',
                          `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                          `sort` int NULL DEFAULT NULL COMMENT '排序',
                          `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                          `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                          `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                          `background_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '楼层背景图链接',
                          `class_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分类ID',
                          `class_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分类路径',
                          `class_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '楼层商品分类名称',
                          `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                          `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                          PRIMARY KEY (`floor_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '客户端商品展示楼层' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for floor_goods
-- ----------------------------
DROP TABLE IF EXISTS `floor_goods`;
CREATE TABLE `floor_goods`  (
                                `floor_goods_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '楼层商品id',
                                `order_value` int NULL DEFAULT NULL COMMENT '商品排序值',
                                `floor_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '显示楼层id',
                                `goods_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '显示的商品id',
                                `goods_picture_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '图片地址',
                                `gmt_release` datetime NULL DEFAULT NULL COMMENT '发布时间',
                                `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                `gmt_modified` datetime NULL DEFAULT NULL COMMENT '修改时间',
                                `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人id',
                                `founder_name` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                `state` tinyint NULL DEFAULT NULL COMMENT '商品状态（1：发布（显示）  0：未发布（不显示））',
                                `sort` int NULL DEFAULT NULL COMMENT '排序',
                                `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                                `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                                `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                PRIMARY KEY (`floor_goods_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '楼层显示的商品' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for import_suppler
-- ----------------------------
DROP TABLE IF EXISTS `import_suppler`;
CREATE TABLE `import_suppler`  (
                                   `import_suppler_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
                                   `enterprise_name` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商名称',
                                   `social_credit_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '统一社会信用代码',
                                   `legal_representative` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '法定代表人',
                                   `admin_phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系电话',
                                   `state` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '结果',
                                   `fail` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '失败原因',
                                   `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                   `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                   PRIMARY KEY (`import_suppler_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '导入供应商失败的结果' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for insurance_products
-- ----------------------------
DROP TABLE IF EXISTS `insurance_products`;
CREATE TABLE `insurance_products`  (
                                       `insurance_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '保险id',
                                       `describes` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
                                       `type` tinyint NULL DEFAULT NULL COMMENT '0:交强险 1:车损险 2:三者险',
                                       `details` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '详情',
                                       `company` tinyint NULL DEFAULT NULL COMMENT '0:阳光产险 1:长安保险 2:天安财险 3:亚太财险 4:国任保险 5:浙商保险 6:中国人寿保险 7:阳光信保',
                                       `shop_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '店铺id',
                                       `project_area` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '项目地区',
                                       `sort` tinyint NULL DEFAULT NULL COMMENT '排序',
                                       `state` tinyint NULL DEFAULT NULL COMMENT '状态:1发布 0未发布',
                                       `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '保险名称',
                                       `insure` tinyint NULL DEFAULT NULL COMMENT '0:已投 1:未投',
                                       `years` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '保险年限',
                                       `age` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '承保年龄',
                                       `occupation` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '承保职业',
                                       `notice` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '投保须知',
                                       `common_problem` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '常见问题',
                                       `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                       `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                       `gmt_release` datetime NULL DEFAULT NULL COMMENT '发布时间',
                                       `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                                       `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                       `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型（0物资1设备）',
                                       `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                       `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                       PRIMARY KEY (`insurance_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '保险产品' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for interface_logs
-- ----------------------------
DROP TABLE IF EXISTS `interface_logs`;
CREATE TABLE `interface_logs`  (
                                   `log_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '日志id',
                                   `secret_key` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '秘钥唯一key',
                                   `business_flag` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '业务标识（当多业务相同key，此字段用于区分开业务）(情况少)',
                                   `class_package` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '类路径',
                                   `method_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '请求方法',
                                   `log_type` tinyint NULL DEFAULT NULL COMMENT '日志类型（1请求远程2请求远程回滚3请求本地4请求本地回滚）',
                                   `update_before` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '修改前的数据（json）',
                                   `local_arguments` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '本地方法请求参数（json）',
                                   `far_arguments` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '请求远程接口参数（json）',
                                   `is_success` tinyint NULL DEFAULT NULL COMMENT '是否成功（0否1是）',
                                   `result` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '返回结果（可选）（json）',
                                   `error_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '错误信息（可选）',
                                   `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id	',
                                   `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                   `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                   `remarks` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注（记录描述信息）',
                                   `execute_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '执行时间',
                                   `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                   `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                   PRIMARY KEY (`log_id`) USING BTREE,
                                   INDEX `key_index`(`secret_key` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for invoice
-- ----------------------------
DROP TABLE IF EXISTS `invoice`;
CREATE TABLE `invoice`  (
                            `invoice_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '发票id',
                            `enterprise_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '项目部Id',
                            `enterprise_name` varchar(233) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '项目部名称',
                            `supplier_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商id',
                            `supplier_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商名称',
                            `invoice_state` tinyint NULL DEFAULT NULL COMMENT '0:已申请 1:已开票',
                            `invoice_type` tinyint NOT NULL COMMENT '0：增值税专用发票、1增值税普通发票',
                            `rise_type` tinyint NULL DEFAULT NULL COMMENT '0:个人 1:单位',
                            `user_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '收票人姓名',
                            `user_address` varchar(233) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '收票人地址',
                            `user_phone` varchar(233) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '收票人联系电话',
                            `email` varchar(233) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '收票人邮箱',
                            `company` varchar(233) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '单位名称',
                            `duty_paragraph` varchar(233) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '单位税号',
                            `register_address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '注册地址',
                            `register_phone` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '注册电话',
                            `bank` varchar(125) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '开户银行',
                            `bank_account` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '银行账号',
                            `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                            `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                            `gmt_modified` datetime NULL DEFAULT NULL COMMENT '修改时间',
                            `gmt_apply` datetime NULL DEFAULT NULL COMMENT '通过时间',
                            `gmt_adopt` datetime NULL DEFAULT NULL COMMENT '申请时间',
                            `Invoice_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '发票单号',
                            `founder_id` char(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                            `founder_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                            `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                            `sort` int NULL DEFAULT NULL COMMENT '排序',
                            `state` tinyint NULL DEFAULT NULL COMMENT '状态',
                            `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城  ',
                            `detail_addr` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '详细地址',
                            `invoice_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '发票抬头',
                            `total_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '含税金额',
                            `no_rate_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '不含税金额',
                            `tax_rate` decimal(18, 2) NULL DEFAULT NULL COMMENT '税率',
                            `rate_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '含税金额',
                            `fail_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
                            `invoice_category` tinyint(1) NOT NULL DEFAULT 0 COMMENT '发票种类 0普通发票 2红字发票',
                            `invoice_class` tinyint NOT NULL DEFAULT 1 COMMENT '1对账单 2二级对账单',
                            `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                            `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                            PRIMARY KEY (`invoice_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '发票' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for invoice_dtl
-- ----------------------------
DROP TABLE IF EXISTS `invoice_dtl`;
CREATE TABLE `invoice_dtl`  (
                                `invoice_dtl_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '发票项',
                                `invoice_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '发票id',
                                `invoice_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '发票编号',
                                `material_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '商品名称',
                                `reconciliation_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '对账单id',
                                `reconciliation_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '对账单no',
                                `reconciliation_dtl_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '对账单项id',
                                `acceptance_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '含税金额',
                                `no_rate_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '不含税金额',
                                `price` decimal(18, 2) NULL DEFAULT NULL COMMENT '含税单价',
                                `no_price` decimal(18, 2) NULL DEFAULT NULL COMMENT '不含税单价',
                                `spec` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '规格',
                                `texture` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '材质',
                                `quantity` decimal(18, 4) NOT NULL DEFAULT 0.0000 COMMENT '对账数量',
                                `unit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '单位',
                                `reconciliation_type` tinyint(1) NULL DEFAULT NULL COMMENT '对账类型',
                                `mall_type` tinyint NULL DEFAULT NULL,
                                `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                `gmt_modified` datetime NULL DEFAULT NULL COMMENT '修改时间',
                                `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                `sort` int NULL DEFAULT NULL COMMENT '排序',
                                `is_delete` int NULL DEFAULT NULL COMMENT '排序',
                                `tax_rate` decimal(5, 2) NULL DEFAULT NULL COMMENT '税率',
                                `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                PRIMARY KEY (`invoice_dtl_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for invoice_record
-- ----------------------------
DROP TABLE IF EXISTS `invoice_record`;
CREATE TABLE `invoice_record`  (
                                   `invoice_record_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '发票抬头id',
                                   `enterprise_id` char(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '采购单位id',
                                   `state` tinyint NOT NULL DEFAULT 0 COMMENT '0 未选择 1默认',
                                   `invoice_type` tinyint NOT NULL COMMENT '0：增值税专用发票、1增值税普通发票',
                                   `rise_type` tinyint NULL DEFAULT NULL COMMENT '0:个人 1:单位',
                                   `user_name` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '收票人姓名',
                                   `user_address` varchar(125) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '收票人地址',
                                   `user_phone` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '收票人联系电话',
                                   `email` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '收票人邮箱',
                                   `company` varchar(125) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '单位名称',
                                   `duty_paragraph` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '单位税号',
                                   `register_address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '注册地址',
                                   `register_phone` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '注册电话',
                                   `bank` varchar(125) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '开户银行',
                                   `bank_account` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '银行账号',
                                   `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                                   `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                   `gmt_modified` datetime NULL DEFAULT NULL COMMENT '修改时间',
                                   `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                   `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                   `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                   `sort` int NULL DEFAULT NULL COMMENT '排序',
                                   `user_province` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '省',
                                   `user_city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '市',
                                   `user_county` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '县、区',
                                   `shop_balance` decimal(18, 2) NULL DEFAULT NULL COMMENT '店铺余额',
                                   `province` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '省',
                                   `city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '市',
                                   `county` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '县、区',
                                   `mall_type` tinyint NULL DEFAULT NULL,
                                   `user_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '1供应商  0项目部',
                                   `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                   `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                   PRIMARY KEY (`invoice_record_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for lbb
-- ----------------------------
DROP TABLE IF EXISTS `lbb`;
CREATE TABLE `lbb`  (
                        `lbb` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                        `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                        `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id'
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for links
-- ----------------------------
DROP TABLE IF EXISTS `links`;
CREATE TABLE `links`  (
                          `link_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '链接id',
                          `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '链接名',
                          `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                          `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '链接url',
                          `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                          `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                          `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除 -1删除 0未删除',
                          `state` tinyint NULL DEFAULT NULL COMMENT '状态 1:发布 0:未发布',
                          `sort` int NULL DEFAULT NULL COMMENT '排序',
                          `display` tinyint NULL DEFAULT NULL COMMENT '链接显示 0:首页显示 1:首页未显示',
                          `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id	',
                          `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                          `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                          `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                          `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                          PRIMARY KEY (`link_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '友情链接' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for materail_reconciliation_log
-- ----------------------------
DROP TABLE IF EXISTS `materail_reconciliation_log`;
CREATE TABLE `materail_reconciliation_log`  (
                                                `log_id` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
                                                `reconciliation_id` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
                                                `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
                                                `out_key_id` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
                                                `is_delete` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
                                                PRIMARY KEY (`log_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '对账日志关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for material_master_plan
-- ----------------------------
DROP TABLE IF EXISTS `material_master_plan`;
CREATE TABLE `material_master_plan`  (
                                         `bill_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '物资总计划id',
                                         `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                         `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                         `founder_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                         `founder_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                         `work_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程id',
                                         `bill_no` char(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '计划编号',
                                         `plan_date` datetime NULL DEFAULT NULL COMMENT '计划日期',
                                         `project_plan_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '工程计划Id',
                                         `amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '计划金额(元)',
                                         `tax_rate` decimal(18, 4) NULL DEFAULT NULL COMMENT '税率',
                                         `tax_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '税额',
                                         `total_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '税价合计',
                                         `preparer_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '填报人Id',
                                         `preparer` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '填报人名称',
                                         `currency_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '币种Id',
                                         `currency` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '币种名称',
                                         `purchase_type` int NULL DEFAULT NULL COMMENT '采购方式',
                                         `org_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '单据机构Id',
                                         `org_name` varchar(250) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '单据机构名称',
                                         `remarks` varchar(250) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注信息',
                                         `base_cur_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '本位币id',
                                         `base_cur_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '本位币名称',
                                         `base_cur_rate` decimal(18, 4) NULL DEFAULT NULL COMMENT '本位币汇率',
                                         `base_cur_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '计划金额(本位币)',
                                         `base_cur_tax_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '税额(本位币)',
                                         `base_cur_total_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '税价合计(本位币)c',
                                         `rmb_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '人民币id',
                                         `rmb_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '人民币名称',
                                         `rmb_rate` decimal(18, 4) NULL DEFAULT NULL COMMENT '人民币汇率',
                                         `rmb_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '计划金额(人民币)',
                                         `rmb_tax_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '税额(人民币)',
                                         `rmb_total_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '税价合计(人民币)',
                                         `order_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '订单id',
                                         `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                                         `product_type` tinyint NULL DEFAULT NULL COMMENT '商品类型：0物资 1设备 2周材（物资） 3周材（设备）',
                                         `sort` int NULL DEFAULT NULL COMMENT '排序',
                                         `state` tinyint NULL DEFAULT NULL COMMENT '状态',
                                         `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                                         `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                         `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                         PRIMARY KEY (`bill_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '物资总计划' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for material_master_plan_dtl
-- ----------------------------
DROP TABLE IF EXISTS `material_master_plan_dtl`;
CREATE TABLE `material_master_plan_dtl`  (
                                             `dtl_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '物资总计划明细id',
                                             `bill_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '物资总计划id',
                                             `project_plan_dtl_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '工程计划明细id',
                                             `material_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '物资id',
                                             `material_name` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '物资名称',
                                             `material_class_id` varchar(150) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '物资类别id(1级类别id/2级类别id/..)',
                                             `material_class_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '物资类别名称(1级类别名称/2级类别名称/..)',
                                             `spec` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '规格型号',
                                             `texture` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '材质',
                                             `unit` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '计量单位',
                                             `plan_quantity` decimal(18, 4) NULL DEFAULT NULL COMMENT '计划数量',
                                             `design_quantity` decimal(18, 4) NULL DEFAULT NULL COMMENT '设计数量',
                                             `budget_price` decimal(18, 4) NULL DEFAULT NULL COMMENT '预算单价(元)',
                                             `budget_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '预算金额(元)',
                                             `market_price` decimal(18, 4) NULL DEFAULT NULL COMMENT '市场单价(元)',
                                             `market_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '市场金额(元)',
                                             `purchasing_unit_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '采购单位id',
                                             `purchasing_unit_name` varchar(250) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '采购单位名称',
                                             `purchase_type` int NULL DEFAULT NULL COMMENT '采购方式',
                                             `received_quantity` decimal(18, 4) NULL DEFAULT NULL COMMENT '已收料数量',
                                             `summarized_quantity` decimal(18, 4) NULL DEFAULT NULL COMMENT '已汇总数量',
                                             `used_quantity_tender` decimal(18, 4) NULL DEFAULT NULL COMMENT '已使用数量(招标)',
                                             `used_quantity_contract` decimal(18, 4) NULL DEFAULT NULL COMMENT '已使用数量(合同)',
                                             `is_summarized` int NULL DEFAULT NULL COMMENT '是否已汇总(0:否;1:是)',
                                             `order_item_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '订单项id',
                                             `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                             `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                             `founder_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                             `founder_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                             `remarks` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '备注',
                                             `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                                             `product_type` tinyint NULL DEFAULT NULL COMMENT '商品类型：0物资 1设备 2周材（物资） 3周材（设备） 4二手设备 5租赁设备',
                                             `sort` int NULL DEFAULT NULL COMMENT '排序',
                                             `state` tinyint NULL DEFAULT NULL COMMENT '状态',
                                             `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                                             `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                             `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                             PRIMARY KEY (`dtl_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '物资总计划明细' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for material_month_supply_plan
-- ----------------------------
DROP TABLE IF EXISTS `material_month_supply_plan`;
CREATE TABLE `material_month_supply_plan`  (
                                               `plan_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '计划Id',
                                               `plan_no` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '计划编号',
                                               `plan_date` date NOT NULL COMMENT '计划日期',
                                               `plan_create_date` date NOT NULL COMMENT '计划创建日期',
                                               `business_type` tinyint NOT NULL COMMENT '业务类型（物资采购合同0、事实合同1、零星采购计划2）',
                                               `contract_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '合同id',
                                               `contract_no` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '合同编号',
                                               `supplier_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '供应商id（pcwp）',
                                               `supplier_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '供应商名称',
                                               `org_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '机构id（pcwp）',
                                               `org_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '机构名称',
                                               `state` tinyint NOT NULL DEFAULT 0 COMMENT '计划状态（0草稿1待审核2通过3未通过4已作废）',
                                               `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                               `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                               `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                               `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                               `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                               `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                                               `sort` int NULL DEFAULT NULL COMMENT '排序',
                                               `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                                               `local_supplier_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '本地供应商id',
                                               `local_org_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '本地机构id',
                                               `version` int NOT NULL DEFAULT 1 COMMENT '乐观锁',
                                               `pcwp_version` tinyint NULL DEFAULT NULL COMMENT 'pcwp版本区分（1:1.0，2:2.0）',
                                               `is_close` tinyint NOT NULL DEFAULT 0 COMMENT '计划是否完结（0 未完结 1已完结）',
                                               `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                               `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                               PRIMARY KEY (`plan_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '计划表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for material_month_supply_plan_change
-- ----------------------------
DROP TABLE IF EXISTS `material_month_supply_plan_change`;
CREATE TABLE `material_month_supply_plan_change`  (
                                                      `plan_change_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '变更计划Id',
                                                      `plan_change_no` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '变更计划编号',
                                                      `plan_date` date NOT NULL COMMENT '计划日期',
                                                      `plan_create_date` date NOT NULL COMMENT '计划创建日期',
                                                      `business_type` tinyint NOT NULL COMMENT '业务类型（物资采购合同0、事实合同1、零星采购计划2）',
                                                      `contract_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '合同id',
                                                      `contract_no` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '合同编号',
                                                      `supplier_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '供应商id（pcwp）',
                                                      `supplier_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '供应商名称',
                                                      `org_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '机构id（pcwp）',
                                                      `org_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '机构名称',
                                                      `state` tinyint NOT NULL DEFAULT 0 COMMENT '计划状态（0草稿1待审核2通过3未通过4已作废）',
                                                      `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                                      `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                                      `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                                      `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                                      `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                                      `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                                                      `sort` int NULL DEFAULT NULL COMMENT '排序',
                                                      `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                                                      `plan_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '源计划id',
                                                      `plan_no` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '源计划编号',
                                                      `local_supplier_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '本地供应商id',
                                                      `local_org_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '本地机构id',
                                                      `version` int NOT NULL DEFAULT 1 COMMENT '乐观锁',
                                                      `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                                      `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                                      PRIMARY KEY (`plan_change_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '计划变更表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for material_month_supply_plan_dtl
-- ----------------------------
DROP TABLE IF EXISTS `material_month_supply_plan_dtl`;
CREATE TABLE `material_month_supply_plan_dtl`  (
                                                   `plan_dtl_id` char(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '计划明细id',
                                                   `plan_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '计划Id',
                                                   `material_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物资id',
                                                   `material_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '物资名称',
                                                   `spec` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '规格型号',
                                                   `unit` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '计量单位',
                                                   `texture` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '材质',
                                                   `this_plan_qty` decimal(10, 4) NOT NULL COMMENT '本期计划数量',
                                                   `source_qty` decimal(10, 4) NOT NULL COMMENT '计划明细总数量',
                                                   `state` tinyint NOT NULL DEFAULT 0 COMMENT '计划状态（0草稿1待审核2通过3未通过4已作废）冗余用于统计数量',
                                                   `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                                   `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                                   `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                                   `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                                   `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                                   `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                                                   `sort` int NULL DEFAULT NULL COMMENT '排序',
                                                   `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                                                   `contract_dtl_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '合同明细id',
                                                   `order_qty` decimal(10, 4) NOT NULL DEFAULT 0.0000 COMMENT '已生成订单商品数量',
                                                   `class_path_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分类路径名称（xxx/xxx/xx）',
                                                   `class_path_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分类路径id（xxx/xxx/xx）',
                                                   `version` int NOT NULL DEFAULT 1 COMMENT '乐观锁',
                                                   `two_supplier_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '二级供应商id ',
                                                   `two_supplier_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '二级供应商名称',
                                                   `is_orders` tinyint NULL DEFAULT 0 COMMENT '是否生成订单  （0未生成  1已生成）',
                                                   `close_qty` decimal(10, 4) NOT NULL DEFAULT 0.0000 COMMENT '完结回归合同数量\r\n',
                                                   `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                                   `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                                   PRIMARY KEY (`plan_dtl_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '计划明细' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for material_month_supply_plan_dtl_change
-- ----------------------------
DROP TABLE IF EXISTS `material_month_supply_plan_dtl_change`;
CREATE TABLE `material_month_supply_plan_dtl_change`  (
                                                          `plan_dtl_change_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '变更计划明细id',
                                                          `plan_change_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '变更计划id',
                                                          `plan_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '源计划id',
                                                          `plan_dtl_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '源计划明细id',
                                                          `material_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物资id',
                                                          `material_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '物资名称',
                                                          `spec` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '规格型号',
                                                          `unit` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '计量单位',
                                                          `texture` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '材质',
                                                          `this_plan_qty` decimal(10, 4) NOT NULL COMMENT '本期计划数量',
                                                          `source_qty` decimal(10, 4) NOT NULL COMMENT '计划明细总数量',
                                                          `state` tinyint NULL DEFAULT NULL COMMENT '状态',
                                                          `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                                          `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                                          `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                                          `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                                          `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                                          `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                                                          `sort` int NULL DEFAULT NULL COMMENT '排序',
                                                          `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                                                          `contract_dtl_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '合同明细id',
                                                          `order_qty` decimal(10, 4) NOT NULL DEFAULT 0.0000 COMMENT '已生成订单数量',
                                                          `class_path_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分类路径名称（xxx/xxx/xx）',
                                                          `class_path_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分类路径id（xxx/xxx/xx）',
                                                          `version` int NOT NULL DEFAULT 1 COMMENT '乐观锁',
                                                          `dtl_update_state` tinyint NULL DEFAULT 2 COMMENT '明细修改状态（1新增2修改3删除）默认修改',
                                                          `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                                          `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                                          PRIMARY KEY (`plan_dtl_change_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '计划明细' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for material_reconciliation
-- ----------------------------
DROP TABLE IF EXISTS `material_reconciliation`;
CREATE TABLE `material_reconciliation`  (
                                            `reconciliation_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '对账单ID',
                                            `reconciliation_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '对账单编号',
                                            `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '对账标题',
                                            `type` tinyint NULL DEFAULT NULL COMMENT '对账类型（1浮动价格对账单2固定价格对账单）',
                                            `business_type` tinyint NULL DEFAULT NULL COMMENT '业务类型（1合同2计划3调拨4甲供5暂估）',
                                            `source_bill_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '源单ID',
                                            `source_bill_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '源单编号',
                                            `source_bill_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '原单名称',
                                            `supplier_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商id',
                                            `supplier_enterprise_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商本地机构id',
                                            `supplier_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商名称',
                                            `purchaser_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '采购人员ID',
                                            `purchaser_local_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '采购人员本地id',
                                            `purchaser_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '采购人员名称',
                                            `purchasing_org_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '采购单位ID',
                                            `purchasing_local_org_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '采购单位本地id',
                                            `purchasing_org_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '采购单位名称',
                                            `acceptance_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '对账远程人员ID',
                                            `acceptance_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '对账远程人员名称',
                                            `reconciliation_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '对账总金额（含税）',
                                            `reconciliation_no_rate_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '对账总金额（不含税）',
                                            `settle_amount` decimal(18, 4) NULL DEFAULT 0.0000 COMMENT '已结算金额',
                                            `start_time` datetime NULL DEFAULT NULL COMMENT '对账开始时间',
                                            `end_time` datetime NULL DEFAULT NULL COMMENT '对账结束时间',
                                            `create_type` tinyint NULL DEFAULT NULL COMMENT '新增来源（1采购员新增2供应商新增3pcwp新增）',
                                            `purchase_is_affirm` tinyint NULL DEFAULT 0 COMMENT '采购是否确认（0否1是）',
                                            `purchase_affirm_time` datetime NULL DEFAULT NULL COMMENT '采购员确认时间',
                                            `reconciliation_product_type` tinyint NULL DEFAULT NULL COMMENT '对账结算类型（0普通物资 10零星采购（低值易耗品）11办公用品 12大宗月供订单）',
                                            `supplier_affirm_time` datetime NULL DEFAULT NULL COMMENT '供应商确认时间',
                                            `supplier_is_affirm` tinyint NULL DEFAULT 0 COMMENT '供应商是否确认（0否1是）',
                                            `is_push` tinyint NULL DEFAULT 0 COMMENT '是否推送（以后定时器使用）',
                                            `freight` decimal(18, 2) NULL DEFAULT 0.00 COMMENT '运输单位费用(含税,元)',
                                            `tax_rate` decimal(18, 2) NULL DEFAULT NULL COMMENT '税率(%)',
                                            `nullify_reason` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '作废原因',
                                            `nullify_creator_local_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '作废人本地id',
                                            `nullify_creator_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '作废人id',
                                            `nullify_creator` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '作废人',
                                            `nullify_created` datetime NULL DEFAULT NULL COMMENT '作废时间',
                                            `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                            `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                            `state` tinyint NULL DEFAULT 0 COMMENT '状态（0草稿1待提交2待审核3审核通过4审核失败7作废）',
                                            `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人id',
                                            `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                            `sort` int NULL DEFAULT NULL COMMENT '排序',
                                            `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                            `is_delete` tinyint NULL DEFAULT 0 COMMENT '逻辑删除 -1: 删除 0:未删除',
                                            `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                                            `org_name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建机构名称',
                                            `org_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建机构id',
                                            `enterprise_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建本地机构id',
                                            `relevance_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '关联id',
                                            `relevance_sn` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '关联编号',
                                            `credit_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商信用代码（外部供应商使用）',
                                            `org_short` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商机构简码（内部供应商使用）',
                                            `supplier_org_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商机构id（内部供应商使用）',
                                            `is_not_push` tinyint NULL DEFAULT NULL COMMENT '是否待推送pcwp（1是0已推送null不需要推送）',
                                            `version` int NOT NULL DEFAULT 1 COMMENT '乐观锁',
                                            `out_phase_interest` decimal(10, 2) NULL DEFAULT NULL COMMENT '超期垫资利息（%）',
                                            `payment_week` tinyint NULL DEFAULT NULL COMMENT '货款支付周期（单位月）',
                                            `invoice_state` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '发票状态',
                                            `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                            `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                            `tax_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '税额',
                                            PRIMARY KEY (`reconciliation_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '物资验收' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for material_reconciliation_dtl
-- ----------------------------
DROP TABLE IF EXISTS `material_reconciliation_dtl`;
CREATE TABLE `material_reconciliation_dtl`  (
                                                `reconciliation_dtl_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '对账明细ID',
                                                `reconciliation_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '对账ID',
                                                `source_dtl_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '源单明细ID（对应pcwp的id）',
                                                `material_class_id` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '物资类别id(1级类别id/2级类别id/..)',
                                                `material_class_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '物资类别名称(1级类别名称/2级类别名称/..)',
                                                `material_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物资id',
                                                `material_mall_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '商城物资id',
                                                `material_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物资名称',
                                                `spec` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '规格型号',
                                                `unit` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '计量单位',
                                                `texture` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '材质',
                                                `no_rate_price` decimal(18, 2) NULL DEFAULT NULL COMMENT '对账单价（不含税）',
                                                `price` decimal(18, 2) NULL DEFAULT 0.00 COMMENT '对账单价（含税）',
                                                `quantity` decimal(18, 4) NULL DEFAULT 0.0000 COMMENT '对账数量',
                                                `acceptance_amount` decimal(18, 4) NULL DEFAULT 0.0000 COMMENT '对账总金额（含税）',
                                                `acceptance_no_rate_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '对账总金额（不含税）',
                                                `source_amount` decimal(18, 2) NULL DEFAULT 0.00 COMMENT '源单据总金额（废弃）',
                                                `source_quantity` decimal(18, 4) NULL DEFAULT 0.0000 COMMENT '原单据总数量（废弃）',
                                                `settled_amount` decimal(18, 2) NULL DEFAULT 0.00 COMMENT '已结算金额',
                                                `freight_price` decimal(18, 2) NULL DEFAULT 0.00 COMMENT '到货网价',
                                                `fixation_price` decimal(18, 2) NULL DEFAULT 0.00 COMMENT '固定费用',
                                                `state` tinyint NULL DEFAULT NULL COMMENT '状态',
                                                `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人id',
                                                `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                                `sort` int NULL DEFAULT NULL COMMENT '排序',
                                                `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                                `is_delete` tinyint NULL DEFAULT 0 COMMENT '逻辑删除 -1: 删除 0:未删除',
                                                `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                                                `order_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '订单id',
                                                `order_sn` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '订单编号',
                                                `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                                `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                                `receipt_bill_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'pcwp收料单id',
                                                `receipt_bill_sn` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'pcwp收料单编号',
                                                `receipt_bill_dtl_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'pcwp收料明细id',
                                                `receiving_date` datetime NULL DEFAULT NULL COMMENT 'pcwp收料单单据日期',
                                                `warehouse_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '仓库id（pcwp1需要使用）',
                                                `warehouse_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '仓库名称（pcwp1需要使用）',
                                                `version` int NOT NULL DEFAULT 1 COMMENT '乐观锁',
                                                `out_factory_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '出厂价（固定价格使用）',
                                                `transport_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '运杂费（固定价格使用）',
                                                `order_dtl_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '订单明细id（大宗临采使用）',
                                                `trade_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '商品id(零星和大宗临采使用)',
                                                `order_item_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '订单项id',
                                                `product_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '商品名称',
                                                `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                                `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                                `tax_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '税额',
                                                PRIMARY KEY (`reconciliation_dtl_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '物资验收明细' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for material_reconciliation_dtl_copy1
-- ----------------------------
DROP TABLE IF EXISTS `material_reconciliation_dtl_copy1`;
CREATE TABLE `material_reconciliation_dtl_copy1`  (
                                                      `reconciliation_dtl_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '对账明细ID',
                                                      `reconciliation_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '对账ID',
                                                      `source_dtl_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '源单明细ID（对应pcwp的id）',
                                                      `material_class_id` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '物资类别id(1级类别id/2级类别id/..)',
                                                      `material_class_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '物资类别名称(1级类别名称/2级类别名称/..)',
                                                      `material_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物资id',
                                                      `material_mall_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '商城物资id',
                                                      `material_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物资名称',
                                                      `spec` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '规格型号',
                                                      `unit` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '计量单位',
                                                      `texture` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '材质',
                                                      `no_rate_price` decimal(18, 2) NULL DEFAULT NULL COMMENT '对账单价（不含税）',
                                                      `price` decimal(18, 2) NULL DEFAULT 0.00 COMMENT '对账单价（含税）',
                                                      `quantity` decimal(18, 4) NULL DEFAULT 0.0000 COMMENT '对账数量',
                                                      `acceptance_amount` decimal(18, 4) NULL DEFAULT 0.0000 COMMENT '对账总金额（含税）',
                                                      `acceptance_no_rate_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '对账总金额（不含税）',
                                                      `source_amount` decimal(18, 2) NULL DEFAULT 0.00 COMMENT '源单据总金额（废弃）',
                                                      `source_quantity` decimal(18, 4) NULL DEFAULT 0.0000 COMMENT '原单据总数量（废弃）',
                                                      `settled_amount` decimal(18, 2) NULL DEFAULT 0.00 COMMENT '已结算金额',
                                                      `freight_price` decimal(18, 2) NULL DEFAULT 0.00 COMMENT '到货网价',
                                                      `fixation_price` decimal(18, 2) NULL DEFAULT 0.00 COMMENT '固定费用',
                                                      `state` tinyint NULL DEFAULT NULL COMMENT '状态',
                                                      `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人id',
                                                      `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                                      `sort` int NULL DEFAULT NULL COMMENT '排序',
                                                      `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                                      `is_delete` tinyint NULL DEFAULT 0 COMMENT '逻辑删除 -1: 删除 0:未删除',
                                                      `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                                                      `order_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '订单id',
                                                      `order_sn` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '订单编号',
                                                      `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                                      `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                                      `receipt_bill_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'pcwp收料单id',
                                                      `receipt_bill_sn` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'pcwp收料单编号',
                                                      `receipt_bill_dtl_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'pcwp收料明细id',
                                                      `receiving_date` datetime NULL DEFAULT NULL COMMENT 'pcwp收料单单据日期',
                                                      `warehouse_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '仓库id（pcwp1需要使用）',
                                                      `warehouse_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '仓库名称（pcwp1需要使用）',
                                                      `version` int NOT NULL DEFAULT 1 COMMENT '乐观锁',
                                                      `out_factory_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '出厂价（固定价格使用）',
                                                      `transport_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '运杂费（固定价格使用）',
                                                      `order_dtl_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '订单明细id（大宗临采使用）',
                                                      `trade_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '商品id(零星和大宗临采使用)',
                                                      `order_item_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '订单项id',
                                                      `product_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '商品名称',
                                                      `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                                      `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                                      `tax_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '税额',
                                                      PRIMARY KEY (`reconciliation_dtl_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '物资验收明细' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for message_informations
-- ----------------------------
DROP TABLE IF EXISTS `message_informations`;
CREATE TABLE `message_informations`  (
                                         `message_informations_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '留言信息id',
                                         `message_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '留言人id',
                                         `message_name` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '留言人名称',
                                         `respond_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '回复人id',
                                         `respond_name` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '回复人名称',
                                         `messag_title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '留言标题(总结你的反馈）',
                                         `messag_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '留言内容(详细描述)',
                                         `messag_type` tinyint NULL DEFAULT NULL COMMENT '留言类型（1：问题，2：建议）',
                                         `is_file` tinyint NULL DEFAULT NULL COMMENT '留言是否有附件(0否1是)',
                                         `messag_date` datetime NULL DEFAULT NULL COMMENT '留言发送时间',
                                         `respond_title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '回复标题',
                                         `respond_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '回复内容',
                                         `respond_date` datetime NULL DEFAULT NULL COMMENT '回复时间',
                                         `state` tinyint NULL DEFAULT NULL COMMENT '处理状态（0：未处理 1：已处理）',
                                         `is_delete` tinyint NULL DEFAULT 0 COMMENT '逻辑删除 -1: 删除 0:未删除',
                                         `sort` int NULL DEFAULT NULL COMMENT '排序',
                                         `mall_type` tinyint NULL DEFAULT 1 COMMENT '商城类型：0物资商场, 1设备商城 ',
                                         `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                         `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                         `founder_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                         `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                         `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                         `public_display` tinyint NULL DEFAULT 0 COMMENT '公开展示留言（0：不展示 1：展示）',
                                         `enterprise_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '留言人所属企业名称',
                                         `shop_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '留言人所属店铺名称',
                                         `is_interior` tinyint NULL DEFAULT NULL COMMENT '留言人是否是内部用户（0否1是）',
                                         `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                         `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                         PRIMARY KEY (`message_informations_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for month_plan
-- ----------------------------
DROP TABLE IF EXISTS `month_plan`;
CREATE TABLE `month_plan`  (
                               `bill_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '物资月度计划id',
                               `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                               `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                               `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人id',
                               `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
                               `work_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '流程id',
                               `bill_no` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '计划编号',
                               `plan_date` datetime NULL DEFAULT NULL COMMENT '计划日期',
                               `year` int NULL DEFAULT NULL COMMENT '计划年度',
                               `month` int NULL DEFAULT NULL COMMENT '计划月份',
                               `year_plan_bill_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物资年度计划id',
                               `year_plan_bill_no` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物资年度计划编号',
                               `amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '计划金额',
                               `tax_rate` decimal(18, 4) NULL DEFAULT NULL COMMENT '税率',
                               `tax_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '税额',
                               `total_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '税价合计',
                               `state` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '状态',
                               `preparer_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '填报人id',
                               `preparer` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '填报人名称',
                               `currency_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '币种id',
                               `currency` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '币种名称',
                               `org_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '单据机构id',
                               `org_name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '单据机构名称',
                               `remarks` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注信息',
                               `base_cur_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '本位币id',
                               `base_cur_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '本位币名称',
                               `base_cur_rate` decimal(18, 4) NULL DEFAULT NULL COMMENT '本位币汇率',
                               `base_cur_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '计划金额(本位币)',
                               `base_cur_tax_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '税额(本位币)',
                               `base_cur_total_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '税价合计(本位币)c',
                               `rmb_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '人民币id',
                               `rmb_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '人民币名称',
                               `rmb_rate` decimal(18, 4) NULL DEFAULT NULL COMMENT '人民币汇率',
                               `rmb_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '计划金额(人民币)',
                               `rmb_tax_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '税额(人民币)',
                               `rmb_total_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '税价合计(人民币)',
                               `nullify_reason` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '作废原因',
                               `nullify_description` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '作废描述',
                               `nullify_creator_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '作废人id',
                               `nullify_creator` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '作废人',
                               `nullify_created` datetime NULL DEFAULT NULL COMMENT '作废时间',
                               `last_change_bill_state` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '最新变更单状态',
                               `business_type` int NULL DEFAULT NULL COMMENT '业务类型',
                               `source_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '源单id',
                               `source_number` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '源单编号',
                               `supplier_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商id',
                               `supplier_name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商名称',
                               `credit_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '统一社会信用代码',
                               `source_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '合同名称',
                               `is_all_send` enum('0','1') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '是否全部发送',
                               `supplier_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '0内部，1：外部（供应商）',
                               `plan_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '计划类型（0要货，发货）',
                               `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型（0：物资 1：装备）',
                               `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                               `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                               PRIMARY KEY (`bill_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'pcwp2物资月度计划' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for month_plan_dtl
-- ----------------------------
DROP TABLE IF EXISTS `month_plan_dtl`;
CREATE TABLE `month_plan_dtl`  (
                                   `dtl_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '物资月度计划明细id',
                                   `bill_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物资月度计划id',
                                   `year_plan_dtl_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物资年度计划明细id',
                                   `material_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物资id',
                                   `material_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物资名称',
                                   `material_class_id` char(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物资类别id(1级类别id/2级类别id/..)',
                                   `material_class_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物资类别名称(1级类别名称/2级类别名称/..)',
                                   `spec` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '规格型号',
                                   `texture` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '材质',
                                   `unit` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '计量单位',
                                   `quantity` decimal(18, 3) NULL DEFAULT NULL COMMENT '数量',
                                   `price` decimal(18, 4) NULL DEFAULT NULL COMMENT '单价(元)',
                                   `amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '金额(元)',
                                   `purchase_type` int NULL DEFAULT NULL COMMENT '采购方式',
                                   `purchasing_unit_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '采购单位id',
                                   `purchasing_unit_name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '采购单位名称',
                                   `purchased_quantity` decimal(18, 3) NULL DEFAULT NULL COMMENT '已采购数量',
                                   `used_quantity_tender` decimal(18, 4) NULL DEFAULT NULL COMMENT '已使用数量(招标)',
                                   `used_quantity_contract` decimal(18, 4) NULL DEFAULT NULL COMMENT '已使用数量(合同)',
                                   `is_send` enum('1','0') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '是否生成发货计划',
                                   `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                   `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                   PRIMARY KEY (`dtl_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'pcwp2物资月度计划明细' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for month_send_plan
-- ----------------------------
DROP TABLE IF EXISTS `month_send_plan`;
CREATE TABLE `month_send_plan`  (
                                    `bill_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '物资月度计划id',
                                    `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                    `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                    `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人id',
                                    `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
                                    `work_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '流程id',
                                    `bill_no` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '计划编号',
                                    `plan_date` datetime NULL DEFAULT NULL COMMENT '计划日期',
                                    `year` int NULL DEFAULT NULL COMMENT '计划年度',
                                    `month` int NULL DEFAULT NULL COMMENT '计划月份',
                                    `year_plan_bill_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物资年度计划id',
                                    `year_plan_bill_no` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物资年度计划编号',
                                    `amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '计划金额',
                                    `tax_rate` decimal(18, 4) NULL DEFAULT NULL COMMENT '税率',
                                    `tax_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '税额',
                                    `total_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '税价合计',
                                    `state` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '状态',
                                    `preparer_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '填报人id',
                                    `preparer` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '填报人名称',
                                    `currency_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '币种id',
                                    `currency` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '币种名称',
                                    `org_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '单据机构id',
                                    `org_name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '单据机构名称',
                                    `remarks` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注信息',
                                    `base_cur_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '本位币id',
                                    `base_cur_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '本位币名称',
                                    `base_cur_rate` decimal(18, 4) NULL DEFAULT NULL COMMENT '本位币汇率',
                                    `base_cur_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '计划金额(本位币)',
                                    `base_cur_tax_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '税额(本位币)',
                                    `base_cur_total_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '税价合计(本位币)c',
                                    `rmb_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '人民币id',
                                    `rmb_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '人民币名称',
                                    `rmb_rate` decimal(18, 4) NULL DEFAULT NULL COMMENT '人民币汇率',
                                    `rmb_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '计划金额(人民币)',
                                    `rmb_tax_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '税额(人民币)',
                                    `rmb_total_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '税价合计(人民币)',
                                    `nullify_reason` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '作废原因',
                                    `nullify_description` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '作废描述',
                                    `nullify_creator_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '作废人id',
                                    `nullify_creator` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '作废人',
                                    `nullify_created` datetime NULL DEFAULT NULL COMMENT '作废时间',
                                    `last_change_bill_state` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '最新变更单状态',
                                    `business_type` int NULL DEFAULT NULL COMMENT '业务类型',
                                    `source_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '源单id',
                                    `source_number` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '源单编号',
                                    `supplier_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商id',
                                    `supplier_name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商名称',
                                    `credit_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '统一社会信用代码',
                                    `source_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '合同名称',
                                    `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                    `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                    PRIMARY KEY (`bill_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'pcwp2物资月度发送计划' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for month_send_plan_dtl
-- ----------------------------
DROP TABLE IF EXISTS `month_send_plan_dtl`;
CREATE TABLE `month_send_plan_dtl`  (
                                        `dtl_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '物资月度计划明细id',
                                        `bill_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物资月度计划id',
                                        `year_plan_dtl_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物资年度计划明细id',
                                        `material_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物资id',
                                        `material_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物资名称',
                                        `material_class_id` char(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物资类别id(1级类别id/2级类别id/..)',
                                        `material_class_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物资类别名称(1级类别名称/2级类别名称/..)',
                                        `spec` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '规格型号',
                                        `texture` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '材质',
                                        `unit` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '计量单位',
                                        `quantity` decimal(18, 3) NULL DEFAULT NULL COMMENT '数量',
                                        `price` decimal(18, 4) NULL DEFAULT NULL COMMENT '单价(元)',
                                        `amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '金额(元)',
                                        `purchase_type` int NULL DEFAULT NULL COMMENT '采购方式',
                                        `purchasing_unit_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '采购单位id',
                                        `purchasing_unit_name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '采购单位名称',
                                        `purchased_quantity` decimal(18, 3) NULL DEFAULT NULL COMMENT '已采购数量',
                                        `used_quantity_tender` decimal(18, 4) NULL DEFAULT NULL COMMENT '已使用数量(招标)',
                                        `used_quantity_contract` decimal(18, 4) NULL DEFAULT NULL COMMENT '已使用数量(合同)',
                                        `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                        `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                        PRIMARY KEY (`dtl_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'pcwp2物资月度发货计划明细' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for negotiated_price
-- ----------------------------
DROP TABLE IF EXISTS `negotiated_price`;
CREATE TABLE `negotiated_price`  (
                                     `negotiated_price_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '报价id',
                                     `enquiry_amount` decimal(18, 2) NOT NULL COMMENT '报价金额',
                                     `relevance_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '关联id',
                                     `type` tinyint NOT NULL COMMENT '报价类型（0求购2求租3招标）',
                                     `illustrate` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '说明',
                                     `state` tinyint NULL DEFAULT NULL COMMENT '状态',
                                     `sort` int NULL DEFAULT NULL COMMENT '排序',
                                     `is_delete` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                                     `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                     `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                     `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                     `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                     `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                     `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                                     `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                     `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                     PRIMARY KEY (`negotiated_price_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '询价' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for operate_log
-- ----------------------------
DROP TABLE IF EXISTS `operate_log`;
CREATE TABLE `operate_log`  (
                                `operate_log_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作记录id',
                                `relevance_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '关联id',
                                `relevance_type` tinyint NOT NULL COMMENT '关联类型（1:订单id）',
                                `explain_info` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '操作说明',
                                `before_update` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改前数据',
                                `after_update` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改后数据',
                                `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                                `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                PRIMARY KEY (`operate_log_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for order_contact
-- ----------------------------
DROP TABLE IF EXISTS `order_contact`;
CREATE TABLE `order_contact`  (
                                  `order_contact_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '订单合同id',
                                  `order_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '订单id',
                                  `order_sn` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '订单编号',
                                  `order_item_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '订单项id',
                                  `contact_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '合同id',
                                  `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间	',
                                  `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间	',
                                  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                  `sort` int NULL DEFAULT NULL COMMENT '排序',
                                  `state` tinyint NULL DEFAULT NULL COMMENT '状态',
                                  `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：（ 0物资商场  1设备商城 ）',
                                  `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除：（ -1: 删除  0:未删除）',
                                  `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                  `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                  PRIMARY KEY (`order_contact_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '订单合同关联信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for order_item
-- ----------------------------
DROP TABLE IF EXISTS `order_item`;
CREATE TABLE `order_item`  (
                               `order_item_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '订单项id',
                               `order_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '订单id',
                               `order_sn` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '订单号',
                               `product_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '商品id',
                               `product_sn` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '商品编号',
                               `product_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '商品名称',
                               `product_img` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '商品图片',
                               `sku_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'skuid',
                               `sku_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'sku名称',
                               `cost_price` decimal(18, 2) NULL DEFAULT NULL COMMENT '商品成本价',
                               `product_price` decimal(18, 2) NULL DEFAULT NULL COMMENT '商品价格',
                               `buy_counts` decimal(18, 4) NULL DEFAULT NULL COMMENT '购买数量',
                               `total_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '商品小计金额',
                               `is_comment` tinyint NOT NULL DEFAULT 0 COMMENT '评论状态： 0 未评价  1 已评价',
                               `buy_time` datetime NULL DEFAULT NULL COMMENT '购买时间',
                               `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                               `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                               `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                               `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                               `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                               `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                               `product_type` tinyint NULL DEFAULT NULL COMMENT '商品类型：0物资 1设备 2周材（设备租赁） 3周材（设备） 4二手设备 5租赁设备',
                               `sort` int NULL DEFAULT NULL COMMENT '排序',
                               `state` tinyint NULL DEFAULT 0 COMMENT '订单明细状态（0默认不使用1已选择供方2待分配3待分配竞价4已生成竞价）',
                               `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                               `version` int NOT NULL DEFAULT 1 COMMENT '乐观锁',
                               `bill_id` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '关联计划id（逗号分隔）',
                               `dtl_id` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '关联计划明细id（逗号分割）',
                               `relevance_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '关联名称（用于关联外部的名称唯一不修改）',
                               `lease_num` decimal(18, 4) NOT NULL DEFAULT 1.0000 COMMENT '租赁时长',
                               `lease_unit` char(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '租赁单位（天月年）',
                               `return_state` tinyint NULL DEFAULT NULL COMMENT '退货状态 0:申请退货，1审核中，2审核通过 3:审核失败，',
                               `invoice_state` tinyint NULL DEFAULT NULL COMMENT '是否开票（0：未申请，2.已申请）',
                               `profit_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '利润',
                               `ship_counts` decimal(18, 4) NULL DEFAULT 0.0000 COMMENT '发货数量',
                               `supplier_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商id（本地机构id）',
                               `parent_order_item_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '父订单项id（是拆单订单这里才有值）',
                               `cost_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '总成本价',
                               `confirm_counts` decimal(18, 4) NOT NULL DEFAULT 0.0000 COMMENT '确认收货数量',
                               `original_price` decimal(18, 2) NULL DEFAULT 0.00 COMMENT '原价',
                               `return_counts` decimal(18, 4) NOT NULL DEFAULT 0.0000 COMMENT '退货数量',
                               `unit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '单位',
                               `class_path_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分类路径名称（xxx/xxx/xx）',
                               `class_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分类id',
                               `brand_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '品牌id',
                               `brand_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '品牌名称',
                               `texture` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '材质',
                               `no_rate_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '不含税单价',
                               `no_rate_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '不含税总金额',
                               `tax_rate` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '税率',
                               `class_path_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分类路径id（xxx/xxx/xx）',
                               `payment_week` tinyint NULL DEFAULT NULL COMMENT '货款支付周期（单位月）',
                               `net_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '网价（浮动价格使用）',
                               `fixation_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '固定费用（浮动价格使用）',
                               `out_factory_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '出厂价（固定价格使用）',
                               `transport_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '运杂费（固定价格使用）',
                               `is_two_unit` tinyint NULL DEFAULT NULL COMMENT '是否有二级单位',
                               `two_unit` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '二级单位',
                               `two_unit_num` decimal(10, 4) NULL DEFAULT NULL COMMENT '二级单位购买数量',
                               `second_unit_num` decimal(10, 4) NULL DEFAULT NULL COMMENT '二级单位的计算系数',
                               `relevance_no` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '关联编号',
                               `pcwp_return` decimal(10, 4) NOT NULL DEFAULT 0.0000 COMMENT 'pcwp退货数量',
                               `close_max_qty` decimal(10, 4) NOT NULL DEFAULT 0.0000 COMMENT '手动关闭订单，pcwp退货打开订单记录最大发货数量',
                               `relevance_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '关联id(用于关联外部(物资基础库)的id)',
                               `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                               `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                               PRIMARY KEY (`order_item_id`) USING BTREE,
                               UNIQUE INDEX `index_name1`(`order_item_id` ASC) USING BTREE,
                               INDEX `parent_order_item_id`(`parent_order_item_id` ASC) USING BTREE,
                               INDEX `order_sn`(`order_sn` ASC) USING BTREE,
                               INDEX `order_id`(`order_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '订单项' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for order_payment_info
-- ----------------------------
DROP TABLE IF EXISTS `order_payment_info`;
CREATE TABLE `order_payment_info`  (
                                       `payment_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '支付号',
                                       `order_sn` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单号（对外业务号）',
                                       `order_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单id',
                                       `alipay_trade_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '交易流水号',
                                       `total_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '支付总金额',
                                       `subject` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '交易内容',
                                       `payment_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付状态',
                                       `callback_content` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '回调内容',
                                       `callback_time` datetime NULL DEFAULT NULL COMMENT '回调时间',
                                       `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                       `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                       `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                       `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                       `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '备注',
                                       `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                                       `product_type` tinyint NULL DEFAULT NULL COMMENT '商品类型：0物资 1设备 2周材（物资） 3周材（设备）',
                                       `sort` int NULL DEFAULT NULL COMMENT '排序',
                                       `state` tinyint NULL DEFAULT NULL COMMENT '状态',
                                       `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                                       `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                       `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                       PRIMARY KEY (`payment_id`) USING BTREE,
                                       UNIQUE INDEX `order_sn`(`order_sn` ASC) USING BTREE,
                                       UNIQUE INDEX `alipay_trade_no`(`alipay_trade_no` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '支付信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for order_return
-- ----------------------------
DROP TABLE IF EXISTS `order_return`;
CREATE TABLE `order_return`  (
                                 `order_return_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '退货id',
                                 `order_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '订单id',
                                 `order_sn` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '订单编号',
                                 `order_return_no` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '退货编号',
                                 `remarks` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '问题描述',
                                 `submit_reason` varchar(23) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '退货原因（1 ：7天无理由退货 2不想要了 3.物流太慢）',
                                 `user_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '退货人id',
                                 `return_method` int NULL DEFAULT NULL COMMENT '退货方式（1上门取件 2快递至卖家）',
                                 `state` tinyint NULL DEFAULT NULL COMMENT '退货状态 1:已申请 2:退货中 3:退款成功 4:退货失败',
                                 `delivery_flow_id` varchar(244) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '物流单号',
                                 `delivery_type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '配送方式1(上门取件 2快递至卖家）',
                                 `flish_time` datetime NULL DEFAULT NULL COMMENT '退货成功时间',
                                 `receiver_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '收货人姓名',
                                 `receiver_mobile` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '收货人电话',
                                 `receiver_address` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '收货人地址',
                                 `address` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '详细地址',
                                 `gmt_create` datetime NULL DEFAULT NULL COMMENT '退货时间',
                                 `founder_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人姓名',
                                 `founder_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
                                 `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                 `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                                 `is_delete` tinyint NULL DEFAULT NULL COMMENT '是否删除',
                                 `sort` int NULL DEFAULT NULL COMMENT '排序值',
                                 `total_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '退款金额',
                                 `send_address` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发货人地址',
                                 `send_mobile` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发货人电话',
                                 `send_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发货人姓名',
                                 `logistics_company` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '物流公司',
                                 `deliveryFlowId` varchar(244) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '物流单号',
                                 `source_type` tinyint NULL DEFAULT NULL COMMENT '退货来源',
                                 `order_class` tinyint NOT NULL DEFAULT 1 COMMENT '订单类别（1普通订单2多供方订单）',
                                 `other_order_sn` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '订单编号（二级订单）',
                                 `other_order_item_id` varchar(233) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '订单项（二级订单））',
                                 `other_order_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '订单编号（二级订单））',
                                 `other_total_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '退款金额',
                                 `no_rate_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '不含税总金额',
                                 `is_out` tinyint NOT NULL DEFAULT 1 COMMENT '是否外部退货（1外部退货（计划，合同）0内部退货）',
                                 `supplier_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '企业名称',
                                 `supplier_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '企业id',
                                 `enterprise_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '退货企业名称',
                                 `enterprise_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '退货企业id',
                                 `ship_enterprise_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '二级供应商名称',
                                 `ship_enterprise_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '二级供应商企业id',
                                 `rate_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '含税总金额',
                                 `other_no_rate_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '二级供应商不含税总金额',
                                 `other_rate_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '二级供应商含税总金额',
                                 `shop_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '店铺Id',
                                 `shop_name` varchar(233) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '店铺名称',
                                 `untitled` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '产品名称（多个产品用,隔开）',
                                 `business_type` tinyint NULL DEFAULT NULL COMMENT '业务类型（物资采购合同0、事实合同1、零星采购计划2 6大宗临采）',
                                 `bill_type` bigint NULL DEFAULT NULL COMMENT '1浮动 2 固定',
                                 `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                 `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                 `out_key_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '退货id',
                                 PRIMARY KEY (`order_return_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '退货表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for order_return_item
-- ----------------------------
DROP TABLE IF EXISTS `order_return_item`;
CREATE TABLE `order_return_item`  (
                                      `order_return_item_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '退货项id',
                                      `order_return_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '退货id',
                                      `order_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '订单id',
                                      `order_sn` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '订单号',
                                      `other_order_sn` char(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '二级订单号',
                                      `order_return_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '退货单编号',
                                      `other_order_item_id` char(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '二级订单项Id',
                                      `order_item_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '订单项Id',
                                      `product_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '商品id',
                                      `product_sn` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '商品编号',
                                      `product_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '商品名称',
                                      `product_img` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '商品图片',
                                      `sku_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'skuid',
                                      `sku_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'sku名称',
                                      `buy_counts` decimal(18, 4) NOT NULL COMMENT '购买数量',
                                      `count` decimal(18, 4) NOT NULL DEFAULT 0.0000 COMMENT '退货数量',
                                      `is_delete` tinyint NULL DEFAULT 0 COMMENT '逻辑删除 -1: 删除 0:未删除',
                                      `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                      `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                      `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                      `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                      `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                      `version` int NOT NULL DEFAULT 1 COMMENT '乐观锁',
                                      `product_price` decimal(18, 2) NULL DEFAULT NULL COMMENT '商品价格',
                                      `total_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '总金额',
                                      `rate_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '含税总金额',
                                      `no_rate_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '不含税总金额',
                                      `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                                      `brand_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '品牌id',
                                      `brand_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '品牌名称',
                                      `sort` int NULL DEFAULT NULL COMMENT '排序',
                                      `is_out` tinyint NOT NULL DEFAULT 1 COMMENT '是否外部退货（1外部退货（计划，合同）0内部退货）',
                                      `other_order_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '二级订单Id',
                                      `other_total_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '二级订单总金额',
                                      `other_rate_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '二级含税金额',
                                      `other_no_rate_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '二级不含税金额',
                                      `other_product_price` decimal(18, 2) NULL DEFAULT NULL COMMENT '二级订单单价',
                                      `is_reconciliation` tinyint NOT NULL DEFAULT 0 COMMENT '是否对账',
                                      `unit` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '单位',
                                      `texture` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '材质',
                                      `no_rate_price` decimal(18, 2) NULL DEFAULT NULL COMMENT '二级不含税商品金额',
                                      `other_no_product_price` decimal(18, 2) NULL DEFAULT NULL COMMENT '二级不含税商品金额',
                                      `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                      `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                      PRIMARY KEY (`order_return_item_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '退货项表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for order_select_contact
-- ----------------------------
DROP TABLE IF EXISTS `order_select_contact`;
CREATE TABLE `order_select_contact`  (
                                         `order_select_contact_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '订单选择合同id',
                                         `order_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '订单id',
                                         `order_sn` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '订单编号',
                                         `order_item_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '订单项id',
                                         `dtl_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '合同设备明细id',
                                         `item_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '设备名称',
                                         `count` decimal(10, 4) NOT NULL COMMENT '已选数量',
                                         `bill_no` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '合同编号',
                                         `bill_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '合同id',
                                         `bill_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '合同名称',
                                         `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间	',
                                         `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间	',
                                         `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                         `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                         `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                         `sort` int NULL DEFAULT NULL COMMENT '排序',
                                         `state` tinyint NULL DEFAULT NULL COMMENT '状态',
                                         `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：（ 0物资商场  1设备商城 ）',
                                         `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除：（ -1: 删除  0:未删除）',
                                         `product_type` tinyint NULL DEFAULT NULL COMMENT '商品类型：0物资 1设备 2周材（物资） 3周材（设备） 4二手设备 5租赁设备 6维修服务 7金融服务 8保险服务',
                                         `size` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '合同设备型号',
                                         `org_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '机构id',
                                         `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                         `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                         PRIMARY KEY (`order_select_contact_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '订单关联合同' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for order_select_plan
-- ----------------------------
DROP TABLE IF EXISTS `order_select_plan`;
CREATE TABLE `order_select_plan`  (
                                      `order_select_plan_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '订单已选择计划id',
                                      `order_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '订单id',
                                      `order_sn` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '订单编号',
                                      `order_item_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '订单项id',
                                      `dtl_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '计划明细id',
                                      `equipment_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '设备名称',
                                      `count` decimal(10, 4) NULL DEFAULT NULL COMMENT '已选数量',
                                      `bill_no` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '计划编号',
                                      `bill_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '计划id',
                                      `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间	',
                                      `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间	',
                                      `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                      `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                      `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                      `sort` int NULL DEFAULT NULL COMMENT '排序',
                                      `state` tinyint NULL DEFAULT NULL COMMENT '状态',
                                      `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：（ 0物资商场  1设备商城 ）',
                                      `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除：（ -1: 删除  0:未删除）',
                                      `product_type` tinyint NULL DEFAULT NULL COMMENT '商品类型：0物资 1设备 2周材（物资） 3周材（设备） 4二手设备 5租赁设备 6维修服务 7金融服务 8保险服务',
                                      `org_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '机构名称(采购员的机构名称)',
                                      `org_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '机构id(采购员的orgId)',
                                      `price` decimal(18, 2) NULL DEFAULT NULL COMMENT '计划的单价',
                                      `account` decimal(18, 2) NULL DEFAULT NULL COMMENT '计划的总金额',
                                      `storage_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '供应商id',
                                      `storage_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '供应商名称',
                                      `short_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '机构简码',
                                      `credit_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '信用代码',
                                      `supplier_type` tinyint NULL DEFAULT NULL COMMENT '供应闪类型（1外部供应闪2内部供应商）',
                                      `storage_org_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '供应商的机构id',
                                      `plan_type` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '计划类型（1零星采购计划2大宗计划）',
                                      `contract_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '大宗计划合同id',
                                      `contract_no` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '大宗计划合同编号',
                                      `tax_rate` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '供应商税率',
                                      `bill_type` tinyint NULL DEFAULT NULL COMMENT '清单类型（1浮动价格2固定价格）',
                                      `synthesize_temporary_dtl_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '大宗临采单明细id',
                                      `source_plan_qty` decimal(10, 4) NULL DEFAULT NULL COMMENT '源计划明细总数量',
                                      `source_contract_qty` decimal(10, 4) NULL DEFAULT NULL COMMENT '源合同明细总数量',
                                      `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                      `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                      PRIMARY KEY (`order_select_plan_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '选择计划清单' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for order_ship
-- ----------------------------
DROP TABLE IF EXISTS `order_ship`;
CREATE TABLE `order_ship`  (
                               `bill_Id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '单据id',
                               `order_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '订单id',
                               `bill_sn` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '单据编号',
                               `order_sn` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '订单编号',
                               `type` tinyint NULL DEFAULT NULL COMMENT '发货单状态（0未发货。1发货中，2已收货，）',
                               `ship_data` datetime NULL DEFAULT NULL COMMENT '发货时间',
                               `ship_user_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '发货人id',
                               `suplier_id` char(44) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商id',
                               `supplier_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商姓名',
                               `receive_id` char(44) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '收料人员Id',
                               `receive_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '收料员名称',
                               `receive_phone` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '收料员联系电话',
                               `receive_org_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '收料人员单位id',
                               `receive_org_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '收料单位名称',
                               `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                               `total_price` decimal(18, 2) NULL DEFAULT NULL COMMENT '总价格',
                               `ship_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '发货地址',
                               `org_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '机构名称',
                               `delivery_flow_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物流单号',
                               `logistics_company` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物流公司',
                               `shop_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '店铺id',
                               `shop_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '店铺名称',
                               `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                               `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                               `founder_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '当前用户',
                               `founder_id` varchar(222) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '操作人id',
                               `is_delete` tinyint NULL DEFAULT NULL COMMENT '是否删除',
                               `sort` int NULL DEFAULT NULL COMMENT '排序',
                               `remarks` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
                               `org_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '收料员组织机构Id',
                               `flish_time` datetime NULL DEFAULT NULL COMMENT '下单时间',
                               `suplier_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商社会编码',
                               `confirm_time` datetime NULL DEFAULT NULL COMMENT '收料员确认收料时间',
                               `plan_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '计划名称',
                               `plan_no` varchar(233) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '计划编号',
                               `out_bill_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '外部收料单id',
                               `out_bill_no` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '外部收料单编号',
                               `order_class` tinyint NOT NULL DEFAULT 1 COMMENT '订单类别（1普通订单2多供方订单）',
                               `other_total_price` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '总价格',
                               `ship_enterprise_id` char(44) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '发货企业id',
                               `ship_enterprise_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '发货企业名称',
                               `ship_user_name` varchar(233) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '发货人姓名',
                               `product_type` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '商品类型： 10低值易耗品 12大宗材料',
                               `source_type` tinyint NULL DEFAULT NULL COMMENT '发货单来源',
                               `enterprise_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '采购员本地机构Id',
                               `source_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '原单名称',
                               `source_no` varchar(233) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '原单编号',
                               `enterprise_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '本地采购员机构名称',
                               `supplier_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商id',
                               `supplier_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商社会编码',
                               `tax_rate` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '税率',
                               `no_rate_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '不含税总金额',
                               `other_tax_rate` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '税率',
                               `other_no_rate_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '不含税总金额',
                               `other_rate_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '含税总金额',
                               `suplier_org_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商id',
                               `rate_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '不含税总金额',
                               `is_not_push` tinyint NULL DEFAULT NULL COMMENT 'pcwp是否验收',
                               `total_amount` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '税率',
                               `id_str` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '关键key\r\n',
                               `supplier_org_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商id',
                               `version` int NOT NULL DEFAULT 1 COMMENT '乐观锁',
                               `other_order_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '二级订单id（二级供应商使用）',
                               `other_order_sn` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '二级单订单编号（二级供应商使用）',
                               `bill_type` tinyint NULL DEFAULT NULL COMMENT '清单类型',
                               `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                               `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                               `out_key_id` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '推送日志id',
                               PRIMARY KEY (`bill_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '发货单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for order_ship_dtl
-- ----------------------------
DROP TABLE IF EXISTS `order_ship_dtl`;
CREATE TABLE `order_ship_dtl`  (
                                   `dtl_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
                                   `order_Id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '订单id',
                                   `order_item_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '订单项id',
                                   `order_sn` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '订单编号',
                                   `bill_sn` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '发货单编号',
                                   `bill_id` char(33) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '发货单编号',
                                   `product_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '商品名称',
                                   `product_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '商品id',
                                   `product_sn` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '商品编号',
                                   `send_time` datetime NULL DEFAULT NULL COMMENT '发货时间',
                                   `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                                   `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                   `ship_num` decimal(10, 4) NOT NULL DEFAULT 0.0000 COMMENT '已收货数量',
                                   `total_sum` decimal(18, 4) NOT NULL DEFAULT 0.0000 COMMENT '总数量',
                                   `product_category_id` varchar(233) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '商品分类id',
                                   `product_category_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '商品跟类名称',
                                   `ship_counts` decimal(16, 4) NOT NULL DEFAULT 0.0000 COMMENT '发货数量',
                                   `is_delete` int NULL DEFAULT NULL COMMENT '是否删除',
                                   `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '操作人id',
                                   `founder_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '当前用户',
                                   `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                   `sort` int NULL DEFAULT NULL COMMENT '排序',
                                   `remarks` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
                                   `sku_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'sku名称',
                                   `receive_time` datetime NULL DEFAULT NULL COMMENT '收货时间',
                                   `unit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '计量单位',
                                   `return_counts` decimal(18, 4) NOT NULL DEFAULT 0.0000 COMMENT '退货数量',
                                   `shop_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '店铺id',
                                   `is_equal` tinyint NOT NULL DEFAULT 0 COMMENT '是否相等   0 (发货数量==确认收货数量) 1：发货数量>确认收货数量 ',
                                   `other_product_price` decimal(10, 4) NOT NULL DEFAULT 0.0000 COMMENT '二级商品价格',
                                   `product_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '商品价格',
                                   `other_total_amount` decimal(14, 4) NOT NULL DEFAULT 0.0000 COMMENT '二级总价格',
                                   `other_no_rate_amount` decimal(14, 4) NOT NULL DEFAULT 0.0000 COMMENT '二级不含税商品总价',
                                   `other_no_rate_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '二级不含税商品单价',
                                   `no_rate_price` decimal(10, 4) NOT NULL DEFAULT 0.0000 COMMENT '不含税商品单价',
                                   `rate_amount` decimal(14, 4) NOT NULL DEFAULT 0.0000 COMMENT '含税总价格',
                                   `no_rate_amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '不含税总价格',
                                   `total_amount` decimal(14, 4) NOT NULL DEFAULT 0.0000 COMMENT '总价格',
                                   `is_reconciliation` tinyint NOT NULL DEFAULT 0 COMMENT '是否对账',
                                   `texture` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '材质',
                                   `tax_rate` decimal(10, 2) NULL DEFAULT NULL COMMENT '税率',
                                   `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                   `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                   PRIMARY KEY (`dtl_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '发货单项表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for orders
-- ----------------------------
DROP TABLE IF EXISTS `orders`;
CREATE TABLE `orders`  (
                           `order_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '订单id',
                           `order_sn` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '订单号',
                           `shop_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '店铺名称',
                           `shop_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '店铺id',
                           `user_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用户id',
                           `untitled` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '产品名称（多个产品用,隔开）',
                           `receiver_name` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '收货人快照',
                           `receiver_mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '收货人手机号快照',
                           `receiver_address` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '收货地址快照',
                           `total_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '订单总价格',
                           `actual_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '实际支付总价格',
                           `deal_type` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '交易类型，店铺对用户：1外->外 2内-外 3外-内 4内-内',
                           `pay_type` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '线上支付方式 1:微信 2:支付宝 3:银联 ',
                           `pay_way` tinyint NULL DEFAULT NULL COMMENT '支付方式 1线上支付 2内部结算3线下转账',
                           `order_remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '订单备注',
                           `state` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '订单状态：0草稿 1已提交 2待确认 3已确认 4待签订合同 5已签合同 6待发货 7已关闭 8 发货中 9待收货 10 已完成',
                           `order_bill_state` tinyint NULL DEFAULT NULL COMMENT '是否开发票 0初始 1已申请 2已开票',
                           `invoice_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '发票类型（1.电子发票，2 普通发票）',
                           `delivery_type` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '配送方式（0:普通物流）',
                           `delivery_flow_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物流单号',
                           `logistics_company` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物流公司',
                           `order_freight` decimal(18, 2) NULL DEFAULT NULL COMMENT '订单运费 默认可以为零，代表包邮',
                           `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                           `pay_time` datetime NULL DEFAULT NULL COMMENT '付款时间',
                           `delivery_time` datetime NULL DEFAULT NULL COMMENT '发货时间',
                           `flish_time` datetime NULL DEFAULT NULL COMMENT '确认时间',
                           `cancel_time` datetime NULL DEFAULT NULL COMMENT '取消时间',
                           `close_type` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '订单关闭类型1-超时未支付 2-退款关闭 4-买家取消 15-已通过货到付款交易',
                           `trade_no` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '支付交易号',
                           `product_type` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '商品类型：0物资 1设备 2周材（设备租赁） 3周材（设备） 4二手设备 5租赁设备 10零星采购（低值易耗品），11办公用品, 12大宗月供订单 13 大宗临采)',
                           `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                           `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                           `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                           `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                           `remarks` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                           `sort` int NULL DEFAULT NULL COMMENT '排序',
                           `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                           `version` int NOT NULL DEFAULT 1 COMMENT '乐观锁',
                           `cost_price_total` decimal(20, 2) NULL DEFAULT NULL COMMENT '总成本价',
                           `profit_price_total` decimal(30, 2) NULL DEFAULT NULL COMMENT ' 总利润',
                           `success_date` datetime NULL DEFAULT NULL COMMENT '完成时间',
                           `org_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '企业id',
                           `order_class` tinyint NOT NULL DEFAULT 1 COMMENT '订单类别（1普通订单2多供方订单）',
                           `parent_order_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '父订单id（是拆单订单这里才有值）',
                           `supplier_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商id（本地机构id）',
                           `affirm_state` tinyint NOT NULL DEFAULT 0 COMMENT '拆单供方确认状态（0默认1已确认2已拒绝）',
                           `supplier_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商名称',
                           `enterprise_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '本地机构id',
                           `enterprise_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '本地机构名称',
                           `master_affirm_state` tinyint NOT NULL DEFAULT 0 COMMENT '主订单方确认拆单状态（0默认待确认1已确认）',
                           `order_source_type` tinyint NULL DEFAULT 1 COMMENT '订单来源（1商品供应商2竞价3大宗',
                           `order_source_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '订单来源id',
                           `tax_rate` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '税率',
                           `no_rate_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '不含税总金额',
                           `bill_type` tinyint NULL DEFAULT NULL COMMENT '清单类型（1浮动价格2固定价格）',
                           `payment_week` tinyint NULL DEFAULT NULL COMMENT '货款支付周期（单位月）',
                           `out_phase_interest` decimal(10, 2) NULL DEFAULT NULL COMMENT '超期垫资利息（%）',
                           `finish_type` tinyint NULL DEFAULT 0 COMMENT '订单完成类型（0发货完成1手动完成）',
                           `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                           `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                           `out_key_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '推送日志id',
                           PRIMARY KEY (`order_id`) USING BTREE,
                           UNIQUE INDEX `uniq_order_sn`(`order_sn` ASC) USING BTREE COMMENT '订单号唯一索引'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '商品订单' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for page_info
-- ----------------------------
DROP TABLE IF EXISTS `page_info`;
CREATE TABLE `page_info`  (
                              `page_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '页面id',
                              `page_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '页面名称',
                              `product_category_ids` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '产品分类ids',
                              `product_category_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '商品分类楼层名称',
                              `enterprise_ids` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '企业ids',
                              `enterprise_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '企业楼层名称',
                              `floor_ids` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '楼层ids',
                              `floor_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '商品楼层名称',
                              `
gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                              `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                              `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                              `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                              `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                              `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                              `sort` int NULL DEFAULT NULL COMMENT '排序',
                              `state` tinyint NULL DEFAULT NULL COMMENT '状态',
                              `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                              `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                              `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                              PRIMARY KEY (`page_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for platform_fee_file
-- ----------------------------
DROP TABLE IF EXISTS `platform_fee_file`;
CREATE TABLE `platform_fee_file`  (
                                      `file_Id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '附件id',
                                      `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '附件名称',
                                      `relevance_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '关联id',
                                      `relevance_type` tinyint NOT NULL COMMENT '关联类型（1店铺缴费记录）',
                                      `url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '附件地址',
                                      `file_far_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '附件远程id',
                                      `file_type` tinyint NOT NULL DEFAULT 1 COMMENT '媒体类型 1:图片2视频3附件',
                                      `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '逻辑删除 -1: 删除 0:未删除',
                                      `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                      `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                      `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                      `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                      `remarks` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
                                      `sort` int NULL DEFAULT NULL COMMENT '排序',
                                      `mall_type` tinyint NOT NULL DEFAULT 0 COMMENT '商城类型：0物资商场, 1设备商城 ',
                                      `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                      `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                      PRIMARY KEY (`file_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '通用附件' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for platform_year_fee
-- ----------------------------
DROP TABLE IF EXISTS `platform_year_fee`;
CREATE TABLE `platform_year_fee`  (
                                      `platform_year_fee_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '平台计费id',
                                      `platform_year_fee_nu` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '平台计费编号',
                                      `shop_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '店铺id',
                                      `shop_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '店铺名称',
                                      `enterprise_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '企业id',
                                      `enterprise_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '企业名称',
                                      `serve_type` tinyint NOT NULL COMMENT '服务类型（1店铺年费2招标年费）',
                                      `payment_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '总缴费金额（废弃）',
                                      `serve_end_time` date NULL DEFAULT NULL COMMENT '服务到期时间（为null或者小于当前时间都是过期）',
                                      `out_time` tinyint NOT NULL COMMENT '服务是否未过期',
                                      `sort` int NULL DEFAULT NULL COMMENT '排序',
                                      `state` tinyint NULL DEFAULT 0 COMMENT '状态',
                                      `remarks` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
                                      `gmt_create` datetime NOT NULL COMMENT '创建时间',
                                      `gmt_modified` datetime NOT NULL COMMENT '更新时间',
                                      `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                      `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                      `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '逻辑删除 -1: 删除 0:未删除',
                                      `mall_type` tinyint NOT NULL DEFAULT 0 COMMENT '商城类型：0物资商场, 1设备商城 ',
                                      `version` int NOT NULL DEFAULT 1 COMMENT '乐观锁',
                                      `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                      `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                      PRIMARY KEY (`platform_year_fee_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '平台年费表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for platform_year_fee_dtl
-- ----------------------------
DROP TABLE IF EXISTS `platform_year_fee_dtl`;
CREATE TABLE `platform_year_fee_dtl`  (
                                          `platform_year_fee_dtl_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '平台年费明细id',
                                          `payment_record_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '缴费记录id',
                                          `payment_record_un` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '缴费记录编号',
                                          `serve_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '服务类型（1店铺年费2招标年费）',
                                          `enterprise_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '企业id',
                                          `enterprise_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '企业名称',
                                          `serve_amount` decimal(18, 2) NOT NULL COMMENT '服务金额',
                                          `serve_start_time` datetime NULL DEFAULT NULL COMMENT '服务开始时间（废弃）',
                                          `serve_end_time` datetime NULL DEFAULT NULL COMMENT '服务截止时间（废弃）',
                                          `payment_duration` int NULL DEFAULT NULL COMMENT '服务时长',
                                          `payment_duration_type` tinyint NULL DEFAULT NULL COMMENT '服务时长类型（单位）（1天2周3月4年）',
                                          `sort` int NULL DEFAULT NULL COMMENT '排序',
                                          `remarks` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
                                          `gmt_create` datetime NOT NULL COMMENT '创建时间',
                                          `gmt_modified` datetime NOT NULL COMMENT '更新时间',
                                          `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人Id',
                                          `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人名称',
                                          `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '逻辑删除 -1: 删除 0:未删除',
                                          `mall_type` tinyint NOT NULL DEFAULT 0 COMMENT '商城类型：0物资商场, 1设备商城 ',
                                          `version` int NOT NULL DEFAULT 1 COMMENT '乐观锁',
                                          `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                          `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                          PRIMARY KEY (`platform_year_fee_dtl_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '平台年费明细 （废弃）' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for platform_year_fee_record
-- ----------------------------
DROP TABLE IF EXISTS `platform_year_fee_record`;
CREATE TABLE `platform_year_fee_record`  (
                                             `payment_record_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '缴费记录id',
                                             `payment_record_un` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '缴费记录编号',
                                             `shop_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '店铺id',
                                             `shop_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '店铺名称',
                                             `enterprise_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '企业id',
                                             `enterprise_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '企业名称',
                                             `pay_amount` decimal(18, 2) NOT NULL COMMENT '缴费金额',
                                             `pay_type` tinyint NOT NULL DEFAULT 1 COMMENT '缴费类型(1线下2线上3其他)',
                                             `payment_duration` int NULL DEFAULT NULL COMMENT '缴费时长',
                                             `payment_duration_type` tinyint NULL DEFAULT NULL COMMENT '缴费时长类型（单位）（1天2周3月4年）',
                                             `audit_open_time` datetime NULL DEFAULT NULL COMMENT '审核通过时间',
                                             `record_type` tinyint NOT NULL COMMENT '记录类型（1店铺年费2招标年费）',
                                             `state` tinyint NOT NULL DEFAULT 0 COMMENT '状态（0草稿1待审核2审核通过3审核未通过）',
                                             `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                             `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间	',
                                             `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                             `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                             `sort` int NULL DEFAULT NULL COMMENT '排序',
                                             `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                             `mall_type` tinyint NOT NULL DEFAULT 0 COMMENT '商城类型：0物资商场, 1设备商城 ',
                                             `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '逻辑删除 -1: 删除 0:未删除',
                                             `version` int NOT NULL DEFAULT 1 COMMENT '乐观锁',
                                             `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                             `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                             PRIMARY KEY (`payment_record_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '年费缴费记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for private_key_supplier
-- ----------------------------
DROP TABLE IF EXISTS `private_key_supplier`;
CREATE TABLE `private_key_supplier`  (
                                         `private_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '私有id',
                                         `supplier_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '供应商id',
                                         `supplier_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '供应商名称',
                                         `supplier_type` tinyint NULL DEFAULT NULL COMMENT '供应商类型',
                                         `private_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '秘钥key',
                                         `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                         `remarks` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注说明',
                                         `shop_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '店铺id',
                                         `invalid_time` datetime NULL DEFAULT NULL COMMENT '失效时间',
                                         `ip_auth` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'ip地址',
                                         `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                         `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                         PRIMARY KEY (`private_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '供应商秘钥' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for procurement_info
-- ----------------------------
DROP TABLE IF EXISTS `procurement_info`;
CREATE TABLE `procurement_info`  (
                                     `bill_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'id',
                                     `contract_number` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
                                     `contract_name` char(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
                                     `list_type` tinyint NULL DEFAULT NULL COMMENT '清单类型：1 计划 2应急抢险 3基础库 4 招标',
                                     `server_unit_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '服务单位费用',
                                     `transport_tax_rate` decimal(18, 4) NULL DEFAULT NULL COMMENT '服务费税率',
                                     `tax_excluded_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '不含税金额',
                                     `tax_rate` decimal(18, 2) NULL DEFAULT NULL COMMENT '税率',
                                     `tax_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '税额',
                                     `contract_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '合同金额',
                                     `bond_state` bit(1) NULL DEFAULT NULL COMMENT '是否需要缴纳履约保证金: 1是 0 否',
                                     `bond_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '应交履约保证金',
                                     `currency_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '币种id',
                                     `currency_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '币种名称',
                                     `advance_charge` decimal(18, 2) NULL DEFAULT NULL COMMENT '预付款',
                                     `write_off_advance_charge` decimal(18, 2) NOT NULL COMMENT '已冲销预付款',
                                     `signing_date` date NULL DEFAULT NULL COMMENT '签订日期',
                                     `signing_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '签约人id',
                                     `signing_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '签约人',
                                     `signing_start_time` date NULL DEFAULT NULL COMMENT '签约有效期开始时间',
                                     `signing_end_time` date NULL DEFAULT NULL COMMENT '签约有效期结束时间',
                                     `address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '地址',
                                     `contract_state` tinyint NULL DEFAULT NULL COMMENT '合同状态',
                                     `financial_sharing` tinyint NOT NULL DEFAULT 0 COMMENT '传输财务共享状态',
                                     `contract_context` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '主要合同内容',
                                     `period` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '变更次数',
                                     `state` tinyint NULL DEFAULT NULL COMMENT '申请状态',
                                     `work_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '流程Id',
                                     `gmt_create` datetime NOT NULL COMMENT '创建时间',
                                     `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                     `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人Id',
                                     `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人名称',
                                     `org_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '单据机构id',
                                     `org_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '单据机构名称',
                                     `nullify_reason` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '作废原因',
                                     `nullify_description` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '作废描述',
                                     `nullify_creator_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '作废人id',
                                     `nullify_creator` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '作废人',
                                     `nullify_created` datetime NULL DEFAULT NULL COMMENT '作废时间',
                                     `base_cur_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '本位币id',
                                     `base_cur_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '本位币名称',
                                     `base_cur_rate` decimal(18, 4) NULL DEFAULT NULL COMMENT '本位币汇率',
                                     `rmb_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '人民币名称',
                                     `rmb_rate` decimal(18, 4) NULL DEFAULT NULL COMMENT '人民币汇率',
                                     `settlement_amount` decimal(18, 4) NOT NULL COMMENT '结算金额',
                                     `contract_state_before_settlement` tinyint NULL DEFAULT NULL COMMENT '终期结算前合同状态(用于终期结算单据作废或撤销后还原状态)',
                                     `is_move` bit(1) NOT NULL DEFAULT b'0' COMMENT '迁移状态',
                                     `rmb_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '人名币金额',
                                     `base_cur_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '本位币金额',
                                     `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                     `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                     PRIMARY KEY (`bill_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'pcwp2物资采购合同基本信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for procurement_info_dtl
-- ----------------------------
DROP TABLE IF EXISTS `procurement_info_dtl`;
CREATE TABLE `procurement_info_dtl`  (
                                         `bill_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
                                         `source_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '源单id',
                                         `ori_bill_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '引入的清单id',
                                         `one_level_type_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '一级类别id',
                                         `one_level_type_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '一级类别名称',
                                         `plan_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '计划id',
                                         `plan_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '计划名称',
                                         `plan_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '计划编号',
                                         `plan_type` tinyint NULL DEFAULT NULL COMMENT '计划类型',
                                         `type_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物资类别id',
                                         `type_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物资类别名称',
                                         `material_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物资类别id',
                                         `material_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
                                         `specification_model` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
                                         `texture_of_material` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
                                         `unit` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '计量单位',
                                         `quantity` decimal(18, 4) NULL DEFAULT NULL COMMENT '数量',
                                         `ori_quantity` decimal(18, 4) NULL DEFAULT NULL COMMENT '原数量',
                                         `unit_price` decimal(18, 4) NULL DEFAULT NULL COMMENT '单价',
                                         `ori_unit_price` decimal(18, 4) NULL DEFAULT NULL COMMENT '原单价',
                                         `amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '金额',
                                         `settled_quantity` decimal(18, 4) NULL DEFAULT NULL COMMENT '已收料数量',
                                         `warehousing_quantity` decimal(18, 4) NULL DEFAULT NULL COMMENT '已入库数量',
                                         `acceptance_quantity` decimal(18, 4) NULL DEFAULT NULL COMMENT '已验收数量',
                                         `is_move` bit(1) NOT NULL DEFAULT b'0' COMMENT '迁移状态',
                                         `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                         `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                         PRIMARY KEY (`bill_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'pcwp2物资采购合同清单(物资)' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for product
-- ----------------------------
DROP TABLE IF EXISTS `product`;
CREATE TABLE `product`  (
                            `product_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '商品id',
                            `serial_num` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '商品编码',
                            `product_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '商品名称',
                            `product_intro` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '商品简介',
                            `shop_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '店铺id',
                            `class_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分类id',
                            `product_describe` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '商品描述',
                            `product_type` tinyint NOT NULL DEFAULT 0 COMMENT '商品类型：0 低值易耗品 1大宗临采',
                            `state` tinyint NOT NULL COMMENT '商品状态（0待上架 1已上架 2已下架）',
                            `product_keyword` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '商品关键字（,分隔）',
                            `product_transport_type` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品运费类型（0商家包邮）',
                            `product_min_price` decimal(18, 2) NULL DEFAULT NULL COMMENT '商品的最低价',
                            `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '逻辑删除 -1: 删除 0:未删除',
                            `sort` int NULL DEFAULT NULL COMMENT '平台排序',
                            `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                            `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                            `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                            `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                            `product_inventory_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '商品库id',
                            `relevance_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '关联外部id',
                            `brand_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '品牌id',
                            `brand_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '品牌名称冗余',
                            `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                            `mall_type` tinyint NOT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                            `putaway_date` datetime NULL DEFAULT NULL COMMENT '上架时间',
                            `sold_num` decimal(18, 4) NULL DEFAULT NULL COMMENT '销量',
                            `product_visit_num` int NULL DEFAULT NULL COMMENT '商品访问量',
                            `province` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '省',
                            `city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '市',
                            `county` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '县、区',
                            `detailed_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '详细地址',
                            `longitude` decimal(10, 7) NULL DEFAULT NULL COMMENT '经度',
                            `latitude` decimal(10, 7) NULL DEFAULT NULL COMMENT '纬度',
                            `shop_sort` int NULL DEFAULT NULL COMMENT '店铺排序',
                            `synthesis_sort` int NULL DEFAULT NULL COMMENT '综合排序',
                            `product_min_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '商品小图',
                            `class_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分类路径（/分割）',
                            `relevance_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '关联名称（用于关联外部的名称唯一不修改）',
                            `relevance_no` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '关联编号',
                            `is_completion` tinyint NULL DEFAULT NULL COMMENT '是否完成商品编辑（0否1是）',
                            `leave_factory` datetime NULL DEFAULT NULL COMMENT '出厂日期',
                            `quality` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '质量、成色',
                            `is_week_materials` tinyint NOT NULL DEFAULT 0 COMMENT '是否周材',
                            `fail_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '失败原因',
                            `is_open_import` tinyint NOT NULL DEFAULT 0 COMMENT '是否外部导入商品',
                            `supper_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商冗余',
                            `is_maintain` tinyint NULL DEFAULT NULL COMMENT '是否维修',
                            `sku_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '规格型号冗余',
                            `supplier_submit_state` tinyint NOT NULL DEFAULT 0 COMMENT '提交状态（0默认1待提交2待确认3已确认4已拒绝）供方使用',
                            `supplier_submit_error` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '提交失败原因',
                            `supplier_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供方名称（冗余）',
                            `product_texture` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '商品材质',
                            `outer_product_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '商品编码（外部）',
                            `shop_type` tinyint NULL DEFAULT NULL COMMENT '商品所属店铺类型（1自营店铺2内部店铺3外部店铺）',
                            `show_state` tinyint NOT NULL DEFAULT 0 COMMENT '显示状态（0显示1不显示）默认0',
                            `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                            `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                            `out_key_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '（特使字段，切换自营店使用）自营店商品id',
                            `tax_rate` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '税率',
                            PRIMARY KEY (`product_id`) USING BTREE,
                            UNIQUE INDEX `serial_num`(`serial_num` ASC) USING BTREE,
                            INDEX `modifyTimeIndex`(`gmt_modified` ASC) USING BTREE,
                            INDEX `synIdnex`(`synthesis_sort` ASC) USING BTREE,
                            INDEX `classIdIndex`(`class_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '店铺商品信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for product_attribute
-- ----------------------------
DROP TABLE IF EXISTS `product_attribute`;
CREATE TABLE `product_attribute`  (
                                      `attribute_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '属性id',
                                      `attribute_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '属性名称',
                                      `attribute_value` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '可选值列表[用逗号分隔]',
                                      `attribute_type` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '属性类型[0-销售属性，1-基本属性]',
                                      `sort` int NULL DEFAULT NULL COMMENT '排序',
                                      `state` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '启用状态[0 - 停用，1 - 启用]',
                                      `parent_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '父级id',
                                      `class_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分类id',
                                      `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                      `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                      `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                      `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                      `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                                      `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                      `product_type` tinyint NULL DEFAULT NULL COMMENT '商品类型：0物资 1设备 2周材（物资） 3周材（设备） 4二手设备 5租赁设备',
                                      `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                                      `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                      `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                      PRIMARY KEY (`attribute_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '商品属性' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for product_attribute_value
-- ----------------------------
DROP TABLE IF EXISTS `product_attribute_value`;
CREATE TABLE `product_attribute_value`  (
                                            `sku_attribute_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '商品属性id',
                                            `product_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '商品id',
                                            `attribute_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '属性id',
                                            `attribute_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '属性名',
                                            `attribute_value` varchar(1500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '属性值',
                                            `sort` int NULL DEFAULT NULL COMMENT '排序',
                                            `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                            `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                            `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                            `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                            `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                                            `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                            `product_type` tinyint NULL DEFAULT NULL COMMENT '商品类型：0物资 1设备 2周材（物资） 3周材（设备） 4二手设备 5租赁设备',
                                            `state` tinyint NULL DEFAULT NULL COMMENT '状态',
                                            `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                                            `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                            `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                            PRIMARY KEY (`sku_attribute_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '商品属性值' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for product_category
-- ----------------------------
DROP TABLE IF EXISTS `product_category`;
CREATE TABLE `product_category`  (
                                     `class_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '分类id',
                                     `class_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '分类名称',
                                     `class_keyword` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分类关键字',
                                     `class_level` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '分类层级 1:一级大分类 2:二级分类 3:三级小分类',
                                     `parent_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '父层级id',
                                     `class_icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '图标 logo',
                                     `class_bg_color` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '背景颜色',
                                     `sort` int NULL DEFAULT NULL COMMENT '排序',
                                     `class_describe` varchar(400) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分类的描述',
                                     `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                                     `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                     `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                     `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                     `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                     `state` tinyint NULL DEFAULT 0 COMMENT '状态（1启用0停用）',
                                     `product_type` tinyint NULL DEFAULT NULL COMMENT '商品类型：0物资 1设备 2周材（物资） 3周材（设备） 4二手设备 5租赁设备 6维修服务',
                                     `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                     `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                                     `is_exhibition` tinyint NULL DEFAULT NULL COMMENT '是否展示(1是，0否)',
                                     `is_have_product` tinyint NULL DEFAULT NULL COMMENT '是否有商品(1有，0没有)',
                                     `class_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分类路径',
                                     `class_no` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分类编号',
                                     `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                     `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                     PRIMARY KEY (`class_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '商品分类' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for product_collect
-- ----------------------------
DROP TABLE IF EXISTS `product_collect`;
CREATE TABLE `product_collect`  (
                                    `collect_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '收藏id',
                                    `user_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用户id',
                                    `product_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '关注id（商品id,店铺id）',
                                    `collect_type` tinyint NULL DEFAULT NULL COMMENT '关注类型 ( 1:商品  2：店铺  3:需求)',
                                    `product_type` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '商品类型：0物资 1设备 2周材（物资） 3周材（设备） 4二手设备 5租赁设备',
                                    `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间	',
                                    `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间	',
                                    `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                    `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                    `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                    `sort` int NULL DEFAULT NULL COMMENT '排序',
                                    `state` tinyint NULL DEFAULT NULL COMMENT '状态（1：启用 0： 停用）',
                                    `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：（ 0物资商场  1设备商城 ）',
                                    `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除：（ -1: 删除  0:未删除）',
                                    `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                    `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                    PRIMARY KEY (`collect_id`) USING BTREE,
                                    INDEX `product_id`(`product_id` ASC) USING BTREE,
                                    INDEX `user_id`(`user_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '个人商品收藏' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for product_comment
-- ----------------------------
DROP TABLE IF EXISTS `product_comment`;
CREATE TABLE `product_comment`  (
                                    `comment_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '评论id',
                                    `product_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '商品id',
                                    `user_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '评论用户id',
                                    `parent_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '父评论id',
                                    `order_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '订单id',
                                    `order_item_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '订单项id',
                                    `product_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '商品名称',
                                    `is_anonymous` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '是否匿名（1:是，0:否）',
                                    `comment_type` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '评价类型（1好评，2中评，3差评）',
                                    `comment_level` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '评价分数',
                                    `comment_content` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '评价内容',
                                    `comment_imgs` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '评价晒图(JSON {img1:url1,img2:url2}  )',
                                    `evaluate_time` datetime NULL DEFAULT NULL COMMENT '评价时间',
                                    `is_reply` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '是否回复（0:未回复，1:已回复）',
                                    `reply_content` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '回复内容',
                                    `reply_time` datetime NULL DEFAULT NULL COMMENT '回复时间',
                                    `is_show` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '是否显示（1:是，0:否）',
                                    `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                                    `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                    `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                    `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                    `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                    `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                    `product_type` tinyint NULL DEFAULT NULL COMMENT '商品类型：0物资 1设备 2周材（物资） 3周材（设备） 4二手设备 5租赁设备',
                                    `sort` int NULL DEFAULT NULL COMMENT '排序',
                                    `state` tinyint NULL DEFAULT NULL COMMENT '状态',
                                    `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                                    `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                    `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                    PRIMARY KEY (`comment_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '商品评价' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for product_compare
-- ----------------------------
DROP TABLE IF EXISTS `product_compare`;
CREATE TABLE `product_compare`  (
                                    `compare_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键',
                                    `enterprise_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '比价人企业id',
                                    `enterprise_name` varchar(125) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '比价人企业名称（公司名称、供应商公司名称 ）',
                                    `compare_time` datetime NULL DEFAULT NULL COMMENT '比价提交时间',
                                    `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                    `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                    `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                    `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '比价人Id',
                                    `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '比价人名称',
                                    `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                                    `sort` int NULL DEFAULT NULL COMMENT '排序',
                                    `state` tinyint NULL DEFAULT NULL COMMENT '状态',
                                    `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                                    `product_names` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '比价的商品名称（使用/分割）',
                                    `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                    `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                    PRIMARY KEY (`compare_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for product_compare_item
-- ----------------------------
DROP TABLE IF EXISTS `product_compare_item`;
CREATE TABLE `product_compare_item`  (
                                         `compare_item_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键',
                                         `compare_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '比价主表ID',
                                         `product_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '比价商品id',
                                         `product_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '物资名称',
                                         `sku_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'skuid',
                                         `sku_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '规格型号',
                                         `shop_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '店铺id',
                                         `shop_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
                                         `product_min_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '商品小图片',
                                         `sell_price` decimal(18, 2) NULL DEFAULT NULL COMMENT '销售价格',
                                         `unit` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'sku单位',
                                         `brand_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '品牌id',
                                         `brand_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '品牌名称',
                                         `supper_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商id',
                                         `supplier_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供方名称',
                                         `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                         `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                         `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                         `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '比价人Id',
                                         `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '比价人名称',
                                         `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                                         `sort` int NULL DEFAULT NULL COMMENT '排序',
                                         `state` tinyint NULL DEFAULT NULL COMMENT '状态',
                                         `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                                         `is_internal_settlement` int NULL DEFAULT NULL COMMENT '是否支持内部结算：1：是  0：否',
                                         `is_business` int NULL DEFAULT 0 COMMENT '是否自营：1：是  0：否（默认：0）',
                                         `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                         `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                         PRIMARY KEY (`compare_item_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for product_inventory
-- ----------------------------
DROP TABLE IF EXISTS `product_inventory`;
CREATE TABLE `product_inventory`  (
                                      `product_inventory_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '商品库存id',
                                      `product_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '商品名称',
                                      `class_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '类别id',
                                      `class_name` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '类别名称',
                                      `class_name_path` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '类别名称路径',
                                      `product_describe` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '商品描述',
                                      `product_type` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '商品类型：0物资 1设备 2周材（物资） 3周材（设备） 4二手设备 5租赁设备',
                                      `spec` char(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '规格',
                                      `unit` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '计量单位',
                                      `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                                      `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                      `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                      `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                      `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                      `relevance_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '关联外部id',
                                      `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                      `sort` int NULL DEFAULT NULL COMMENT '排序',
                                      `state` tinyint NULL DEFAULT NULL COMMENT '状态（1启用0停用）',
                                      `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                                      `relevance_no` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '关联外部编号',
                                      `product_title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '商品标题',
                                      `spec_title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '规格',
                                      `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                      `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                      PRIMARY KEY (`product_inventory_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '商品库' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for product_sku
-- ----------------------------
DROP TABLE IF EXISTS `product_sku`;
CREATE TABLE `product_sku`  (
                                `sku_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'skuid',
                                `product_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '商品id',
                                `sku_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'sku名称',
                                `sku_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'sku图片',
                                `cost_price` decimal(18, 2) NULL DEFAULT 0.00 COMMENT '成本价',
                                `original_price` decimal(18, 2) NULL DEFAULT 0.00 COMMENT '原价',
                                `sell_price` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '销售价格',
                                `discounts` decimal(18, 2) NULL DEFAULT NULL COMMENT '折扣力度',
                                `stock` decimal(18, 4) NULL DEFAULT NULL COMMENT '库存',
                                `state` tinyint NULL DEFAULT NULL COMMENT 'sku状态(1启用，0停用)',
                                `unit` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'sku单位',
                                `sold_num` decimal(18, 4) NULL DEFAULT NULL COMMENT '销量',
                                `sku_title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'sku标题',
                                `sku_subtitle` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'sku副标题',
                                `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                                `brand_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '品牌id',
                                `sort` int NULL DEFAULT NULL COMMENT '排序',
                                `product_type` tinyint NULL DEFAULT NULL COMMENT '商品类型：0物资 1设备 2周材（物资） 3周材（设备） 4二手设备 5租赁设备',
                                `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                                `lease_unit` char(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '租赁单位（天月年）',
                                `settle_price` decimal(18, 2) NULL DEFAULT NULL COMMENT '结算价',
                                `s_cost_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '供方成本价',
                                `s_sell_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '供方推荐销售价',
                                `second_unit` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '临采副级sku单位',
                                `second_unit_num` decimal(18, 4) NULL DEFAULT NULL COMMENT '临采副级sku单位系数',
                                `is_zone` tinyint NOT NULL DEFAULT 1 COMMENT '1 全区域  2区域',
                                `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                PRIMARY KEY (`sku_id`) USING BTREE,
                                INDEX `productIdIndex`(`product_id` ASC) USING BTREE,
                                INDEX `sellPriceIndex`(`sell_price` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '商品sku' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for product_zone
-- ----------------------------
DROP TABLE IF EXISTS `product_zone`;
CREATE TABLE `product_zone`  (
                                 `zone_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '区域id',
                                 `zone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '区域名称',
                                 `zone_keyword` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '区域关键字',
                                 `zone_level` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '分类层级 1:一级大分类 2:二级分类 3:三级小分类',
                                 `parent_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '父层级id',
                                 `sort` int NULL DEFAULT NULL COMMENT '排序',
                                 `class_describe` varchar(400) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分类的描述',
                                 `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                                 `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                 `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                 `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                 `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                 `state` tinyint NULL DEFAULT 0 COMMENT '状态（1启用0停用）',
                                 `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                 `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                                 `zone_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '区域路径',
                                 `zone_no` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '区域编号',
                                 `is_exhibition` tinyint NULL DEFAULT NULL COMMENT '是否展示(1是，0否)',
                                 `is_have_product` tinyint NULL DEFAULT NULL COMMENT '是否有商品(1有，0没有)',
                                 `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                 `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                 PRIMARY KEY (`zone_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for product_zone_price
-- ----------------------------
DROP TABLE IF EXISTS `product_zone_price`;
CREATE TABLE `product_zone_price`  (
                                       `product_zone_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
                                       `sku_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
                                       `zone_addr` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '详细地址',
                                       `zone_price` decimal(18, 2) NULL DEFAULT NULL COMMENT '商品区域价格',
                                       `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                       `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                       `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                       `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                       `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                                       `sort` int NULL DEFAULT NULL COMMENT '排序',
                                       `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                                       `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                       `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                       `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                       PRIMARY KEY (`product_zone_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for receipt_person
-- ----------------------------
DROP TABLE IF EXISTS `receipt_person`;
CREATE TABLE `receipt_person`  (
                                   `receipt_person_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '收料人员id',
                                   `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '姓名',
                                   `phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '电话',
                                   `open_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'wx的openId',
                                   `local_org_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '本地机构名称',
                                   `local_org_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '本地机构id',
                                   `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                   `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                   `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                   `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                   `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                   `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                                   `sort` int NULL DEFAULT NULL COMMENT '排序',
                                   `state` tinyint NULL DEFAULT NULL COMMENT '状态（0停用1启用）',
                                   `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                                   `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                   `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                   PRIMARY KEY (`receipt_person_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '收料人员电话表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for role
-- ----------------------------
DROP TABLE IF EXISTS `role`;
CREATE TABLE `role`  (
                         `role_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类型id',
                         `role_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用于查询',
                         `role_name` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '角色名称',
                         `is_selective` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '是否可选   1：可选 0：不可选',
                         `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                         `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                         `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                         `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                         `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                         `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                         `sort` int NULL DEFAULT NULL COMMENT '排序',
                         `state` tinyint NULL DEFAULT NULL COMMENT '状态',
                         `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                         `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                         `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                         PRIMARY KEY (`role_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '商城角色表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sheel
-- ----------------------------
DROP TABLE IF EXISTS `sheel`;
CREATE TABLE `sheel`  (
                          `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
                          `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                          `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for shop
-- ----------------------------
DROP TABLE IF EXISTS `shop`;
CREATE TABLE `shop`  (
                         `shop_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '店铺id',
                         `shop_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '店铺名称',
                         `shop_balance` decimal(18, 2) NULL DEFAULT NULL COMMENT '店铺余额',
                         `shop_freeze_money` decimal(18, 2) NULL DEFAULT NULL COMMENT '店铺冻结的资金',
                         `shop_type` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '店铺类型 0：个体户  1：企业  2：个人',
                         `ad_Img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '广告图',
                         `shop_img` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '店铺log',
                         `province` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '省',
                         `city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '市',
                         `county` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '县、区',
                         `detailed_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '详细地址',
                         `longitude` decimal(10, 7) NULL DEFAULT NULL COMMENT '经度',
                         `latitude` decimal(10, 7) NULL DEFAULT NULL COMMENT '纬度',
                         `state` tinyint NOT NULL DEFAULT 1 COMMENT '店铺状态 1:启用 0停用',
                         `shop_describle` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '店铺简介',
                         `enterprise_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '企业id',
                         `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                         `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                         `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                         `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                         `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除： -1: 删除 0:未删除',
                         `is_business` tinyint NOT NULL DEFAULT 0 COMMENT '是否自营：1：是  0：否（默认：0）',
                         `is_supplier` tinyint NULL DEFAULT NULL COMMENT '是否为供应商：1： 是  0：否',
                         `is_internal_shop` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否内部店铺：1：是  0：否',
                         `is_internal_settlement` tinyint NOT NULL DEFAULT 1 COMMENT '是否支持内部结算：1：是  0：否',
                         `audit_status` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '店铺审核状态： 1：审核通过  2：未审核  3：审核未通过 ',
                         `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                         `sort` int NULL DEFAULT NULL COMMENT '排序',
                         `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                         `main_business` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '主营业务',
                         `open_date` datetime NULL DEFAULT NULL COMMENT '开店时间（审核通过时间）',
                         `serial_num` char(36) CHARACTER SET koi8u COLLATE koi8u_general_ci NULL DEFAULT NULL COMMENT '序列号',
                         `link_man` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系人',
                         `initial` char(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '店铺名称首字母',
                         `contact_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系电话',
                         `synthesis_sort` int NULL DEFAULT NULL COMMENT '综合排序值',
                         `fail_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '审核失败原因',
                         `is_index_show` tinyint NOT NULL DEFAULT 0 COMMENT '是否首页显示',
                         `is_other_auth` varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '其他服务权限（JSON）',
                         `audit_pass_time` datetime NULL DEFAULT NULL COMMENT '店铺审核通过时间',
                         `return_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '退货地址',
                         `return_relation_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '退货负责人',
                         `return_relation_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '退货联系电话',
                         `shop_class` tinyint NOT NULL DEFAULT 1 COMMENT '店铺类别（1普通店铺2多供方店铺）',
                         `is_principal_material` tinyint NOT NULL DEFAULT 0 COMMENT '是否能提供主材（0否1是默认0）',
                         `shu_dao_flag` tinyint NULL DEFAULT 0 COMMENT '是否为蜀道企业或者内部(1是，0否)',
                         `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                         `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                         `is_arrearage` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '是否欠费',
                         PRIMARY KEY (`shop_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '店铺' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for shop_business
-- ----------------------------
DROP TABLE IF EXISTS `shop_business`;
CREATE TABLE `shop_business`  (
                                  `shop_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '店铺id',
                                  `shop_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '店铺名称',
                                  `state` tinyint NOT NULL DEFAULT 1 COMMENT '店铺状态 1:启用 0停用',
                                  `enterprise_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '企业名称',
                                  `interior_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '远程机构id',
                                  `enterprise_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '企业id',
                                  `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                  `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                  `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除： -1: 删除 0:未删除',
                                  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                  `sort` int NULL DEFAULT NULL COMMENT '排序',
                                  `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                                  `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                  `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                  PRIMARY KEY (`shop_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for shop_role_association
-- ----------------------------
DROP TABLE IF EXISTS `shop_role_association`;
CREATE TABLE `shop_role_association`  (
                                          `id` int NOT NULL AUTO_INCREMENT COMMENT '店铺角色关联id',
                                          `shop_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '店铺id',
                                          `role_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '角色id',
                                          `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                          `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间	',
                                          `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                          `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                          `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                          `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                                          `sort` int NULL DEFAULT NULL COMMENT '排序',
                                          `state` tinyint NULL DEFAULT NULL COMMENT '状态',
                                          `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                                          `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                          `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                          PRIMARY KEY (`id`) USING BTREE,
                                          INDEX `shop_id`(`shop_id` ASC) USING BTREE,
                                          INDEX `role_id`(`role_id` ASC) USING BTREE,
                                          CONSTRAINT `role_id` FOREIGN KEY (`role_id`) REFERENCES `role` (`role_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
                                          CONSTRAINT `shop_id` FOREIGN KEY (`shop_id`) REFERENCES `shop` (`shop_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 4873 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '店铺—角色类型关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for shop_supplier_rele
-- ----------------------------
DROP TABLE IF EXISTS `shop_supplier_rele`;
CREATE TABLE `shop_supplier_rele`  (
                                       `shop_supplier_rele_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '店铺供应商关联表id',
                                       `shop_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '店铺id',
                                       `supplier_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商id（本地机构id）',
                                       `supplier_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商名称',
                                       `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间	',
                                       `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间	',
                                       `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                       `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                       `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                       `sort` int NULL DEFAULT NULL COMMENT '排序',
                                       `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：（ 0物资商场  1设备商城 ）',
                                       `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除：（ -1: 删除  0:未删除）',
                                       `list_permissions` tinyint NOT NULL DEFAULT 0 COMMENT '上架商品权限(0无权限，1有权限)',
                                       `permissions_commodities` tinyint NOT NULL DEFAULT 0 COMMENT '大宗商品权限(0无权限，1有权限)',
                                       `permissions_low_value` tinyint NOT NULL DEFAULT 0 COMMENT '低值易耗商品权限(0无权限，1有权限)',
                                       `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                       `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                       PRIMARY KEY (`shop_supplier_rele_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '店铺供方关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for shopping_cart
-- ----------------------------
DROP TABLE IF EXISTS `shopping_cart`;
CREATE TABLE `shopping_cart`  (
                                  `cart_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '购物车id',
                                  `shop_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '店铺id',
                                  `product_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '商品id',
                                  `sku_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'skuid',
                                  `user_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户id',
                                  `cart_num` decimal(18, 4) NULL DEFAULT NULL COMMENT '购物车商品数量',
                                  `cart_time` datetime NULL DEFAULT NULL COMMENT '添加购物车时间',
                                  `product_price` decimal(18, 2) NOT NULL COMMENT '添加购物车时商品价格',
                                  `sku_props` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '选择的规格属性',
                                  `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除 1: 删除 0:未删除',
                                  `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                  `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                  `product_type` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '商品类型：0物资 1设备 2周材（物资） 3周材（设备） 4二手设备 5租赁设备',
                                  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                  `sort` int NULL DEFAULT NULL COMMENT '排序',
                                  `state` tinyint NULL DEFAULT NULL COMMENT '状态',
                                  `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                                  `cart_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '购物车图片',
                                  `lease_num` decimal(18, 4) NOT NULL DEFAULT 1.0000 COMMENT '租赁时长',
                                  `lease_unit` char(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '租赁单位（天月年）',
                                  `checked` tinyint NULL DEFAULT 0 COMMENT '是否选中，1选中0不选中',
                                  `zone_addr` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '区域地址',
                                  `zone_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '区域Id',
                                  `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                  `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                  `tax_rate` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '税率',
                                  PRIMARY KEY (`cart_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '购物车' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for shudao_enterprise
-- ----------------------------
DROP TABLE IF EXISTS `shudao_enterprise`;
CREATE TABLE `shudao_enterprise`  (
                                      `shudao_enterprise_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '蜀道企业ID',
                                      `affiliation_enterprise` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '隶属企业',
                                      `enterprise_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '公司名称',
                                      `enterprise_category` tinyint NULL DEFAULT NULL COMMENT '企业类别(1:一类 2:二类 3:三类)',
                                      `adjust` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '调整',
                                      `enterprise_nature` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '企业性质',
                                      `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间	',
                                      `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间	',
                                      `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                      `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                      `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                      `sort` int NULL DEFAULT NULL COMMENT '排序',
                                      `state` tinyint NULL DEFAULT NULL COMMENT '状态',
                                      `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型',
                                      `is_delete` tinyint NULL DEFAULT 0 COMMENT '逻辑删除：（ -1: 删除  0:未删除）',
                                      `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                      `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                      PRIMARY KEY (`shudao_enterprise_id`) USING BTREE,
                                      UNIQUE INDEX `1`(`enterprise_name` ASC) USING BTREE COMMENT '企业名称不重复'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sku_sale_attribute_value
-- ----------------------------
DROP TABLE IF EXISTS `sku_sale_attribute_value`;
CREATE TABLE `sku_sale_attribute_value`  (
                                             `sku_sale_attribute_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'sku销售id',
                                             `sku_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'sku_id',
                                             `attribute_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '属性id',
                                             `attribute_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '销售属性名',
                                             `attribute_value` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '销售属性值',
                                             `sort` int NULL DEFAULT NULL COMMENT '排序',
                                             `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                             `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                             `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                             `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                             `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                                             `state` tinyint NULL DEFAULT NULL COMMENT '状态',
                                             `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                             `product_type` tinyint NULL DEFAULT NULL COMMENT '商品类型：0物资 1设备 2周材（物资） 3周材（设备） 4二手设备 5租赁设备',
                                             `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                                             `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                             `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                             PRIMARY KEY (`sku_sale_attribute_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'sku销售属性&值' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for station_message
-- ----------------------------
DROP TABLE IF EXISTS `station_message`;
CREATE TABLE `station_message`  (
                                    `station_message_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '消息id',
                                    `send_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '发件人id',
                                    `send_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '发件人名称',
                                    `send_type` tinyint NULL DEFAULT NULL COMMENT '发件人类型（0店铺1用户2平台）',
                                    `send_code` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '发件人消息账号',
                                    `all_read` tinyint NULL DEFAULT NULL COMMENT '是否全部已读(0否1是)',
                                    `is_file` tinyint NULL DEFAULT NULL COMMENT '是否有附件(0否1是)',
                                    `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '标题',
                                    `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '内容',
                                    `send_date` datetime NULL DEFAULT NULL COMMENT '发送时间',
                                    `message_type` tinyint NULL DEFAULT NULL COMMENT '消息类型',
                                    `state` tinyint NULL DEFAULT NULL COMMENT '状态',
                                    `sort` int NULL DEFAULT NULL COMMENT '排序',
                                    `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '逻辑删除 -1: 删除 0:未删除',
                                    `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                    `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                    `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                    `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                    `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                    `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                                    `remind` tinyint NULL DEFAULT 0 COMMENT '是否弹窗提醒(0否，1是)',
                                    `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                    `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                    PRIMARY KEY (`station_message_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '站点消息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for station_message_receive
-- ----------------------------
DROP TABLE IF EXISTS `station_message_receive`;
CREATE TABLE `station_message_receive`  (
                                            `station_message_receive_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '消息接收id',
                                            `receive_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '收件人id',
                                            `receive_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '收件人名称',
                                            `receive_type` tinyint NULL DEFAULT NULL COMMENT '收件人类型（0店铺1用户2平台）',
                                            `receive_code` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '收件人消息账号',
                                            `is_read` tinyint NULL DEFAULT NULL COMMENT '是否已读（0否1是）',
                                            `read_date` datetime NULL DEFAULT NULL COMMENT '已读时间',
                                            `sort` int NULL DEFAULT NULL COMMENT '排序',
                                            `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                                            `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                            `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                            `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                            `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                            `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                            `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                                            `station_message_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '消息id',
                                            `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                            `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                            PRIMARY KEY (`station_message_receive_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '站点接收消息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for statistical_num
-- ----------------------------
DROP TABLE IF EXISTS `statistical_num`;
CREATE TABLE `statistical_num`  (
                                    `id` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
                                    `statistical_Date` datetime NULL DEFAULT NULL COMMENT '统计时间',
                                    `supplier_num` int NULL DEFAULT NULL COMMENT '供应商数',
                                    `shop_num` int NULL DEFAULT NULL COMMENT '店铺数',
                                    `in_enterPrise` int NULL DEFAULT NULL COMMENT '内部企业数',
                                    `in_user_counts` int NULL DEFAULT NULL COMMENT '内部用户数',
                                    `user_num` int NULL DEFAULT NULL COMMENT '用户数',
                                    `orders_total_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '订单金额',
                                    `product_num` int NULL DEFAULT NULL COMMENT '商品总数',
                                    `up_product_num` int NULL DEFAULT NULL COMMENT '商品上架总数',
                                    `orders_num` int NULL DEFAULT NULL COMMENT '零星采购订单数',
                                    `order_ship_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '零星采购发货金额',
                                    `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                    `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '物资数据统计' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for statistics_data
-- ----------------------------
DROP TABLE IF EXISTS `statistics_data`;
CREATE TABLE `statistics_data`  (
                                    `statistics_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '统计id',
                                    `founder_id` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '当前用户Id',
                                    `founder_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '当前用户',
                                    `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                    `sort` int NULL DEFAULT NULL COMMENT '排序',
                                    `remarks` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
                                    `shop_num` bigint NULL DEFAULT NULL COMMENT '店铺数',
                                    `order_ship_total_amount` decimal(18, 3) NOT NULL DEFAULT 0.000 COMMENT '零星采购发货金额',
                                    `orders_num` bigint NULL DEFAULT NULL COMMENT '零星采购订单数',
                                    `enterprise_num` bigint NOT NULL COMMENT '企业数据',
                                    `up_product_num` bigint NULL DEFAULT NULL COMMENT '商品上架总数',
                                    `product_num` bigint NULL DEFAULT NULL COMMENT '商品总数',
                                    `orders_total_amount` decimal(18, 3) NOT NULL DEFAULT 0.000 COMMENT '订单金额',
                                    `user_num` bigint NULL DEFAULT NULL COMMENT '用户数',
                                    `supplier_num` int NULL DEFAULT NULL COMMENT '供货商数',
                                    `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                    `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                    PRIMARY KEY (`statistics_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for supplier_reconciliation
-- ----------------------------
DROP TABLE IF EXISTS `supplier_reconciliation`;
CREATE TABLE `supplier_reconciliation`  (
                                            `bill_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '对账单ID',
                                            `bill_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '对账单编号',
                                            `business_type` tinyint NULL DEFAULT NULL COMMENT '业务类型（1发货2退货）',
                                            `two_supplier_org_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '二级供应商id',
                                            `two_supplier_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '二级供应商名称',
                                            `supplier_org_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商机构id',
                                            `supplier_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商名称',
                                            `no_rate_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '对账总金额（不含税）',
                                            `rate_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '对账总金额（含税）',
                                            `start_time` datetime NULL DEFAULT NULL COMMENT '对账开始时间',
                                            `end_time` datetime NULL DEFAULT NULL COMMENT '对账结束时间',
                                            `create_type` tinyint NULL DEFAULT NULL COMMENT '新增来源（1供应商新增2二级供应商新增)',
                                            `two_supplier_is_affirm` tinyint NULL DEFAULT 0 COMMENT '二级供应商是否确认（0否1是）',
                                            `two_supplier_affirm_time` datetime NULL DEFAULT NULL COMMENT '二级供应商确认时间',
                                            `reconciliation_product_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '对账结算类型（ 10零星采购（低值易耗品）11办公用品 12大宗月供订单）',
                                            `supplier_affirm_time` datetime NULL DEFAULT NULL COMMENT '供应商确认时间',
                                            `supplier_is_affirm` tinyint NOT NULL DEFAULT 0 COMMENT '供应商是否确认（0否1是）',
                                            `tax_rate` decimal(18, 2) NULL DEFAULT NULL COMMENT '税率(%)',
                                            `nullify_reason` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '作废原因',
                                            `nullify_creator_org_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '作废人机构id',
                                            `nullify_creator_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '作废人id',
                                            `nullify_creator` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '作废人',
                                            `nullify_created` datetime NULL DEFAULT NULL COMMENT '作废时间',
                                            `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                            `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                            `state` tinyint NULL DEFAULT 0 COMMENT '状态（0草稿1待提交2待审核3审核通过4审核失败7作废）',
                                            `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人id',
                                            `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                            `sort` int NULL DEFAULT NULL COMMENT '排序',
                                            `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                            `is_delete` tinyint NULL DEFAULT 0 COMMENT '逻辑删除 -1: 删除 0:未删除',
                                            `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                                            `order_sn` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '订单编号',
                                            `order_id` char(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '订单id',
                                            `source_bill_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '对账单ID',
                                            `submit_time` datetime NULL DEFAULT NULL COMMENT '自营店提交时间',
                                            `two_submit_time` datetime NULL DEFAULT NULL COMMENT '二级供应商提交时间',
                                            `submit_user_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人id',
                                            `submit_user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                            `type` tinyint NULL DEFAULT NULL COMMENT '对账类型（1浮动价格对账单2固定价格对账单）',
                                            `invoice_state` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '发票状态',
                                            `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                            `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                            `tax_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '税额',
                                            PRIMARY KEY (`bill_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '物资验收' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for supplier_reconciliation_dtl
-- ----------------------------
DROP TABLE IF EXISTS `supplier_reconciliation_dtl`;
CREATE TABLE `supplier_reconciliation_dtl`  (
                                                `dtl_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '对账明细ID',
                                                `bill_no` varchar(233) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '对账编号',
                                                `bill_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '对账ID',
                                                `order_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '订单id',
                                                `order_sn` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '订单编号',
                                                `order_item_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '订单项id',
                                                `material_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物资id',
                                                `material_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物资名称',
                                                `spec` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '规格型号',
                                                `unit` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '计量单位',
                                                `texture` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '材质',
                                                `no_rate_price` decimal(18, 2) NULL DEFAULT NULL COMMENT '对账单价（不含税）',
                                                `rate_price` decimal(18, 2) NULL DEFAULT NULL COMMENT '对账单价（含税）',
                                                `price` decimal(18, 2) NULL DEFAULT 0.00 COMMENT '对账单价（含税）',
                                                `quantity` decimal(18, 4) NULL DEFAULT 0.0000 COMMENT '对账数量',
                                                `no_rate_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '对账总金额（不含税）',
                                                `rate_amount` decimal(18, 2) NULL DEFAULT 0.00 COMMENT '对账总金额（含税）',
                                                `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人id',
                                                `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                                `sort` int NULL DEFAULT NULL COMMENT '排序',
                                                `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                                `is_delete` tinyint NULL DEFAULT 0 COMMENT '逻辑删除 -1: 删除 0:未删除',
                                                `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                                                `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                                `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                                `material_class_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物资类别id(1级类别id/2级类别id/..)',
                                                `material_class_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物资类别名称(1级类别名称/2级类别名称/..)',
                                                `total_amount` decimal(18, 2) NULL DEFAULT 0.00 COMMENT '对账总金额',
                                                `source_dtl_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '源单ID',
                                                `is_reconciliation` tinyint NOT NULL DEFAULT 0 COMMENT '是否对账',
                                                `business_type` tinyint NULL DEFAULT NULL COMMENT '业务类型（1发货单对账，2退货单对账）',
                                                `state` tinyint NULL DEFAULT NULL COMMENT '业务类型（1发货单对账，2退货单对账）',
                                                `receiving_date` datetime NULL DEFAULT NULL COMMENT '原单日期',
                                                `reconciliation_type` tinyint NULL DEFAULT NULL COMMENT '业务类型（1发货单对账，2退货单对账）',
                                                `net_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '网价（浮动价格使用）',
                                                `fixation_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '固定费用（浮动价格使用）',
                                                `out_factory_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '出厂价（固定价格使用）',
                                                `transport_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '运杂费（固定价格使用）',
                                                `product_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '商品名称',
                                                `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                                `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                                `tax_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '税额',
                                                PRIMARY KEY (`dtl_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '物资验收明细' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for synthesize_temporary
-- ----------------------------
DROP TABLE IF EXISTS `synthesize_temporary`;
CREATE TABLE `synthesize_temporary`  (
                                         `synthesize_temporary_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '大宗临采单id',
                                         `synthesize_temporary_sn` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '大宗临采单编号',
                                         `org_far_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '采购单位内部id',
                                         `org_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '采购单位id',
                                         `org_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '采购单位名称',
                                         `supplier_org_far_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商内部机构id',
                                         `supplier_org_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '供应商机构id',
                                         `supplier_org_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '供应商机构名称',
                                         `supplier_credit_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商信用代码',
                                         `supplier_short_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商机构简码',
                                         `province` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '省',
                                         `city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '市',
                                         `county` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '县、区',
                                         `receiver_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '项目收货地址',
                                         `bill_type` tinyint NULL DEFAULT NULL COMMENT '清单类型（1浮动价格2固定价格）',
                                         `reference_sum_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '参考总金额（不会变化）',
                                         `synthesize_sum_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '综合总金额（会变化，网价+固定费+管理费）',
                                         `submit_time` datetime NULL DEFAULT NULL COMMENT '提交时间',
                                         `audit_time` datetime NULL DEFAULT NULL COMMENT '审核时间',
                                         `state` tinyint NOT NULL DEFAULT 0 COMMENT '状态（0草稿1已提交2供应商已确认待审核3审核通过4审核不通过5收货方拒绝6已推送大宗临购计划）',
                                         `org_is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '采购单位是否删除（0否1是）这个删除是指供应商已确认后删除操作，采购单位不可见',
                                         `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间	',
                                         `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间	',
                                         `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                         `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                         `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                         `sort` int NULL DEFAULT NULL COMMENT '排序',
                                         `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型',
                                         `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '逻辑删除：（ -1: 删除  0:未删除）',
                                         `out_phase_interest` decimal(10, 2) NULL DEFAULT NULL COMMENT '超期垫资利息（%）',
                                         `payment_week` tinyint NULL DEFAULT NULL COMMENT '货款支付周期（单位月）',
                                         `refuse_res` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '收货单位拒绝原因',
                                         `bid_status` tinyint NULL DEFAULT 0 COMMENT '是否已生成竞价（0否1是）',
                                         `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                         `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                         PRIMARY KEY (`synthesize_temporary_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '大宗临采单' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for synthesize_temporary_dtl
-- ----------------------------
DROP TABLE IF EXISTS `synthesize_temporary_dtl`;
CREATE TABLE `synthesize_temporary_dtl`  (
                                             `synthesize_temporary_dtl_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '大宗临采单明细id',
                                             `synthesize_temporary_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '大宗临采单id',
                                             `product_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '商品id',
                                             `product_sn` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '商品编号',
                                             `product_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '商品名称',
                                             `material_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '基础库物资id',
                                             `material_sn` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '基础库物资编号',
                                             `material_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '基础库物资名称',
                                             `class_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '分类id',
                                             `class_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '分类名称',
                                             `class_id_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '分类id路径（xxx/xxx/xx）',
                                             `class_name_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '分类name路径（xxx/xxx/xx）',
                                             `supplier_org_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '商品供应商机构id',
                                             `supplier_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '商品供应商机构名称',
                                             `spec` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '规格型号',
                                             `texture` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '材质',
                                             `brand_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '品牌id',
                                             `brand_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '品牌名称',
                                             `unit` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '计量单位',
                                             `qty` decimal(10, 4) NULL DEFAULT NULL COMMENT '数量',
                                             `weight_num` decimal(10, 4) NULL DEFAULT NULL COMMENT '重量（废弃）',
                                             `cost_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '成本单价',
                                             `reference_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '参考单价（商品的参考价（不会变化））',
                                             `synthesize_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '综合单价（会变化，网价+固定费 或者 出厂价+运杂费），新增时是商品参考价（销售价）',
                                             `net_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '网价（浮动价格使用）',
                                             `fixation_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '固定费用（浮动价格使用）',
                                             `out_factory_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '出厂价（固定价格使用）',
                                             `transport_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '运杂费（固定价格使用）',
                                             `reference_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '参考金额（不会变化）',
                                             `synthesize_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '综合金额（会变化）',
                                             `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间	',
                                             `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间	',
                                             `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                             `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                             `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                             `sort` int NULL DEFAULT NULL COMMENT '排序',
                                             `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型',
                                             `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '逻辑删除：（ -1: 删除  0:未删除）',
                                             `is_two_unit` tinyint NULL DEFAULT NULL COMMENT '是否有二级单位',
                                             `two_unit` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '二级单位',
                                             `two_unit_num` decimal(10, 4) NULL DEFAULT NULL COMMENT '二级单位购买数量',
                                             `second_unit_num` decimal(10, 4) NULL DEFAULT NULL COMMENT '二级单位的计算系数',
                                             `is_bidding` int NULL DEFAULT NULL COMMENT '竞价状态（0 未生成竞价 1 已生成竞价）',
                                             `zone_addr` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '区域地址',
                                             `zone_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '区域Id',
                                             `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                             `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                             PRIMARY KEY (`synthesize_temporary_dtl_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '大宗临采单明细' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_contr
-- ----------------------------
DROP TABLE IF EXISTS `sys_contr`;
CREATE TABLE `sys_contr`  (
                              `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
                              `my_date` datetime NULL DEFAULT NULL,
                              `json_str` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
                              `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                              `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                              PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '我的控制' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu`  (
                             `menu_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '菜单id',
                             `code` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '菜单编号',
                             `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '菜单名称',
                             `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '1' COMMENT '菜单类型（1菜单2按钮3资源）',
                             `auth_label` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '权限标识（可选）',
                             `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '菜单图标',
                             `path_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '路由地址',
                             `class_code` tinyint NULL DEFAULT NULL COMMENT '所属平台（1后台管理平台2普通供应商3履约管理）',
                             `parent_menu_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '父级菜单id（1级为null）',
                             `level` tinyint NOT NULL DEFAULT 1 COMMENT '菜单层级（0子系统 1：一级，2：二级，3：二级）1',
                             `sort` int NULL DEFAULT NULL COMMENT '排序',
                             `state` tinyint NULL DEFAULT 1 COMMENT '状态（是否显示（0否1是））',
                             `remarks` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
                             `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                             `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                             `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                             `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                             `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '逻辑删除 -1: 删除 0:未删除',
                             `mall_type` tinyint NOT NULL DEFAULT 0 COMMENT '商城类型：0物资商场, 1设备商城 ',
                             `is_open` tinyint NULL DEFAULT 0 COMMENT '是否公开（0否1是）',
                             `show_dev` tinyint NOT NULL DEFAULT 0 COMMENT '是否只测试环境展示（0否1是）',
                             `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                             `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                             PRIMARY KEY (`menu_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '菜单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_menu_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu_role`;
CREATE TABLE `sys_menu_role`  (
                                  `sys_menu_role_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '菜单角色表id',
                                  `role_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色id',
                                  `menu_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '菜单id',
                                  `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '逻辑删除 -1: 删除 0:未删除',
                                  `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                  `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                  PRIMARY KEY (`sys_menu_role_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '菜单角色表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_oper_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_oper_log`;
CREATE TABLE `sys_oper_log`  (
                                 `id` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
                                 `oper_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '主机地址',
                                 `operator_type` int NULL DEFAULT 0 COMMENT '操作类别：0商城 1后台用户 2小程序用户',
                                 `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '模块标题',
                                 `business_type` int NULL DEFAULT 0 COMMENT '业务类型:0=其它,1=新增,2=修改,3=删除,4=上传,5=导出,6=导入',
                                 `method` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '方法名称',
                                 `request_method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '请求方式',
                                 `oper_param` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '请求参数',
                                 `json_result` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '返回参数',
                                 `status` int NULL DEFAULT 0 COMMENT '操作状态（0正常 1异常）',
                                 `error_msg` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '错误消息',
                                 `oper_time` datetime NULL DEFAULT NULL COMMENT '操作时间',
                                 `cost_time` bigint NULL DEFAULT 0 COMMENT '消耗时间',
                                 `oper_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '操作人员',
                                 `org_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '单位/企业名称',
                                 `oper_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '',
                                 `oper_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '请求URL',
                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`  (
                             `role_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色id',
                             `code` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '角色编号',
                             `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '角色名称',
                             `sort` int NULL DEFAULT NULL COMMENT '排序',
                             `state` tinyint NULL DEFAULT 1 COMMENT '状态（是否启用（0否1是））',
                             `remarks` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
                             `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                             `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                             `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                             `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                             `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '逻辑删除 -1: 删除 0:未删除',
                             `mall_type` tinyint NOT NULL DEFAULT 0 COMMENT '商城类型：0物资商场, 1设备商城 ',
                             `keyword` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关键字',
                             `org_search` tinyint NULL DEFAULT NULL COMMENT '机构数据查看权限（1本机及子级2只看本级）',
                             `category_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '所属平台（1后台管理平台2供应商管理平台3履约管理平台）',
                             `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                             `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                             PRIMARY KEY (`role_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '角色表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for system_param
-- ----------------------------
DROP TABLE IF EXISTS `system_param`;
CREATE TABLE `system_param`  (
                                 `system_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '系统参数表id',
                                 `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '名称',
                                 `code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'code',
                                 `key_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '值',
                                 `key_value2` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '值2',
                                 `remarks` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '说明',
                                 `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                 `gmt_modified` datetime NULL DEFAULT NULL COMMENT '修改时间',
                                 `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城',
                                 `maintain` tinyint NULL DEFAULT NULL COMMENT '维护: 0不可维护 1可维护',
                                 `sort` int NULL DEFAULT NULL COMMENT '排序',
                                 `type` tinyint NULL DEFAULT NULL COMMENT '0:参数 1:字典',
                                 `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '逻辑删除 -1: 删除 0:未删除',
                                 `is_show` tinyint NOT NULL DEFAULT 1 COMMENT '是否可见（1是0否）',
                                 `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                 `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                 PRIMARY KEY (`system_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '系统参数表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for undo_log
-- ----------------------------
DROP TABLE IF EXISTS `undo_log`;
CREATE TABLE `undo_log`  (
                             `id` bigint NOT NULL AUTO_INCREMENT,
                             `branch_id` bigint NOT NULL,
                             `xid` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                             `context` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                             `rollback_info` longblob NOT NULL,
                             `log_status` int NOT NULL,
                             `log_created` datetime NOT NULL,
                             `log_modified` datetime NOT NULL,
                             `ext` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                             `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                             `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                             PRIMARY KEY (`id`) USING BTREE,
                             UNIQUE INDEX `ux_undo_log`(`xid` ASC, `branch_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6256 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user`  (
                         `user_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户id',
                         `interior_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '内部id',
                         `user_number` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '用户编号',
                         `account` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '账号',
                         `password` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用户密码',
                         `user_mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '手机号码',
                         `email` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '邮箱',
                         `user_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '头像(图片地址)',
                         `user_img_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '图片记录id',
                         `real_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '真实姓名',
                         `nick_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '昵称',
                         `gender` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '性别 1:男 0: 女',
                         `province` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '省份',
                         `city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '城市',
                         `county` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '区县',
                         `detail_address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '详细地址',
                         `birthday` date NULL DEFAULT NULL COMMENT '生日',
                         `device_state` tinyint NOT NULL DEFAULT 1 COMMENT '装备状态（0停用1启用）',
                         `material_state` tinyint NOT NULL DEFAULT 1 COMMENT '物资状态（0停用1启用）',
                         `state` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '用户状态 0：初始（默认） 1：启用  2:禁用 ',
                         `is_admin` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '是否为管理员  1：是  0：不是',
                         `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                         `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                         `gmt_login` datetime NULL DEFAULT NULL COMMENT '上次登录时间',
                         `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人id',
                         `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                         `is_material` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否为物资商城注册   1：是   0：否',
                         `is_device` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否为设备商城注册   1：是   0：否',
                         `enterprise_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '企业附加信息id',
                         `is_internal_user` tinyint NOT NULL DEFAULT 0 COMMENT '是否为内部用户   1：是   0：否（默认：0）',
                         `platform_admin` tinyint NULL DEFAULT NULL COMMENT '是否为平台管理员  1：是  0：不是（默认：0）',
                         `sort` int NULL DEFAULT NULL COMMENT '排序',
                         `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                         `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                         `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                         `wx_open_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '微信的openId',
                         `login_count` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '登陆次数',
                         `first_login` int UNSIGNED NULL DEFAULT 1 COMMENT '是否首次登录 1 是 0 否',
                         `locked_state` int NULL DEFAULT 0 COMMENT '锁定状态(0正常 1锁定)',
                         `pwd_change_date` datetime NULL DEFAULT NULL COMMENT '上次密码修改时间',
                         `password_err_times` int NULL DEFAULT 0 COMMENT '密码错误次数',
                         `lock_cause` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '锁定原因',
                         `last_failed_login_time` datetime NULL DEFAULT NULL COMMENT '上一次密码错误时间',
                         `attr_one` tinyint NOT NULL DEFAULT 0 COMMENT '备用1（目前是重置密码）',
                         `is_show_bid` int NOT NULL DEFAULT 1 COMMENT '是否提示电子招标服务',
                         `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                         `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                         `out_user_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'TT用户编号',
                         PRIMARY KEY (`user_id`) USING BTREE,
                         INDEX `interior_id`(`user_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user_activity
-- ----------------------------
DROP TABLE IF EXISTS `user_activity`;
CREATE TABLE `user_activity`  (
                                  `user_activity_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户行为id',
                                  `user_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用户id',
                                  `user_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用户名称',
                                  `login_time` datetime NULL DEFAULT NULL COMMENT '登陆时间',
                                  `org_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '机构id',
                                  `org_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '机构名称',
                                  `org_type` tinyint NULL DEFAULT NULL COMMENT '机构类型：0：个体户  1：企业  2：个人',
                                  `login_ip` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '登陆ip',
                                  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注信息',
                                  `login_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '登录方式（描述）',
                                  `user_mobile` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用户手机号',
                                  `bus_type` tinyint NOT NULL DEFAULT 0 COMMENT '行为类型（0登陆1发送验证码）',
                                  `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                  `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                  PRIMARY KEY (`user_activity_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户行为表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_address
-- ----------------------------
DROP TABLE IF EXISTS `user_address`;
CREATE TABLE `user_address`  (
                                 `address_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '地址id',
                                 `user_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用户id',
                                 `alias_address` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '地址别名',
                                 `receiver_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '收件人姓名',
                                 `receiver_mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '收件人手机号',
                                 `fixed_telephone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '固定电话',
                                 `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '邮箱',
                                 `province` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '省份',
                                 `city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '城市',
                                 `county` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '区县',
                                 `detail_address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '详细地址',
                                 `post_code` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '邮编',
                                 `state` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '状态,1正常，0无效',
                                 `is_default_address` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '是否默认地址  1:是  0:否',
                                 `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                 `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                 `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
                                 `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                 `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                                 `sort` int NULL DEFAULT NULL COMMENT '排序',
                                 `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                                 `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                                 `enterprise_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '企业id',
                                 `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                 `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                 PRIMARY KEY (`address_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户地址' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user_re_enterprise
-- ----------------------------
DROP TABLE IF EXISTS `user_re_enterprise`;
CREATE TABLE `user_re_enterprise`  (
                                       `user_re_enterprise_id` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
                                       `user_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户ID',
                                       `enterprise_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '企业id',
                                       `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '逻辑删除 -1: 删除 0:未删除',
                                       `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间（加入时间）',
                                       `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间（离开时间）',
                                       `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
                                       `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
                                       `institution_status` int NULL DEFAULT NULL COMMENT '默认机构  1是，2 否',
                                       `interior_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '内部企业id',
                                       `role_id` int NULL DEFAULT NULL COMMENT '对应的角色id',
                                       `far_user_id` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '远程用户id',
                                       `enterprise_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '企业名称',
                                       `short_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '机构简码',
                                       `org_type` int NULL DEFAULT NULL COMMENT '机构类型(1:集团|2:分子公司|4:经理部|5:项目部|6:股份|7:事业部)\"',
                                       PRIMARY KEY (`user_re_enterprise_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Procedure structure for add_modify_columns_to_all_tables
-- ----------------------------
DROP PROCEDURE IF EXISTS `add_modify_columns_to_all_tables`;
delimiter ;;
CREATE PROCEDURE `add_modify_columns_to_all_tables`()
BEGIN
    DECLARE done INT DEFAULT 0;
    DECLARE current_table VARCHAR(255);

    -- 游标查询所有表
    DECLARE table_cursor CURSOR FOR
        SELECT table_name FROM information_schema.tables
        WHERE table_schema = DATABASE(); -- 当前数据库

    -- 用于捕捉游标结束的 handler
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = 1;

    -- 打开游标
    OPEN table_cursor;

    -- 遍历所有表
    read_loop: LOOP
        -- 获取当前表名
        FETCH table_cursor INTO current_table;
        IF done THEN
            LEAVE read_loop;
        END IF;

        -- 检查 `modify_name` 字段是否存在，如果不存在则添加
        IF NOT EXISTS (
            SELECT * FROM information_schema.COLUMNS
            WHERE table_schema = DATABASE()
              AND table_name = current_table
              AND column_name = 'modify_name'
        ) THEN
            SET @sql = CONCAT('ALTER TABLE `', current_table, '` ',
                              'ADD COLUMN `modify_name` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL ',
                              'COMMENT \'修改人名称\';');
            PREPARE stmt FROM @sql;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;
        END IF;

        -- 检查 `modify_id` 字段是否存在，如果不存在则添加
        IF NOT EXISTS (
            SELECT * FROM information_schema.COLUMNS
            WHERE table_schema = DATABASE()
              AND table_name = current_table
              AND column_name = 'modify_id'
        ) THEN
            SET @sql = CONCAT('ALTER TABLE `', current_table, '` ',
                              'ADD COLUMN `modify_id` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL ',
                              'COMMENT \'修改人id\';');
            PREPARE stmt FROM @sql;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;
        END IF;

    END LOOP;

    -- 关闭游标
    CLOSE table_cursor;
END
;;
delimiter ;

SET FOREIGN_KEY_CHECKS = 1;