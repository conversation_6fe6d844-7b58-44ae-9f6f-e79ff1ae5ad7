package scrbg.meplat.mall.dto.contact.RPCDTO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-04-21 10:26
 */
@Data
public class CBM_EquipOperationOrderDTO {


    @ApiModelProperty(value = "合同ID")
    private String ContractId;
    @ApiModelProperty(value = "合同类型")
    private Integer type;
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;
    @ApiModelProperty(value = "信用代码")
    private String creditCode;
    @ApiModelProperty(value = "订单ID")
    private String orderId;
    @ApiModelProperty(value = "店铺ID")
    private String shopId;
    @ApiModelProperty(value = "机构ID")
    private String orgId;
    @ApiModelProperty(value = "机构名称")
    private String orgName;
//    @ApiModelProperty(value = "创建时间")
//    private Date createTime;
    @ApiModelProperty(value = "录入人")
    private String recorder;
    @ApiModelProperty(value = "录入人ID")
    private String recorderId;

    @ApiModelProperty(value = "details")
    private List<CBM_EquipOperationOrderDtlDTO> details;





}
