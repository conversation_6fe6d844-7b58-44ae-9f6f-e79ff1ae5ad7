package scrbg.meplat.mall.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
import scrbg.meplat.mall.vo.fee.DealFeePayDtlList;

/**
 * @描述：平台交易费缴费记录
 * @作者: ye
 * @日期: 2024-01-24
 */
@ApiModel(value = "平台交易费缴费记录")
@Data
@TableName("platform_deal_fee_record")
public class PlatformDealFeeRecord extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "缴费记录id")
    private String dealFeeRecordId;

    @ApiModelProperty(value = "缴费记录编号")
    private String dealFeeRecordUn;

    @ApiModelProperty(value = "店铺id")
    private String shopId;

    @ApiModelProperty(value = "店铺名称")
    private String shopName;

    @ApiModelProperty(value = "企业id")
    private String enterpriseId;

    @ApiModelProperty(value = "企业名称")
    private String enterpriseName;

    @ApiModelProperty(value = "缴费金额")
    private BigDecimal payAmount;

    @ApiModelProperty(value = "缴费类型(1线下2线上3其他)")
    private Integer payType;

    @ApiModelProperty(value = "审核时间")
    private Date auditOpenTime;

    @ApiModelProperty(value = "审核人员id")
    private String auditorId;

    @ApiModelProperty(value = "审核人员姓名")
    private String auditorName;

    @ApiModelProperty(value = "记录类型（1店铺交易服务费2合同履约服务费用）")
    private Integer recordType;

    @ApiModelProperty(value = "状态（0待确认1确认中2确认成功3确认失败4审核中5审核通过6审核未通过）--作废（0草稿1待审核2审核通过3审核未通过）")
    private Integer state;

    @ApiModelProperty(value = "结算日期")
    private LocalDate settleDate;

    @ApiModelProperty(value = "当期结算交易开始日期")
    private LocalDate periodStartDate;

    @ApiModelProperty(value = "当期结算交易结束日期")
    private LocalDate periodEndDate;

    @ApiModelProperty(value = "缴费截止日期")
    private LocalDate paymentDeadline;

    @ApiModelProperty(value = "本次结算交易额")
    private BigDecimal periodTransactionAmount;

    @ApiModelProperty(value = "本次结算店铺交易费收取比例（单位%")
    private BigDecimal feeRatio;

    @ApiModelProperty(value = "累计结算交易额=本次结算金交易额 + 上次结算交易额")
    private BigDecimal totalTransactionAmount;

    @ApiModelProperty(value = "本次缴费多余退回余额金额")
    private BigDecimal returnBalance;

    @ApiModelProperty(value = "乐观锁")
    @Version
    private Integer version;

    @ApiModelProperty(value = "附件")
    @TableField(exist = false)
    private List<PlatformFeeFile> files;

    @ApiModelProperty(value = "是否提交")
    @TableField(exist = false)
    private int submitAud;

    @ApiModelProperty(value = "审核历史")
    @TableField(exist = false)
    private List<AuditRecord> auditRecords;

    @ApiModelProperty(value = "缴费明细")
    @TableField(exist = false)
    private List<PlatformDealFeeRecordDtl> dtls;

    @ApiModelProperty(value = "缴费明细查询返回")
    @TableField(exist = false)
    private List<DealFeePayDtlList> dtlVOs;

    @ApiModelProperty(value = "合同编号")
    @TableField(exist = false)
    private String contractNo;
    
    @ApiModelProperty(value = "不含税金额")
    private BigDecimal noTaxAmount;

    @ApiModelProperty(value = "税率")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "税额")
    private BigDecimal taxAmount;

}