package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @描述：支付信息表
 * @作者: y
 * @日期: 2022-11-13
 */
@ApiModel(value = "支付信息表")
@Data
@TableName("order_payment_info")
public class OrderPaymentInfo extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "支付号")

    private String paymentId;

    @ApiModelProperty(value = "订单号（对外业务号）")

    private String orderSn;

    @ApiModelProperty(value = "订单id")

    private String orderId;

    @ApiModelProperty(value = "交易流水号")

    private String alipayTradeNo;

    @ApiModelProperty(value = "支付总金额")

    private BigDecimal totalAmount;

    @ApiModelProperty(value = "交易内容")

    private String subject;

    @ApiModelProperty(value = "支付状态")

    private String paymentStatus;

    @ApiModelProperty(value = "回调内容")

    private String callbackContent;

    @ApiModelProperty(value = "回调时间")

    private Date callbackTime;


    @ApiModelProperty(value = "状态")

    private Integer state;

    @ApiModelProperty(value = "商品类型：0物资 1设备 2周材（设备租赁） 3周材（设备） 4二手设备 5租赁设备")
    private Integer productType;
}
