package scrbg.meplat.mall.dto.product;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-11-04 17:15
 */
@Data
public class ProductFileSaveOrUpdateDTO {



    @ApiModelProperty(value = "附件id")
    private String fileId;

    @ApiModelProperty(value = "媒体地址")
    private String url;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "是否主图，1：是，0：否")
    private Integer isMain;

    @ApiModelProperty(value = "媒体类型 1图片2视频")
    private Integer type;

}
