package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import scrbg.meplat.mall.service.OutBoxService;
import scrbg.meplat.mall.vo.user.userCenter.OutBoxVo;
@Log4j2
@RestController
@RequestMapping("/platform/enterpriseInfo")
@ApiSort(value = 500)
public class OutBoxController{

    @Autowired
    public OutBoxService outBoxService;

        @PostMapping("/findBySupplierShopsList")
        @ApiOperation(value = "根据实体属性分页查询(供应商和对应店铺)")
        @DynamicParameters(name = "根据实体属性分页查询", properties = {
                @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
                @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
                @DynamicParameter(name = "startDate", value = "创建时间开始时间", dataTypeClass = String.class),
                @DynamicParameter(name = "endDate", value = "创建时间结束时间", dataTypeClass = String.class),
                @DynamicParameter(name = "enterpriseName", value = "企业名称", dataTypeClass = String.class),
                @DynamicParameter(name = "isSupplier", value = "是否供应商", dataTypeClass = Integer.class),
                @DynamicParameter(name = "shopName", value = "店铺名称", dataTypeClass = String.class),
                @DynamicParameter(name = "enterpriseId", value = "企业id", dataTypeClass = String.class),
                @DynamicParameter(name = "orderBy", value = "排序方式", dataTypeClass = Integer.class),
                @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
                @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
                @DynamicParameter(name = "shopName", value = "店铺名称", dataTypeClass = String.class),
                @DynamicParameter(name = "shopType", value = "店铺类型", dataTypeClass = Integer.class),
                @DynamicParameter(name = "state", value = "店铺状态", dataTypeClass = Integer.class),
                @DynamicParameter(name = "isBusiness", value = "是否自营", dataTypeClass = Integer.class),
                @DynamicParameter(name = "isSupplier", value = "是否供应商", dataTypeClass = Integer.class),
        })
        public PageR<OutBoxVo> listBySupplierShops(@RequestBody JSONObject jsonObject) {
            PageUtils page = outBoxService.queryPageSupplierShops(jsonObject, new QueryWrapper<OutBoxVo>());
            return PageR.success(page);
        }
}
