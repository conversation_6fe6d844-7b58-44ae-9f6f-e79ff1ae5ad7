package scrbg.meplat.mall.dto.product.device;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-12-02 9:18
 */
@Data
public class ImportDeviseDTO {

    @ApiModelProperty(value = "商品类型：0物资 1设备 2周材（设备租赁） 3周材（设备） 4二手设备 5租赁设备")
    private Integer productType;

    @ApiModelProperty(value = "分类id")
    private String classId;

    /**
     *  外部导入
     */
//    @ApiModelProperty(value = "主键设备id")
//    private String equipmentId;

    @ApiModelProperty(value = "设备编号")
    private String equipmentNo;

    @ApiModelProperty(value = "设备名称")
    private String equipmentName;

    @ApiModelProperty(value = "规格型号")
    private String specificationModel;

    @ApiModelProperty(value = "计量单位")
    private String unit;

}
