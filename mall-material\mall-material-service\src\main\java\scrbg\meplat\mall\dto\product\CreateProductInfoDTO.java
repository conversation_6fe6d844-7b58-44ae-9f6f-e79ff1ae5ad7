package scrbg.meplat.mall.dto.product;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-11-03 16:48
 */
@Data
public class CreateProductInfoDTO {

    @ApiModelProperty(value = "商品名称",required = true)
    @NotEmpty(message = "商品名称不能为空！")
    private String productName;

    @ApiModelProperty(value = "商品描述")
    private String productDescribe;

    @ApiModelProperty(value = "分类ID",required = true)
    @NotEmpty(message = "分类ID不能为空！")
    private String classId;

    @ApiModelProperty(value = "商品的最低价",required = false)
    private BigDecimal productMinPrice;

    @ApiModelProperty(value = "媒体")
    private List<ProductMediumDTO> mediumList;


    @ApiModelProperty(value = "规格",required = true)
    @NotEmpty(message = "规格不能为空！")
    private String spec;

    @ApiModelProperty(value = "规格图片")
    private String specImage;

    @ApiModelProperty(value = "原价")
    private BigDecimal originalPrice;

    @ApiModelProperty(value = "销售价格",required = true)
    @NotEmpty(message = "销售价格不能为空！")
    private BigDecimal sellPrice;

    @ApiModelProperty(value = "库存",required = true)
    @NotEmpty(message = "库存不能为空！")
    private BigDecimal stock;

    @ApiModelProperty(value = "计量单位",required = true)
    @NotEmpty(message = "计量单位不能为空！")
    private String unit;

    @ApiModelProperty(value = "成本价")
    private BigDecimal costPrice;

}
