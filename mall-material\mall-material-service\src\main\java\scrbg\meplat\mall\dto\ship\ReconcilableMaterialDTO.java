package scrbg.meplat.mall.dto.ship;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @描述：以及对账单可对账物资查询
 * @作者: ye
 * @日期: 2024-01-15
 */
@ApiModel(value = "一级大宗临购可对账物资查询DTO")
@Data
public class ReconcilableMaterialDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "关键字搜索")
    private String keywords;

    @ApiModelProperty(value = "业务类型(新类型) 1、零星采购 2、大宗临购 3、周转材料 老类型：10、零星采购 12、大宗临购")
    private String productType;

    @ApiModelProperty(value = "1浮动价格对账单2固定价格对账单")
    private String type;

    @ApiModelProperty(value = "收料开始时间")
    private String startTime;

    @ApiModelProperty(value = "收料结束时间")
    private String endTime;

    @ApiModelProperty(value = "订单编号（支持多个，用逗号隔开）")
    private String orderSn;

    @ApiModelProperty(value = "当前页数")
    private Integer page;

    @ApiModelProperty(value = "每页显示条数")
    private Integer limit;
}
