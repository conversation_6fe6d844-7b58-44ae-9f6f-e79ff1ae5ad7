package scrbg.meplat.mall.entity.excelTemplate;

/**
 * <AUTHOR>
 * @create 2023-05-07 8:53
 */

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@HeadRowHeight(40)
@ColumnWidth(25)
@ContentRowHeight(40)
// 设置表头颜色
//@HeadStyle(fillForegroundColor = 17)
public class Supplier implements Serializable {
//    private static final long serialVersionUID = -5144055068797033748L;


    @ExcelProperty(value = "供应商名称")
    private String enterpriseName;

    @ExcelProperty(value = "统一社会信用代码")
    private String socialCreditCode;

    @ExcelProperty(value = "经营范围")
    private String mainBusiness;

    @ExcelProperty(value = "注册资本(万元)")

    private BigDecimal registeredCapital;
//    @ExcelProperty(value = "注册省份")
//
//    private String provinces;
//
//    @ExcelProperty(value = "注册市级")
//
//    private String city;
//
//    @ExcelProperty(value = "注册县、区")
//
//    private String county;

    @ExcelProperty(value = "注册详细地址")

    private String detailedAddress;

    @ExcelProperty(value = "法定代表人")

    private String legalRepresentative;

    @ExcelProperty(value = "身份证号")

    private String adminNumber;

    @ExcelProperty(value = "联系电话")

    private String adminPhone;

}
