package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;

/**
 * @描述：开户银行信息表
 * @作者: y
 * @日期: 2022-11-13
 */
@ApiModel(value = "开户银行信息表")
@Data
@TableName("deposit_bank")
public class DepositBank extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "开户银行id")

    private String bankId;

    @ApiModelProperty(value = "银行名称")

    private String bankName;

    @ApiModelProperty(value = "银行账号")

    private String bankCount;

    @ApiModelProperty(value = "联系电话")

    private String phone;


    @ApiModelProperty(value = "企业id")

    private String enterpriseId;

    @ApiModelProperty(value = "是否是该企业默认银行账户 1: 是 0:否")

    private Integer isDefault;

    @ApiModelProperty(value = "状态")

    private Integer state;


}
