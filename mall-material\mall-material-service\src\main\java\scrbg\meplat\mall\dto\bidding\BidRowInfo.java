package scrbg.meplat.mall.dto.bidding;
/**
 * <AUTHOR>
 * @date 2023/7/24
 */

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @program: maill_api
 *
 * @description: 行数据封装
 *
 * @author: 代文翰
 *
 * @create: 2023-07-24 23:14
 **/
@Data
public class BidRowInfo {

    @ApiModelProperty(value = "竞价记录明细id")
    private String bidRecordItemId;

    @ApiModelProperty(value = "竞价记录id")

    private String bidRecordId;


    @ApiModelProperty(value = "竞价采购商品id")

    private String biddingProductId;


    @ApiModelProperty(value = "竞价采购id")

    private String biddingId;


    @ApiModelProperty(value = "不含税到场单价")

    private BigDecimal bidPrice;


    @ApiModelProperty(value = "税率")

    private BigDecimal taxRate;


    @ApiModelProperty(value = "含税到场单价")

    private BigDecimal bidRatePrice;


    @ApiModelProperty(value = "含税总金额")

    private BigDecimal bidRateAmount;


    @ApiModelProperty(value = "不含税总金额")

    private BigDecimal bidAmount;


    @ApiModelProperty(value = "竞价时间")

    private Date bidTime;


    @ApiModelProperty(value = "驳回原因")

    private String rejectReason;


    @ApiModelProperty(value = "状态")

    private Integer state;


    @ApiModelProperty(value = "竞价采购编号")

    private String biddingSn;


    @ApiModelProperty(value = "订单id")

    private String orderId;


    @ApiModelProperty(value = "订单编号")

    private String orderSn;


    @ApiModelProperty(value = "订单明细id")

    private String orderItemId;


    @ApiModelProperty(value = "商品名称")

    private String productName;


    @ApiModelProperty(value = "品牌名称")

    private String brand;


    @ApiModelProperty(value = "规格型号")

    private String spec;


    @ApiModelProperty(value = "计量单位")

    private String unit;


    @ApiModelProperty(value = "数量")

    private BigDecimal num;

    @ApiModelProperty(value = "商品材质")

    private String productTexture;




    @ApiModelProperty(value = "分类id")

    private String classId;


    @ApiModelProperty(value = "分类路径名称（xxx/xxxx/xxx）")

    private String classPathName;


    @ApiModelProperty(value = "创建机构id")

    private String createOrgId;

    @ApiModelProperty(value = "创建机构名称")

    private String createOrgName;

    @ApiModelProperty(value = "参考单价")

    private BigDecimal referencePrice;


    @ApiModelProperty(value = "供货时间")

    private Date deliveryDate;


    @ApiModelProperty(value = "供货地点")

    private String deliveryAddress;
    @ApiModelProperty(value = "备注")

    private String remarks;
}
