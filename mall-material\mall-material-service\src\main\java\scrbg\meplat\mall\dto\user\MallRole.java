package scrbg.meplat.mall.dto.user;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.SysMenu;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-12-21 9:33
 */
@Data
public class MallRole {

    @ApiModelProperty(value = "角色id")
    private String roleId;

    @ApiModelProperty(value = "角色编号")
    private String code;


    @ApiModelProperty(value = "角色名称")
    private String name;


    @ApiModelProperty(value = "机构数据查看权限（1本机及子级2只看本级）")
    private Integer orgSearch;

    @ApiModelProperty(value = "所属平台（1后台管理平台2供应商管理平台3履约管理平台）")

    private Integer categoryType;


    @ApiModelProperty(value = "角色菜单表")
    @TableField(exist = false)
    private List<MallSysMenu> mallSysMenus;




}
