package scrbg.meplat.mall.entity;


import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(value = "仓库管理-自营店商品出入库管理")
@Data
@TableName("secondary_supplier_record")
public class SecondarySupplierRecord  implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "记录id")
    private String recordId;

    @ApiModelProperty(value = "订单号")
    private String orderSn;

    @ApiModelProperty(value = "商品id")
    private String productId;

    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "供应商id")
    private String supplierId;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "采购机构id")
    private String purchasingAgencyId;

    @ApiModelProperty(value = "采购机构")
    private String purchasingAgencyName;

    @ApiModelProperty(value = "记录类型 1-入库 2-出库")
    private Integer recordType;

    @ApiModelProperty(value = "商品类型")
    private Integer productType;

    @ApiModelProperty(value = "含税总金额")
    private BigDecimal bidRateAmount;

    @ApiModelProperty(value = "数量")
    private BigDecimal num;

    @ApiModelProperty(value = "库房id")
    private String warehouseId;

    /** 操作人员 */
    @ApiModelProperty(name = "操作人")
    private String operationUser;

    /** 操作人员 */
    @ApiModelProperty(name = "操作人手机号")
    private String operationUserPhone;

    @ApiModelProperty(name = "申请退货时间")
    private Date returnTime;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;

    @ApiModelProperty(value = "明细")
    private String settlementInfo;
}
