package scrbg.meplat.mall.dto.payment;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @program: maill_api
 * @description: 蜀道企业店铺服务时间数据对象
 * @author: 代文翰
 * @create: 2023-08-22 16:10
 **/
@Data
public class SdDurationDto {
    private String shopId;
    /**
     *租赁服务 1
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd hh:mm:ss")

    private List<Date> zlTimeRange;
    /**
     * 物流服务 2
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd hh:mm:ss")

    private List<Date> wlTimeRange;
    /**
     * 集采服务 0
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd hh:mm:ss")

    private List<Date> jcTimeRange;
}
