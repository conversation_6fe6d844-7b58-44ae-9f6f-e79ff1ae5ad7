package scrbg.meplat.mall.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import scrbg.meplat.mall.enums.PublicEnum;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;

import java.time.LocalDateTime;
import java.util.Date;

@Component
public class MyMetaObjectHandler implements MetaObjectHandler {
    @Autowired
    public MallConfig mallConfig;


    // 插入时的填充策略
    @Override
    public void insertFill(MetaObject metaObject) {
//        Object mallType = metaObject.getValue("mallType");
//        if(mallType == null) {
//            mallType = mallConfig.mallType;
//        }
        Integer mallType = mallConfig.mallType;
        this.setFieldValByName("mallType",mallType,metaObject);
        if (metaObject.getValue("gmtCreate") == null) {
            this.setFieldValByName("gmtCreate", new Date(), metaObject);
        }
        this.setFieldValByName("gmtModified",new Date(),metaObject);
        UserLogin currentUser = ThreadLocalUtil.getCurrentUser();
        if(currentUser != null){
            this.setFieldValByName("founderName",currentUser.getUserName(),metaObject);
            this.setFieldValByName("founderId",currentUser.getUserId(),metaObject);
        }
        this.setFieldValByName("isDelete", PublicEnum.IS_DELETE_NO.getCode(),metaObject);
    }

    //更新时的填充策略
    @Override
    public void updateFill(MetaObject metaObject) {
        this.setFieldValByName("gmtModified",new Date(),metaObject);
        UserLogin currentUser = ThreadLocalUtil.getCurrentUser();
        if(currentUser != null){
            this.setFieldValByName("modifyName",currentUser.getUserName(),metaObject);
            this.setFieldValByName("modifyId",currentUser.getUserId(),metaObject);
        }
    }
}
