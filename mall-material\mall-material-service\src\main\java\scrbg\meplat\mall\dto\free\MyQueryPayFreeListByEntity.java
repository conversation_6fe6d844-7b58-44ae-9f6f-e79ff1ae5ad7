package scrbg.meplat.mall.dto.free;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2024-04-09 9:55
 */
@Data
public class MyQueryPayFreeListByEntity {

    @ApiModelProperty(value = "缴费记录id")
    private String paymentRecordId;

    @ApiModelProperty(value = "缴费记录编号")
    private String paymentRecordUn;

    @ApiModelProperty(value = "店铺id")
    private String shopId;

    @ApiModelProperty(value = "店铺名称")
    private String shopName;

    @ApiModelProperty(value = "企业id")
    private String enterpriseId;

    @ApiModelProperty(value = "企业名称")
    private String enterpriseName;

    @ApiModelProperty(value = "缴费金额")
    private BigDecimal payAmount;

    @ApiModelProperty(value = "缴费类型(1线下2线上3其他)")
    private Integer payType;

    @ApiModelProperty(value = "缴费时长")
    private Integer paymentDuration;

    @ApiModelProperty(value = "缴费时长类型（单位）（1天2周3月4年）")
    private Integer paymentDurationType;

    @ApiModelProperty(value = "审核时间")
    private Date auditOpenTime;

    @ApiModelProperty(value = "缴费记类型（1店铺年费2招标年费）")
    private Integer recordType;

    @ApiModelProperty(value = "状态（0草稿1待审核2审核通过3审核未通过）")
    private Integer state;

    @ApiModelProperty(value = "创建时间")
    private Date gmtCreate;

    @ApiModelProperty(value = "更新时间")
    private Date gmtModified;




}
