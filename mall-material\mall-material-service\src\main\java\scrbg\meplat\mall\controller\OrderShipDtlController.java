package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.dto.ship.ReconcilableMaterialDTO;
import scrbg.meplat.mall.entity.DealOrderInfo;
import scrbg.meplat.mall.entity.OrderShipDtl;
import scrbg.meplat.mall.service.OrderShipDtlService;
import scrbg.meplat.mall.vo.platform.ListShipByAffirmListVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @描述：控制类
 * @作者: ye
 * @日期: 2023-05-22
 */
@RestController
@RequestMapping("/orderShipDtl")
@Api(tags = "发货单清单")
public class OrderShipDtlController {

    @Autowired
    public OrderShipDtlService orderShipDtlService;

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "billId", value = "发货单Id", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<OrderShipDtl> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = orderShipDtlService.queryPage(jsonObject, new LambdaQueryWrapper<OrderShipDtl>());
        return PageR.success(page);
    }

    @PostMapping("/wxListByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "billId", value = "发货单Id", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<OrderShipDtl> wxListByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = orderShipDtlService.wxqueryPage(jsonObject, new LambdaQueryWrapper<OrderShipDtl>());
        return PageR.success(page);
    }

    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "ID", required = true,
            dataType = "String", paramType = "query")
    })
    public R<OrderShipDtl> findById(String id) {
        OrderShipDtl orderShipDtl = orderShipDtlService.getById(id);
        return R.success(orderShipDtl);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody OrderShipDtl orderShipDtl) {
        orderShipDtlService.create(orderShipDtl);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody OrderShipDtl orderShipDtl) {
        orderShipDtlService.update(orderShipDtl);
        return R.success();
    }

    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "ID", required = true,
            dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        orderShipDtlService.delete(id);
        return R.success();
    }

    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")
    public R deleteBatch(@RequestBody List<String> ids) {
        orderShipDtlService.removeByIds(ids);
        return R.success();
    }

    @PostMapping("/purchaseShipDtl")
    @ApiOperation(value = "收料员查看发货单项")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "billId", value = "发货单Id", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<OrderShipDtl> purchaseShipDtl(@RequestBody JSONObject jsonObject) {
        PageUtils page = orderShipDtlService.purchaseShipDtl(jsonObject, new LambdaQueryWrapper<OrderShipDtl>());
        return PageR.success(page);
    }

    @PostMapping("/updateShipCountsByDtlIds")
    @ApiOperation(value = "根据主键批量修改数量(发货小程序)")
    public R updateShipCountsByDtlIds(@RequestBody List<OrderShipDtl> dtls) {
        orderShipDtlService.updateShipCountsByDtlIds(dtls);
        return R.success();
    }

    @PostMapping("/platform/listShipByAffirmList")
    @ApiOperation(value = "查询物资交易量信息")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "productType", value = "商品类型", dataTypeClass = Integer.class),
            @DynamicParameter(name = "productName", value = "商品名称", dataTypeClass = String.class),
            @DynamicParameter(name = "supplierName", value = "供应商名称", dataTypeClass = String.class),
            @DynamicParameter(name = "orderSn", value = "订单编号", dataTypeClass = String.class),
            @DynamicParameter(name = "enterpriseName", value = "客户", dataTypeClass = String.class),
            @DynamicParameter(name = "shopName", value = "店铺名称", dataTypeClass = String.class),
            @DynamicParameter(name = "startFinishDate", value = "完成开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endFinishDate", value = "完成结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "belowPrice", value = "以下价格（交易金额）", dataTypeClass = String.class),
            @DynamicParameter(name = "abovePrice", value = "以上价格（交易金额）", dataTypeClass = String.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
    })
    public PageR<ListShipByAffirmListVO> listByAffirmList(@RequestBody JSONObject jsonObject) {
        PageUtils page = orderShipDtlService.listByAffirmList(jsonObject);
        return PageR.success(page);
    }

    @PostMapping("/shopManage/listShipByAffirmList")
    @ApiOperation(value = "查询物资交易量信息（供应商）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "productName", value = "商品名称", dataTypeClass = String.class),
            @DynamicParameter(name = "startFinishDate", value = "完成开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endFinishDate", value = "完成结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "belowPrice", value = "以下价格（交易金额）", dataTypeClass = String.class),
            @DynamicParameter(name = "abovePrice", value = "以上价格（交易金额）", dataTypeClass = String.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
    })
    public PageR<ListShipByAffirmListVO> shopListByAffirmList(@RequestBody JSONObject jsonObject) {
        PageUtils page = orderShipDtlService.shopListByAffirmList(jsonObject);
        return PageR.success(page);
    }

    @PostMapping("/platform/outputExcel")
    @ApiOperation(value = "导出数据平台（平台交易量报表导出）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "orderSn", value = "订单编号", dataTypeClass = String.class),
            @DynamicParameter(name = "productName", value = "商品名称", dataTypeClass = String.class),
            @DynamicParameter(name = "supplierName", value = "供应商名称", dataTypeClass = String.class),
            @DynamicParameter(name = "shopName", value = "店铺名称", dataTypeClass = String.class),
            @DynamicParameter(name = "startFinishDate", value = "完成开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endFinishDate", value = "完成结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "belowPrice", value = "以下价格（交易金额）", dataTypeClass = String.class),
            @DynamicParameter(name = "abovePrice", value = "以上价格（交易金额）", dataTypeClass = String.class),
            @DynamicParameter(name = "ids", value = "导出的id", dataTypeClass = List.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
    })
    public R platformOutputExcel(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        orderShipDtlService.platformOutputExcel(jsonObject, response);
        return R.success();
    }

    @PostMapping("/shopManage/outputExcel")
    @ApiOperation(value = "导出数据店铺")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "orderSn", value = "订单编号", dataTypeClass = String.class),
            @DynamicParameter(name = "productName", value = "商品名称", dataTypeClass = String.class),
            @DynamicParameter(name = "startFinishDate", value = "完成开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endFinishDate", value = "完成结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "belowPrice", value = "以下价格（交易金额）", dataTypeClass = String.class),
            @DynamicParameter(name = "abovePrice", value = "以上价格（交易金额）", dataTypeClass = String.class),
            @DynamicParameter(name = "ids", value = "导出的id", dataTypeClass = List.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
    })
    public R shopManageOutputExcel(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        orderShipDtlService.shopManageOutputExcel(jsonObject, response);
        return R.success();
    }

    //针对微信小程序收料错误发货数量不等于收货数量的情况下。修改订单和发货单数据
    @PostMapping("/updateShipCounts")
    @ApiOperation(value = "修改订单和发货单数据")
    public R updateShipCounts(@RequestBody List<String> ids) {
        orderShipDtlService.updateShipCounts(ids);
        return R.success();
    }

    @PostMapping("/getReconcilableMaterialList")
    @ApiOperation(value = "查询一级可对账物资列表")
    public PageR getReconcilableMaterialList(@RequestBody ReconcilableMaterialDTO dto) {
        PageUtils page = orderShipDtlService.getReconcilableMaterialList(dto);
        return PageR.success(page);
    }

}

