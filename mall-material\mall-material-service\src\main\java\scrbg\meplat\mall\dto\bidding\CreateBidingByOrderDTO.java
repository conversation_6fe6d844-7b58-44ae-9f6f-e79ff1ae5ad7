package scrbg.meplat.mall.dto.bidding;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.BiddingInvitationRelevance;
import scrbg.meplat.mall.entity.OrderItem;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-07-19 13:59
 */
@Data
public class CreateBidingByOrderDTO {

    @ApiModelProperty(value = "竞价来源类型（1订单2商品）")

    private Integer biddingSourceType;


    @ApiModelProperty(value = "标题")

    private String title;

    @ApiModelProperty(value = "竞价采购类型（1公开竞价2邀请竞价）")

    private Integer type;
    @ApiModelProperty(value = "邀请竞价供应商")
    private List<BiddingInvitationRelevance> suppliers;

    @ApiModelProperty(value = "截止时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date endTime;


    @ApiModelProperty(value = "联系人名称")

    private String linkName;


    @ApiModelProperty(value = "商品类型：0物资 （所有商品都是物资，只有下单才会根据分类自动生成不同的订单）最新改动：商品类型：0 低值易耗品 1大宗临购")

    private Integer productType;

    @ApiModelProperty(value = "价格类型（1浮动价格2固定价格）大宗临购使用")

    private Integer billType;

    @ApiModelProperty(value = "联系电话")

    private String linkPhone;


    @ApiModelProperty(value = "竞价说明")

    private String biddingExplain;
    @ApiModelProperty(value = "竞价函说明")
    private String biddingNotice;

    @ApiModelProperty(value = "订单明细")
    private List<OrderItem> orderItems;

}
