package scrbg.meplat.mall.dto.fee;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @描述：缴费管理查询结果VO
 * @作者: AI Assistant
 * @日期: 2025-07-22
 */
@Data
public class FeeManagementResultVO {

    @ApiModelProperty(value = "缴费编号")
    private String paymentRecordUn;

    @ApiModelProperty(value = "企业名称")
    private String enterpriseName;

    @ApiModelProperty(value = "服务类型（1、店铺年度服务费 2、店铺交易服务费）")
    private Integer serviceType;

    @ApiModelProperty(value = "服务类型名称")
    private String serviceTypeName;

    @ApiModelProperty(value = "店铺名称")
    private String shopName;

    @ApiModelProperty(value = "交费方式（1线下2线上3其他）")
    private Integer payType;

    @ApiModelProperty(value = "交费方式名称")
    private String payTypeName;

    @ApiModelProperty(value = "交费时长")
    private Integer paymentDuration;

    @ApiModelProperty(value = "交费时长单位（1天2周3月4年）")
    private Integer paymentDurationType;

    @ApiModelProperty(value = "交费时长单位名称")
    private String paymentDurationTypeName;

    @ApiModelProperty(value = "交费金额")
    private BigDecimal payAmount;

    @ApiModelProperty(value = "状态（0草稿1待审核2审核通过3审核未通过）")
    private Integer state;

    @ApiModelProperty(value = "状态名称")
    private String stateName;

    @ApiModelProperty(value = "审核时间")
    private Date auditOpenTime;

    @ApiModelProperty(value = "交费时间（创建时间）")
    private Date gmtCreate;

    @ApiModelProperty(value = "修改时间")
    private Date gmtModified;
}
