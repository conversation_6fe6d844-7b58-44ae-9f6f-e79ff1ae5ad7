package scrbg.meplat.mall.config.rabbitMQ;//package scrbg.meplat.mall.config.rabbitMQ;
//
//import org.springframework.amqp.rabbit.connection.ConnectionFactory;
//import org.springframework.amqp.rabbit.core.RabbitTemplate;
//import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
//import org.springframework.amqp.support.converter.MessageConverter;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.Primary;
//
///**
// * <AUTHOR>
// * @create 2023-01-05 13:11
// */
//@Configuration
//public class MyRabbitMQConfig {
//    @Primary
//    @Bean
//    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory) {
//        RabbitTemplate rabbitTemplate = new RabbitTemplate(connectionFactory);
//        rabbitTemplate.setMessageConverter(messageConverter());
//        return rabbitTemplate;
//    }
//    @Bean
//    public MessageConverter messageConverter() {
//        // 使用json序列化器来序列化消息，发送消息时，消息对象会被序列化成json格式
//        return new Jackson2JsonMessageConverter();
//    }
//
//}
