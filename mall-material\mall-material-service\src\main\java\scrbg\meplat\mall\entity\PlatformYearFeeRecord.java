package scrbg.meplat.mall.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
/**
 * @描述：年费缴费记录
 * @作者: ye
 * @日期: 2024-01-24
 */
@ApiModel(value="年费缴费记录")
@Data
@TableName("platform_year_fee_record")
public class PlatformYearFeeRecord extends MustBaseEntity implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "缴费记录id")
    private String paymentRecordId;

    @ApiModelProperty(value = "缴费记录编号")
    private String paymentRecordUn;// 合同下载时间

    @ApiModelProperty("合同下载时间")
    private Date contractDate;

    @ApiModelProperty(value = "店铺id")
    private String shopId;

    @ApiModelProperty(value = "店铺名称")
    private String shopName;

    @ApiModelProperty(value = "企业id")
    private String enterpriseId;

    @ApiModelProperty(value = "企业名称")
    private String enterpriseName;

    @ApiModelProperty(value = "缴费金额")
    private BigDecimal payAmount;

    @ApiModelProperty(value = "缴费类型(1线下2线上3其他)")
    private Integer payType;

    @ApiModelProperty(value = "缴费时长")
    private Integer paymentDuration;

    @ApiModelProperty(value = "有效期开始日期")
    private LocalDate serveStartTime;

    @ApiModelProperty(value = "有效期结束日期")
    private LocalDate serveEndTime;

    @ApiModelProperty("合同下载状态（1已下载0未下载）")
    private Integer contractStatus;

    @ApiModelProperty(value = "旧的合同编号（只有在编号跨年情况修改合同编号的情况才需要使用该字段，作为数据追溯用）")
    private String oldContractNo;

    @ApiModelProperty("联系人")
    private String contact;

    @ApiModelProperty("联系电话")
    private String contactPhone;

    @ApiModelProperty("邮寄地址")
    private String mailAddress;

    @ApiModelProperty(value = "缴费时长类型（单位）（1天2周3月4年）")
    private Integer paymentDurationType;

    @ApiModelProperty(value = "审核时间")
    private Date auditOpenTime;

    @ApiModelProperty(value = "缴费记类型（1店铺年费2招标年费）")
    private Integer recordType;

    @ApiModelProperty(value = "状态（0草稿1待审核2审核通过3审核未通过）")
    private Integer state;

    @ApiModelProperty(value = "店铺缴费状态（0 入驻费用，1 续费）")
    private Integer payStatus;

    @ApiModelProperty(value = "合同编号")
    private String contractNo;

    @ApiModelProperty(value = "乐观锁")
    private Integer version;

    @ApiModelProperty(value = "附件")
    @TableField(exist = false)
    private List<PlatformFeeFile> files;

    @ApiModelProperty(value = "是否提交")
    @TableField(exist = false)
    private int submitAud;

    @ApiModelProperty(value = "审核历史")
    @TableField(exist = false)
    private List<AuditRecord> auditRecords;

    @ApiModelProperty(value = "上期年费有效期截止日期")
    @TableField(exist = false)
    private String previousFeeEndDate;

    @ApiModelProperty(value = "上期年费状态")
    @TableField(exist = false)
    private String previousFeeStatus;
}