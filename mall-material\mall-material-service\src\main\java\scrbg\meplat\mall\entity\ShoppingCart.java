package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @描述：购物车
 * @作者: y
 * @日期: 2022-11-25
 */
@ApiModel(value = "购物车")
@Data
@TableName("shopping_cart")
public class ShoppingCart extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "购物车id")
    private String cartId;

    @ApiModelProperty(value = "商品id")
    private String productId;

    @ApiModelProperty(value = "店铺id")
    private String shopId;

    @ApiModelProperty(value = "skuid")
    private String skuId;

    @ApiModelProperty(value = "用户id")
    private String userId;

    @ApiModelProperty(value = "购物车商品数量")
    private BigDecimal cartNum;

    @ApiModelProperty(value = "添加购物车时间")
    private Date cartTime;

    @ApiModelProperty(value = "税率")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "添加购物车时商品价格")
    private BigDecimal productPrice;

    @ApiModelProperty(value = "选择的规格")
    private String skuProps;

    @ApiModelProperty(value = "商品类型：0物资 1设备 2周材（设备租赁） 3周材（设备） 4二手设备 5租赁设备")
    private Integer productType;

    @ApiModelProperty(value = "状态")
    private Integer state;

    @ApiModelProperty(value = "购物车图片")
    private String cartImg;

    @ApiModelProperty(value = "租赁时长")
    private BigDecimal leaseNum;

    @ApiModelProperty(value = "租赁单位（天月年）")
    private String leaseUnit;

    @ApiModelProperty(value = "订单总价格")
    @TableField(exist = false)
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "实际支付总价格")
    @TableField(exist = false)
    private BigDecimal actualAmount;

    @ApiModelProperty(value = "是否选中")
    private Boolean checked;

    @ApiModelProperty(value = "区域地址")
    private String zoneAddr;

    @ApiModelProperty(value = "区域id")
    private String zoneId;

    @ApiModelProperty(value = "账期，1个月账期，2个月账期，3个月账期")
    private Integer paymentPeriod;
}
