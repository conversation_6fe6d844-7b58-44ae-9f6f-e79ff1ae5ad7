package scrbg.meplat.mall.dto.outer.login;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @program: maill_api
 * @description: 响应用户数据对象
 * @author: 代文翰
 * @create: 2023-11-20 11:41
 **/
@Data
public class UserInfo {
    @ApiModelProperty(value = "供应商名称")

    private String enterpriseName;
    @ApiModelProperty(value = "统一社会信用代码")

    private String socialCreditCode;
    @ApiModelProperty(value = "企业类型：0：个体户  1：企业  2：内部供应商")

    private Integer enterpriseType;
    @ApiModelProperty(value = "法定代表人（经营者）")

    private String legalRepresentative;
    @ApiModelProperty(value = "注册日期")

    private Date creationTime;

    @ApiModelProperty(value = "注册资本(万元)")
    private BigDecimal registeredCapital;
    @ApiModelProperty(value = "注册地址（经营场所）")

    private String placeOfBusiness;

    @ApiModelProperty(value = "注册详细地址")

    private String detailedAddress;
    @ApiModelProperty(value = "内部供应商关联机构集合")
    List<Map<String,Object>> orgList;
}
