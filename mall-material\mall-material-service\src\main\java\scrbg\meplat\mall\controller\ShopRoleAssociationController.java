package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.entity.ShopRoleAssociation;
import scrbg.meplat.mall.service.ShopRoleAssociationService;

import java.util.List;

/**
 * @描述：店铺—角色类型关联表控制类
 * @作者: y
 * @日期: 2022-11-13
 */
@RestController
@RequestMapping("/shopRoleAssociation")
@Api(tags = "店铺—角色类型关联表")
public class ShopRoleAssociationController {

    @Autowired
    public ShopRoleAssociationService shopRoleAssociationService;

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<ShopRoleAssociation> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = shopRoleAssociationService.queryPage(jsonObject, new LambdaQueryWrapper<ShopRoleAssociation>());
        return PageR.success(page);
    }

    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<ShopRoleAssociation> findById(String id) {
        ShopRoleAssociation shopRoleAssociation = shopRoleAssociationService.getById(id);
        return R.success(shopRoleAssociation);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody ShopRoleAssociation shopRoleAssociation) {
        shopRoleAssociationService.create(shopRoleAssociation);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody ShopRoleAssociation shopRoleAssociation) {
        shopRoleAssociationService.update(shopRoleAssociation);
        return R.success();
    }

    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        shopRoleAssociationService.delete(id);
        return R.success();
    }


    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")

    public R deleteBatch(@RequestBody List<String> ids) {
        shopRoleAssociationService.removeByIds(ids);
        return R.success();
    }
}

