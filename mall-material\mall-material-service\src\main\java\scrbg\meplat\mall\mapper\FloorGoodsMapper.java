package scrbg.meplat.mall.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import scrbg.meplat.mall.entity.BiddingBidRecord;
import scrbg.meplat.mall.entity.FloorGoods;
import scrbg.meplat.mall.vo.bidding.BiddingPurchaseItemVO;
import scrbg.meplat.mall.vo.floor.website.WFloorGoodsVO;

import java.util.List;

/**
 * @描述：楼层显示的商品 Mapper 接口
 * @作者: y
 * @日期: 2022-11-10
 */
@Mapper
@Repository
public interface FloorGoodsMapper extends BaseMapper<FloorGoods> {
//@Select("<script>" +
//        "Select  count(*)  from floor_goods where goods_id in " +
//        "<foreach collection='ids' open='(' item='id_' separator=',' close=')'> #{id_}" +
//        "</foreach>" +
//        "</script>")
//int selectRealByProductIdIds(List<String> ids);


    @Select("<script>" +
            "Select  floor_name_text,product_name,floor_name from floor_goods,product,floor where  floor_goods.goods_id=product.product_id and floor_goods.floor_id=floor.floor_id  and floor_goods.is_delete=0  and floor.is_delete=0 and goods_id in " +
            "<foreach collection='ids' open='(' item='id_' separator=',' close=')'> #{id_}" +
            "</foreach>" +
            "</script>")
    List<WFloorGoodsVO> selectRealByProductIdIds(List<String> ids);



    @Delete("<script>" +
            "delete from floor_goods where goods_id in " +
            "<foreach collection='ids' open='(' item='id_' separator=',' close=')'> #{id_}" +
            "</foreach>" +
            "</script>")
    void removeRealByProductIdIds(List<String> ids);

    List<WFloorGoodsVO> wFloorGoodsVOByList(@Param("ew") QueryWrapper<WFloorGoodsVO> goodsWrapper);


//    @Delete("DELETE FROM floor_goods WHERE goods_id=#{goodsId}")
//
//    void removeRealByProductIdIds(List<String> goodsId);
//












}
