package scrbg.meplat.mall.service.impl;

import scrbg.meplat.mall.entity.SynthesizeTemporaryDtl;
import scrbg.meplat.mall.mapper.SynthesizeTemporaryDtlMapper;
import scrbg.meplat.mall.service.SynthesizeTemporaryDtlService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import java.util.List;
/**
 * @描述： 服务类
 * @作者: ye
 * @日期: 2023-10-07
 */
@Service
public class SynthesizeTemporaryDtlServiceImpl extends ServiceImpl<SynthesizeTemporaryDtlMapper, SynthesizeTemporaryDtl> implements SynthesizeTemporaryDtlService{
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<SynthesizeTemporaryDtl> queryWrapper) {
        IPage<SynthesizeTemporaryDtl> page = this.page(
        new Query<SynthesizeTemporaryDtl>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(SynthesizeTemporaryDtl synthesizeTemporaryDtl) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(synthesizeTemporaryDtl);
    }

    @Override
    public void update(SynthesizeTemporaryDtl synthesizeTemporaryDtl) {
        super.updateById(synthesizeTemporaryDtl);
    }


    @Override
    public SynthesizeTemporaryDtl getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

     @Override
     public void deleteBatch(List<String> ids ) {
        super.removeByIds(ids);
        }


        }
