package scrbg.meplat.mall.controller.plan;

import java.util.List;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import scrbg.meplat.mall.dto.plan.PlanDTO;
import scrbg.meplat.mall.entity.plan.Plan;
import scrbg.meplat.mall.service.plan.PlanDetailService;
import scrbg.meplat.mall.service.plan.PlanService;

/**
 * @描述：采购计划主表控制类
 * @作者: ye
 * @日期: 2025-05-27
 */
@RestController
@RequestMapping("/plan")
@Api(tags = "采购计划主表")
public class PlanController {

    @Autowired
    public PlanService planService;

    @Autowired
    public PlanDetailService planDetailService;

    @GetMapping("/page")
    @ApiOperation(value = "根据实体属性分页查询")
    public PageR<Plan> listByPage(@RequestParam PlanDTO dto) {
        PageUtils page = planService.queryPage(dto);
        return PageR.success(page);
    }

    @GetMapping("/findId")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "ID", required = true,
            dataType = "String", paramType = "path")
    })
    public R<Plan> findById(String id) {
        Plan plan = planService.getById(id);
        return R.success(plan);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody @Valid Plan plan) {
        planService.create(plan);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody @Valid Plan plan) {
        planService.update(plan);
        return R.success();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "ID", required = true,
            dataType = "String", paramType = "path")
    })
    public R delete(String id) {
        planService.delete(id);
        return R.success();
    }

    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")
    public R deleteBatch(@RequestBody List<String> ids) {
        planService.deleteBatch(ids);
        return R.success();
    }
}

