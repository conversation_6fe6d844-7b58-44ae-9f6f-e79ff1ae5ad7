package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import scrbg.meplat.mall.entity.SupplierReconciliation;
import scrbg.meplat.mall.service.SupplierReconciliationDtlService;
import scrbg.meplat.mall.entity.SupplierReconciliationDtl;

import java.util.List;

/**
 * @描述：物资验收明细控制类
 * @作者: ye
 * @日期: 2023-08-15
 */
@RestController
@RequestMapping("/supplierReconciliationDtl")
@Api(tags = "物资验收明细")
public class SupplierReconciliationDtlController {

    @Autowired
    public SupplierReconciliationDtlService supplierReconciliationDtlService;

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<SupplierReconciliationDtl> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = supplierReconciliationDtlService.queryPage(jsonObject, new LambdaQueryWrapper<SupplierReconciliationDtl>());
        return PageR.success(page);
    }

    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<SupplierReconciliationDtl> findById(String id) {
        SupplierReconciliationDtl supplierReconciliationDtl = supplierReconciliationDtlService.getById(id);
        return R.success(supplierReconciliationDtl);
    }


    @PostMapping("/getReconciliationDtlList")
    @ApiOperation(value = "")
    @DynamicParameters(name = "查询发货单和退货单可生成的二级对账单", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "userType", value = "用户类型  1 自营店 2 供应商", dataTypeClass = Integer.class),
            @DynamicParameter(name = "businessType", value = "业务类型  1 合同 2 清单 6 大宗临购", dataTypeClass = Integer.class),
            @DynamicParameter(name = "startDate", value = "创建时间开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endDate", value = "创建时间结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "supplierOrgId", value = "自营店名称", dataTypeClass = String.class),
            @DynamicParameter(name = "twoSupplierOrgId", value = "二级供应商名称", dataTypeClass = Integer.class),})
    public PageR<SupplierReconciliationDtl> selectSupplierReconciliation(@RequestBody JSONObject jsonObject) {
        PageUtils page = supplierReconciliationDtlService.getReconciliationDtlList(jsonObject ,new QueryWrapper<SupplierReconciliationDtl>());
        return PageR.success(page);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody SupplierReconciliationDtl supplierReconciliationDtl) {
        supplierReconciliationDtlService.create(supplierReconciliationDtl);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody SupplierReconciliationDtl supplierReconciliationDtl) {
        supplierReconciliationDtlService.update(supplierReconciliationDtl);
        return R.success();
    }

    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        supplierReconciliationDtlService.delete(id);
        return R.success();
    }


    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")
    public R deleteBatch(@RequestBody List<String> ids) {
        supplierReconciliationDtlService.removeByIds(ids);
        return R.success();
    }
}

