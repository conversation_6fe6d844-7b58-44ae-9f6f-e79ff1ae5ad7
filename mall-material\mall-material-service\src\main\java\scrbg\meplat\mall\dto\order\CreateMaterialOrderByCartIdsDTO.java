package scrbg.meplat.mall.dto.order;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-11-07 10:45
 */
@Data
public class CreateMaterialOrderByCartIdsDTO {

    @ApiModelProperty(value = "购物车ids")
    private List<String> cartIds;

    @ApiModelProperty(value = "收货人", required = true)
    @NotEmpty(message = "收货人不能为空！")
    private String receiverName;

    @ApiModelProperty(value = "收货人手机号", required = true)
    @NotEmpty(message = "收货人手机号不能为空！")
    private String receiverMobile;

    @ApiModelProperty(value = "收货地址", required = true)
    @NotEmpty(message = "收货地址不能为空！")
    private String receiverAddress;

    @ApiModelProperty(value = "支付金额", required = true)
    @NotNull(message = "支付金额不能为空！")
    private BigDecimal payPrice;

    @ApiModelProperty(value = "商品id（如果不是购物车结算这必传）")
    private String productId;

    @ApiModelProperty(value = "sku信息（如果不是购物车结算这必传）")
    private List<BuySkuInfoDTO> buySkuInfoDTOS;

    @ApiModelProperty(value = "订单备注")
    private String orderRemark;

//    @ApiModelProperty(value = "配送方式")
//
//    private Integer deliveryType;

    @ApiModelProperty(value = "支付方式 1线上支付 2内部结算 3线下转账", required = true)
    @NotNull(message = "支付方式不能为空！")
    private Integer payWay;

    @ApiModelProperty(value = "线上支付方式 1:微信 2:支付宝 3:银联 ")
    private Integer payType;

    @ApiModelProperty(value = "审核人员id 内部人员立即下单，弹出审核人员")
    private String auditorId;

    @ApiModelProperty(value = "审核人员名称 内部人员立即下单，弹出审核人员")
    private String auditorName;

}
