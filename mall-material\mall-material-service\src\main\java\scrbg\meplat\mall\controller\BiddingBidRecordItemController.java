package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import scrbg.meplat.mall.vo.bidding.GetBidingRecordItemInfoVO;
import scrbg.meplat.mall.service.BiddingBidRecordItemService;
import scrbg.meplat.mall.entity.BiddingBidRecordItem;

import java.util.List;

/**
 * @描述：竞价记录控制类
 * @作者: ye
 * @日期: 2023-07-19
 */
@RestController
@RequestMapping("/")
@Api(tags = "竞价记录")
public class BiddingBidRecordItemController {

    @Autowired
    public BiddingBidRecordItemService biddingBidRecordItemService;

    @PostMapping("shopManage/biddingBidRecordItem/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = Integer.class),
            @DynamicParameter(name = "bidRecordId", value = "竞价记录id", dataTypeClass = String.class,required = true)
    })
    public PageR<BiddingBidRecordItem> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = biddingBidRecordItemService.queryPage(jsonObject, new LambdaQueryWrapper<BiddingBidRecordItem>());
        return PageR.success(page);
    }

    @GetMapping("biddingBidRecordItem/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<BiddingBidRecordItem> findById(String id) {
        BiddingBidRecordItem biddingBidRecordItem = biddingBidRecordItemService.getById(id);
        return R.success(biddingBidRecordItem);
    }

    @PostMapping("biddingBidRecordItem/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody BiddingBidRecordItem biddingBidRecordItem) {
        biddingBidRecordItemService.create(biddingBidRecordItem);
        return R.success();
    }

    @PostMapping("biddingBidRecordItem/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody BiddingBidRecordItem biddingBidRecordItem) {
        biddingBidRecordItemService.update(biddingBidRecordItem);
        return R.success();
    }

    @GetMapping("biddingBidRecordItem/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        biddingBidRecordItemService.delete(id);
        return R.success();
    }


    @PostMapping("biddingBidRecordItem/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")
    public R deleteBatch(@RequestBody List<String> ids) {
        biddingBidRecordItemService.removeByIds(ids);
        return R.success();
    }


    @PostMapping("shopManage/biddingBidRecordItem/getBidingRecordItemInfo")
    @ApiOperation(value = "根据记录id获取记录明细")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = Integer.class),
            @DynamicParameter(name = "bidRecordId", value = "竞价记录id", dataTypeClass = String.class,required = true)
    })
    public R<GetBidingRecordItemInfoVO> getBidingRecordItemInfo(@RequestBody JSONObject jsonObject) {
        GetBidingRecordItemInfoVO vo = biddingBidRecordItemService.getBidingRecordItemInfo(jsonObject, new LambdaQueryWrapper<BiddingBidRecordItem>());
        return R.success(vo);
    }

    @PostMapping("platform/biddingBidRecordItem/getPlatformBidingRecordItemInfo")
    @ApiOperation(value = "根据记录id获取记录明细-平台")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = Integer.class),
            @DynamicParameter(name = "bidRecordId", value = "竞价记录id", dataTypeClass = String.class,required = true)
    })
    public R<GetBidingRecordItemInfoVO> getPlatformBidingRecordItemInfo(@RequestBody JSONObject jsonObject) {
        GetBidingRecordItemInfoVO vo = biddingBidRecordItemService.getPlatformBidingRecordItemInfo(jsonObject, new LambdaQueryWrapper<BiddingBidRecordItem>());
        return R.success(vo);
    }

}

