package scrbg.meplat.mall.dto.plan;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.scrbg.common.utils.PageParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @描述：采购计划主表查询参数
 * @作者: tanfei
 * @日期: 2025-05-27
 */
@ApiModel(value = "采购计划主表查询参数")
@Data
@EqualsAndHashCode(callSuper = true)
public class PlanDTO extends PageParam implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "计划编号")
    private String billNo;

    @ApiModelProperty(value = "计划类型 1、零星采购计划 2、大宗临购 3、周转材料")
    private String type;

    @ApiModelProperty(value = "计划状态(0草稿1待审核2已审核3已执行4已完成5已作废)")
    private String state;

    @ApiModelProperty(value = "计划日期")
    private Date billDate;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "组织机构ID")
    private String orgId;

    @ApiModelProperty(value = "组织机构名称")
    private String orgName;

    @ApiModelProperty(value = "记录人ID")
    private String recorderId;

    @ApiModelProperty(value = "记录人姓名")
    private String recorderName;

    @ApiModelProperty(value = "年度")
    private Integer year;

    @ApiModelProperty(value = "月份")
    private Integer month;

    @ApiModelProperty(value = "计划金额")
    private BigDecimal planAmount;

    @ApiModelProperty(value = "含税计划金额")
    private BigDecimal taxPlanAmount;

    @ApiModelProperty(value = "税率")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "税额")
    private BigDecimal taxAmount;

    @ApiModelProperty(value = "修改人名称")
    private String modifyName;

    @ApiModelProperty(value = "修改人id")
    private String modifyId;

    @ApiModelProperty(value = "是否查询下级")
    private boolean sub;

}