package scrbg.meplat.mall.controller.website;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.dto.product.CheckIsPutawayPDTO;
import scrbg.meplat.mall.entity.Product;
import scrbg.meplat.mall.service.ProductService;
import scrbg.meplat.mall.vo.product.website.ProductDetailDealRecordVO;
import scrbg.meplat.mall.vo.product.website.device.WDeviceInfoVO;
import scrbg.meplat.mall.vo.product.website.device.WDeviceVO;
import scrbg.meplat.mall.vo.product.website.material.WMaterialVO;
import scrbg.meplat.mall.vo.product.website.material.WMaterialnfoVO;
import scrbg.meplat.mall.vo.product.website.restsServe.WOtherServicesVO;
import scrbg.meplat.mall.vo.product.website.restsServe.WRepairVO;
import scrbg.meplat.mall.vo.w.CheckIsPutawayVO;

import javax.validation.Valid;
import java.util.List;

/**
 * @描述：店铺商品信息控制类
 * @作者: y
 * @日期: 2022-11-02
 */
@RestController
@RequestMapping("/w/product")
@ApiSort(value = 100)
@Api(tags = "商品（前台）")
public class WebsiteProductController {

    @Autowired
    public ProductService productService;



    // ----------------------------物资------------------------------------------------------------------------------

    /**
     * 根据物资id获取物资商品信息
     *
     * @param productId
     * @return
     */
    @GetMapping("/materialInfo")
    @ApiOperation(value = "物资详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "productId", value = "商品ID", required = true,
                    dataType = "String", paramType = "query"),
    })
    public R materialInfo(String productId) {
        WMaterialnfoVO vo = productService.materialInfo(productId);
        return R.success(vo);
    }


    @PostMapping("/materialPageList")
    @ApiOperation(value = "物资列表")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "productType", value = "商品类型：0物资  2周材（物资）", required = true,
                    dataTypeClass = Integer.class),
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "shopId", value = "店铺id", dataTypeClass = String.class),
            @DynamicParameter(name = "serialNum", value = "商品编号", dataTypeClass = String.class),
            @DynamicParameter(name = "province", value = "省", dataTypeClass = String.class),
            @DynamicParameter(name = "city", value = "市", dataTypeClass = String.class),
            @DynamicParameter(name = "keywords", value = "关键字（商品名称、店铺名称、规格名称）", dataTypeClass = String.class),
            @DynamicParameter(name = "classId", value = "分类ID", dataTypeClass = String.class),
            @DynamicParameter(name = "brandId", value = "品牌Id", dataTypeClass = String.class),
            @DynamicParameter(name = "brandName", value = "品牌名称", dataTypeClass = String.class),
            @DynamicParameter(name = "isBusiness", value = "营业方式（0:全部1:平台自营2:路桥内部店3:其它）", dataTypeClass = Integer.class),
            @DynamicParameter(name = "belowPrice", value = "以下价格（商品最低价）", dataTypeClass = String.class),
            @DynamicParameter(name = "abovePrice", value = "以上价格（商品最低价）", dataTypeClass = String.class),
            @DynamicParameter(name = "orderBy", value = "排序字段(0:综合排序1:按价格2:按更新时间", dataTypeClass = String.class, required = true),
    })
    public PageR<WMaterialVO> materialPageList(@RequestBody JSONObject jsonObject) {
        PageUtils page = productService.materialPageList(jsonObject, Wrappers.lambdaQuery(Product.class));
        return PageR.success(page);
    }

    // ----------------------------设备------------------------------------------------------------------------------

//    @GetMapping("/deviceInfo")
//    @ApiOperation(value = "设备详情")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "productId", value = "商品ID", required = true,
//                    dataType = "String", paramType = "query"),
//    })
//    public R<WDeviceInfoVO> deviceInfo(String productId) {
//        WDeviceInfoVO vo = productService.deviceInfo(productId);
//        return R.success(vo);
//    }





    @PostMapping("/getProductDetailDealRecord")
    @ApiOperation(value = "获取商品交易记录")
    public R<PageUtils> getProductDetailDealRecord(@RequestBody JSONObject jsonObject) {
        PageUtils vo = productService.getProductDetailDealRecord(jsonObject);
        return R.success(vo);
    }


    @PostMapping("/getRecordListUserInfo")
    @ApiOperation(value = "补充成交记录用户信息")
    public scrbg.meplat.mall.util.R<List<ProductDetailDealRecordVO>> getRecordListUserInfo (@RequestBody List<ProductDetailDealRecordVO> dtos) {
        List<ProductDetailDealRecordVO> vos = productService.getRecordListUserInfo(dtos);
        return scrbg.meplat.mall.util.R.success(vos);
    }

//    @GetMapping("/dfadfas")
//    @ApiOperation(value = "设备详情")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "productId", value = "商品ID", required = true,
//                    dataType = "String", paramType = "query"),
//    })
//    public R sdfaas(String productId) {
//        Product byId = productService.getById(productId);
//        for (int i = 0; i < 64; i++) {
//            byId.setProductId(null);
//            productService.save(byId);
//        }
//        return R.success();
//    }


    /**
     * pcwp1 检查商品是否上架
     */

    @PostMapping("/pcwpContract/checkIsPutaway")
    @ApiOperation(value = "检查商品是否上架")
    public R<List<CheckIsPutawayVO>> checkIsPutaway(@Valid @RequestBody CheckIsPutawayPDTO dto) {
        List<CheckIsPutawayVO> vos =  productService.checkIsPutaway(dto);
        return R.success(vos);
    }
}

