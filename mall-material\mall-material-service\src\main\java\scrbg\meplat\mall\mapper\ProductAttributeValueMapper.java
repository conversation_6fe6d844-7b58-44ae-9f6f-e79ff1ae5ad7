package scrbg.meplat.mall.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;
import scrbg.meplat.mall.entity.ProductAttributeValue;

/**
 * @描述：商品属性值 Mapper 接口
 * @作者: y
 * @日期: 2022-11-02
 */
@Mapper
@Repository
public interface ProductAttributeValueMapper extends BaseMapper<ProductAttributeValue> {


    @Delete("DELETE FROM product_attribute_value WHERE product_id=#{id}")
    void deleteByProductId(String id);
}
