package scrbg.meplat.mall.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import scrbg.meplat.mall.entity.SupplierReconciliation;
import scrbg.meplat.mall.entity.SupplierReconciliationDtl;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;
import org.apache.ibatis.annotations.Mapper;
import scrbg.meplat.mall.entity.SupplierReconciliationDtlExcel;

import java.util.List;

/**
 * @描述：物资验收明细 Mapper 接口
 * @作者: ye
 * @日期: 2023-08-15
 */
@Mapper
@Repository
public interface SupplierReconciliationDtlMapper extends BaseMapper<SupplierReconciliationDtl> {

    List<SupplierReconciliationDtl> findByCondition(IPage<SupplierReconciliationDtl> pages,  @Param("ew") QueryWrapper<SupplierReconciliationDtl> q);

    /**
     * 根据对账单ID批量查询收料明细
     *
     * @param billId 对账单ID
     * @return 导出对账单明细列表
     */
    List<SupplierReconciliationDtlExcel> getReceiptDetailByBillId(String billId);
}
