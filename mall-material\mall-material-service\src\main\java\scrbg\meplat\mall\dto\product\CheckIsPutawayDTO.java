package scrbg.meplat.mall.dto.product;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @create 2023-05-07 17:44
 */
@Data
public class CheckIsPutawayDTO {



    @ApiModelProperty(value = "商品名称",required = true)
    @NotEmpty(message = "商品名称不能为空！")
    private String productName;

    @ApiModelProperty(value = "规格型号",required = true)
    @NotEmpty(message = "规格型号不能为空！")
    private String size;
}
