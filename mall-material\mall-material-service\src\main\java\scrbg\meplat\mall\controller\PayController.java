package scrbg.meplat.mall.controller;

import com.github.xiaoymin.knife4j.annotations.ApiSort;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 叶子
 */
@RestController
@RequestMapping("/pay")
@ApiSort(value = 100)
@Api(tags = "支付回调")
public class PayController {

    /**
     * 回调接口
     */
    @RequestMapping("/material/callback")
    public String paySuccess(HttpServletRequest request) {
        return "";
    }

}