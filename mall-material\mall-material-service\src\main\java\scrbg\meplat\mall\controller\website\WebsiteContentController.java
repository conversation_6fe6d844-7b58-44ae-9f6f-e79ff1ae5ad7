package scrbg.meplat.mall.controller.website;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.entity.Content;
import scrbg.meplat.mall.service.ContentService;

import java.util.List;

/**
 * @描述：内容控制类
 * @作者: sund
 * @日期: 2022-11-07
 */
@RestController
@RequestMapping("/w/richContent")
@ApiSort(value = 100)
@Api(tags = "内容（前台）")
public class WebsiteContentController {

    @Autowired
    MallConfig mallConfig;
    @Autowired
    public ContentService contentService;

    @GetMapping("/findByProgramaKey")
    @ApiOperation(value = "查询发布的内容")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "programaKey", value = "programaKey", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<Content> findByProgramaKey(String programaKey) {
        List<Content> results = contentService.findAllContent(programaKey,"1");
        if(CollectionUtils.isEmpty(results)){
            return R.success();
        }
        return R.success(results.get(0));
    }

    @PostMapping("/findByProgramaKeyUser")
    public R<Content> findByProgramaKeyUser(@RequestBody Content content) {
        QueryWrapper<Content> wrapper = new QueryWrapper();
        wrapper.eq("programa_key", content.getProgramaKey());
        wrapper.eq("agreement_type", content.getAgreementType());
        wrapper.eq("state", 1);
        wrapper.eq("mall_type", mallConfig.mallType);
        List<Content> results = contentService.list(wrapper);
        if(CollectionUtils.isEmpty(results)){
            return R.success();
        }
        return R.success(results.get(0));
    }

}

