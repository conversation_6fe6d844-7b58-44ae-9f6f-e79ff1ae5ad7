package scrbg.meplat.mall.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MallBaseEntity;

/**
 * 流程配置
 * <AUTHOR>
 * @date: 2025年6月20日 上午9:28:16
 */
@ApiModel(value = "流程配置表")
@Data
@TableName("process_config")
public class ProcessConfig extends MallBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "流程ID")
    private String processId;

    @ApiModelProperty(value = "流程名称")
    private String processName;

    @ApiModelProperty(value = "系统名称")
    private String systemName;

    @ApiModelProperty(value = "系统编号")
    private String systemNo;

    @ApiModelProperty(value = "备注")
    private String remark;
    
    @ApiModelProperty(value = "修改人ID")
    private String modifyId;
    
    @ApiModelProperty(value = "修改人名称")
    private String modifyName;
    
    @ApiModelProperty(value = "创建时间")
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "gmt_modified", fill = FieldFill.INSERT_UPDATE)
    private Date gmtModified;
    
    @ApiModelProperty(value = "逻辑删除 -1: 删除 0:未删除")
    @TableField(value = "is_delete", fill = FieldFill.INSERT)
    private Integer isDelete;

    @ApiModelProperty(value = "排序")
    private Integer sort;

}