//package scrbg.meplat.mall.config.minio;
//
//import io.minio.MinioClient;
//import lombok.Data;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
//@Data
//@Configuration
//@ConfigurationProperties(prefix = "minio")
//public class MinioConfig {
//
//    private String endpoint;
//    private String accessKey;
//    private String secretKey;
//
//    @Bean
//    public MinioClient getMinioClient() {
//        MinioClient minioClient = new MinioClient.Builder()
//                .endpoint(endpoint)
//                .credentials(accessKey,secretKey)
//                .build();
//        return minioClient;
//    }
//
//}
