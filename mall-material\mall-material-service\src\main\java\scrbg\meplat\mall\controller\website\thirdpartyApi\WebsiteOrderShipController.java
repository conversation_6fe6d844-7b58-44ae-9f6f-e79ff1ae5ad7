package scrbg.meplat.mall.controller.website.thirdpartyApi;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.config.redis.redisson.NotResubmit;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.service.*;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;
import scrbg.meplat.mall.vo.ship.OrderShipVo;
import scrbg.meplat.mall.vo.ship.SubmitOrderShipDtl;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/w/api/thirdParty/orderShip")
@ApiSort(value = 9999)
@Api(tags = "第三方发货单api")
public class WebsiteOrderShipController {

    @Autowired
    private OrdersService ordersService;

    @Autowired
    private UserService userService;

    @Autowired
    private OrderItemService orderItemService;

    @Autowired
    private OrderShipService orderShipService;
    @Autowired
    private OrderShipDtlService orderShipDtlService;

    @PostMapping("/list/supplierOrderShipPageList")
    @ApiOperation(value = "查询供应商订单列表（普通供应商发货单）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数（默认20）", dataTypeClass = Integer.class),
            @DynamicParameter(name = "orderSn", value = "订单编号", dataTypeClass = String.class),
            @DynamicParameter(name = "billSn", value = "发货单编号", dataTypeClass = String.class),
            @DynamicParameter(name = "enterpriseName", value = "采购机构名称", dataTypeClass = String.class),
            @DynamicParameter(name = "keywords", value = "关键字（订单号、发货单编号，采购机构名称）", dataTypeClass = String.class),
            @DynamicParameter(name = "type", value = "发货单状态（0未发货。1发货中，2已收货，3.已退货）", dataTypeClass = Integer.class),
            @DynamicParameter(name = "productType", value = "订单类型：10:零星采购订单, 12:大宗月供订单", dataTypeClass = Integer.class),
            @DynamicParameter(name = "sort", value = "排序方式：1:创建时间, 2:发货时间 ，3确定收货时间 不传默认为创建时间", dataTypeClass = Integer.class),
    })
    public PageR<OrderShip> supplierOrderPageList(HttpServletRequest request, @RequestBody JSONObject jsonObject) {
        String privateKey = request.getHeader("privateKey");
        if (StringUtils.isBlank(privateKey) || !privateKey.equals(HeaderKey.key)) {
            throw new BusinessException("请求无效！");
        }
        LambdaQueryWrapper<OrderShip> q = Wrappers.lambdaQuery(OrderShip.class);
        // 初始查询条件
        q.isNull(OrderShip::getOtherOrderSn);

        String userId = request.getHeader("userId");
        User user = userService.lambdaQuery().eq(User::getUserId, userId)
                .select(User::getUserId, User::getEnterpriseId).one();
        q.eq(OrderShip::getSupplierId, user.getEnterpriseId());

        Integer page = (Integer) jsonObject.get("page");
        Integer limit = (Integer) jsonObject.get("limit");
        if (page == null) page = 1;
        if (limit == null) limit = 20;
        if (limit > 2000) limit = 20;

        String orderSn = (String) jsonObject.get("orderSn");
        q.eq(StringUtils.isNotBlank(orderSn), OrderShip::getOrderSn, orderSn);
        String billSn = (String) jsonObject.get("billSn");
        q.eq(StringUtils.isNotBlank(billSn), OrderShip::getBillSn, billSn);
        String enterpriseName = (String) jsonObject.get("enterpriseName");
        q.eq(StringUtils.isNotBlank(enterpriseName), OrderShip::getEnterpriseName, enterpriseName);
        q.orderByDesc(OrderShip::getGmtCreate);
        String keywords = (String) jsonObject.get("keywords");
        if (org.apache.commons.lang.StringUtils.isNotBlank(keywords)) {
            q.and((t) -> {
                t.like(OrderShip::getOrderSn, keywords)
                        .or()
                        .like(OrderShip::getBillSn, keywords)
                        .or()
                        .like(OrderShip::getEnterpriseName, keywords);
            });
        }
        Integer type = (Integer) jsonObject.get("type");
        if (type != null) {
            q.eq(OrderShip::getType, type);
        }
        Integer sort = (Integer) jsonObject.get("sort");
        if (sort != null) {
            if (sort == 1) {
                q.orderByDesc(OrderShip::getGmtCreate);
            } else if (sort == 2) {
                q.orderByDesc(OrderShip::getShipData);
            } else {
                q.orderByDesc(OrderShip::getConfirmTime);
            }
        } else {
            q.orderByDesc(OrderShip::getGmtCreate);
        }
        q.eq(OrderShip::getOrderClass,1);
        Integer productType = (Integer) jsonObject.get("productType");
        if (productType != null) {
            if (productType == 10 || productType == 12) {
                q.eq(OrderShip::getProductType, productType);
            }
        }
        IPage<OrderShip> iPage = orderShipService.page(
                new Query<OrderShip>().getPage(jsonObject),
                q
        );
        return PageR.success(new PageUtils(iPage));
    }

    @PostMapping("/list/twoSupplierOrderShipPageList")
    @ApiOperation(value = "查询供应商二级发货单列表（二级供应商）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数（默认20）", dataTypeClass = Integer.class),
            @DynamicParameter(name = "orderSn", value = "订单编号", dataTypeClass = String.class),
            @DynamicParameter(name = "billSn", value = "发货单编号", dataTypeClass = String.class),
            @DynamicParameter(name = "enterpriseName", value = "采购机构名称", dataTypeClass = String.class),
            @DynamicParameter(name = "keywords", value = "关键字（订单号、发货单编号，采购机构名称）", dataTypeClass = String.class),
            @DynamicParameter(name = "type", value = "发货单状态（0未发货。1发货中，2已收货，3.已退货）", dataTypeClass = Integer.class),
            @DynamicParameter(name = "productType", value = "订单类型：10:零星采购订单, 12:大宗月供订单", dataTypeClass = Integer.class),
            @DynamicParameter(name = "sort", value = "排序方式：1:创建时间, 2:发货时间 ，3确定收货时间 不传默认为创建时间", dataTypeClass = Integer.class),
    })
    public PageR<OrderShip> twoSupplierOrderPageList(HttpServletRequest request, @RequestBody JSONObject jsonObject) {
        String privateKey = request.getHeader("privateKey");
        if (StringUtils.isBlank(privateKey) || !privateKey.equals(HeaderKey.key)) {
            throw new BusinessException("请求无效！");
        }
        LambdaQueryWrapper<OrderShip> q = Wrappers.lambdaQuery(OrderShip.class);
        // 初始查询条件
        q.eq(OrderShip::getOrderClass, 3);


        String userId = request.getHeader("userId");
        User user = userService.lambdaQuery().eq(User::getUserId, userId)
                .select(User::getUserId, User::getEnterpriseId).one();
        q.eq(OrderShip::getShipEnterpriseId, user.getEnterpriseId());

        Integer page = (Integer) jsonObject.get("page");
        Integer limit = (Integer) jsonObject.get("limit");
        if (page == null) page = 1;
        if (limit == null) limit = 20;
        if (limit > 2000) limit = 20;

        String orderSn = (String) jsonObject.get("orderSn");
        String keywords = (String) jsonObject.get("keywords");
        String billSn = (String) jsonObject.get("orderSn");
        Integer type = (Integer) jsonObject.get("type");
        Integer sort = (Integer) jsonObject.get("sort");
        q.eq(StringUtils.isNotBlank(orderSn), OrderShip::getOtherOrderSn, orderSn);
        q.eq(StringUtils.isNotBlank(billSn), OrderShip::getBillSn, billSn);
        if (org.apache.commons.lang.StringUtils.isNotBlank(keywords)) {
            q.and((t) -> {
                t.like(OrderShip::getOrderSn, keywords)
                        .or()
                        .like(OrderShip::getBillSn, keywords);

            });
        }

        if (type != null) {
            q.eq(OrderShip::getType, type);
        }
        if (sort != null) {
            if (sort == 1) {
                q.orderByDesc(OrderShip::getGmtCreate);
            } else if (sort == 2) {
                q.orderByDesc(OrderShip::getShipData);
            } else {
                q.orderByDesc(OrderShip::getConfirmTime);
            }
        } else {
            q.orderByDesc(OrderShip::getGmtCreate);
        }
        q.eq(OrderShip::getOrderClass,3);
        Integer productType = (Integer) jsonObject.get("productType");
        if (productType != null) {
            if (productType == 10 || productType == 12 || productType == 13) {
                q.eq(OrderShip::getProductType, productType);
            }
        }
        IPage<OrderShip> iPage = orderShipService.page(
                new Query<OrderShip>().getPage(jsonObject),
                q
        );
        for (OrderShip record : iPage.getRecords()) {
            record.setEnterpriseName("物资分公司");
            record.setOrderSn(record.getOtherOrderSn());
            record.setOrderId(record.getOtherOrderId());
            if (record.getProductType()!=12){
                record.setRateAmount(record.getOtherRateAmount());
                record.setNoRateAmount(record.getOtherNoRateAmount());
            }
        }
        return PageR.success(new PageUtils(iPage));
    }


    @GetMapping("/get/orderShipInfo")
    @ApiOperation(value = "查询发货单详情(普通发货单)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "", value = "billSn", required = true, dataType = "String", paramType = "query"),
    })
    public R<OrderShip> findOrderShipByNo(String billSn) {
        if (org.apache.commons.lang.StringUtils.isEmpty(billSn)) {
            throw new BusinessException("参数错误！");
        }
        OrderShip orderShip = orderShipService.lambdaQuery().eq(OrderShip::getBillSn, billSn)
                .eq(OrderShip::getOrderClass,1).one();
        if (orderShip != null) {
            List<OrderShipDtl> list = orderShipDtlService.lambdaQuery().eq(OrderShipDtl::getBillId, orderShip.getBillId()).list();
            orderShip.setDtls(list);
        } else {
            throw new BusinessException("发货单不存在");
        }

        return R.success(orderShip);
    }

    @GetMapping("/get/twoOrderShipInfo")
    @ApiOperation(value = "查询发货单详情(二级供应商发货单)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "发货单号", value = "billSn", required = true, dataType = "String", paramType = "query"),
    })
    public R<OrderShip> findTwoOrderShipByNo(String billSn) {
        if (org.apache.commons.lang.StringUtils.isEmpty(billSn)) {
            throw new BusinessException("参数错误！");
        }
        OrderShip orderShip = orderShipService.lambdaQuery().eq(OrderShip::getBillSn, billSn)
                .eq(OrderShip::getOrderClass,3).one();
        if (orderShip != null) {
            orderShip.setOrderSn(orderShip.getOtherOrderSn());
            orderShip.setEnterpriseName("物资分公司");
            orderShip.setOrderSn(orderShip.getOtherOrderSn());
            orderShip.setOrderId(orderShip.getOtherOrderId());
            if (orderShip.getProductType()!=12){
                orderShip.setRateAmount(orderShip.getOtherRateAmount());
                orderShip.setNoRateAmount(orderShip.getOtherNoRateAmount());
            }
            List<OrderShipDtl> list = orderShipDtlService.lambdaQuery().eq(OrderShipDtl::getBillId, orderShip.getBillId()).list();
            for (OrderShipDtl orderShipDtl : list) {
                orderShipDtl.setOrderSn(orderShip.getOtherOrderSn());
            }
            orderShip.setDtls(list);
        } else {
            throw new BusinessException("发货单不存在");
        }

        return R.success(orderShip);
    }


    @PostMapping("/save/createShip")
    @ApiOperation(value = "根据发货单项新增发货单(普通订单)")
    public R createShip(HttpServletRequest request ,@RequestBody OrderShipVo orderShipVo) {
        Orders orders = ordersService.getDataByOrderSn(orderShipVo.getOrderSn());
        if (orders == null) {
            throw new BusinessException("订单编号错误，请输入正确的订单编号");
        } else {
            orderShipVo.setOrderId(orders.getOrderId());
            if (orders.getParentOrderId() == null) {
                String userId = request.getHeader("userId");
                User user = userService.lambdaQuery().eq(User::getUserId, userId)
                        .one();

                orderShipService.OutSaveOrderShip(orderShipVo,user);
            } else {
                throw new BusinessException("此订单编号是二级订单编号，请输入普通订单");
            }
        }

        return R.success();
    }

    @PostMapping("/save/createTwoShip")
    @ApiOperation(value = "根据发货单项新增发货单(二级订单)")
    @Transactional
    @NotResubmit
    public R createTwoShip(HttpServletRequest request ,@RequestBody OrderShipVo orderShipVo) {
        Orders orders = ordersService.lambdaQuery().eq(Orders::getOrderSn,orderShipVo.getOrderSn()).one();
        if (orders == null) {
            throw new BusinessException("订单编号错误，请输入正确的订单编号");
        } else {
            if (orders.getParentOrderId() == null) {
                throw new BusinessException("此订单编号是主订单变化，请输入二级订单比那好");
            } else {
                String userId = request.getHeader("userId");
                User user = userService.lambdaQuery().eq(User::getUserId, userId)
                        .one();
                orderShipVo.setOrderId(orders.getOrderId());
                orderShipService.OutSaveOrderShip(orderShipVo,user);
            }
        }
        return R.success();
    }


    @PostMapping("/update/orderShip")
    @ApiOperation(value = "修改零星采购发货单")
    @Transactional
    @NotResubmit
    public R UpdateOrderInfo(@RequestBody OrderShip orderShip) {
        orderShipService.updateDtls(orderShip);
        return R.success();
    }

    @PostMapping("/updateAll/MonOrderShip")
    @ApiOperation(value = "修改大宗月供发货单")
    @Transactional
    @NotResubmit
    public R UpdateMonOrderShip(@RequestBody OrderShip dtls) {
        orderShipService.updateMonOrderShip(dtls);

        return R.success();
    }

    @PostMapping("/updateAll/dzOrderShip")
    @ApiOperation(value = "修改大宗临购发货单")
    @Transactional
    @NotResubmit
    public R UpdateDzOrderShip(@RequestBody OrderShip dtls) {
        orderShipService.UpdateDzOrderShip(dtls);

        return R.success();
    }

    @PostMapping ("/ship/orderShipByBillNo")
    @ApiOperation(value = "供应商发货(对外接口)")
    @Transactional
    @NotResubmit
    @ApiImplicitParams({
            @ApiImplicitParam(name = "发货单Id", value = "billId", required = true,
                    dataType = "String", paramType = "query")
    })
    public R shippingOrderByBillId(HttpServletRequest request ,@RequestBody OrderShip orderShip ) {
        String userId = request.getHeader("userId");
        User user = userService.lambdaQuery().eq(User::getUserId, userId)
                .select(User::getUserId, User::getEnterpriseId).one();
        String deliveryFlowId = orderShip.getDeliveryFlowId();
        if (deliveryFlowId == null || deliveryFlowId == "") {
            throw new BusinessException(500, "该用户没有填写发货单号");
        }
        String company = orderShip.getLogisticsCompany();
        if (company == null || company == "") {
            throw new BusinessException(1002, "该用户没有填写物流公司");
        }

        OrderShip one = orderShipService.getById(orderShip.getBillId());
        if (!one.getShipEnterpriseId().equals(user.getEnterpriseId())) {
            throw new BusinessException(500, "该用户没有发货权限");
        }
        orderShipService.shippingOrderShip(orderShip,user);

        return R.success();
    }


    @GetMapping ("/list/update/delOrderShipByBillId")
    @ApiOperation(value = "删除零星采购发货单")
    @Transactional
    @NotResubmit
    @ApiImplicitParams({
            @ApiImplicitParam(name = "发货单Id", value = "billId", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delOrderShipByBillId(String billId) {
        OrderShip one = orderShipService.lambdaQuery().eq(OrderShip::getBillId, billId)
                .eq(OrderShip::getProductType, 10).one();
        if (one == null) {
            throw new BusinessException("零星采购发货单不存在");
        } else {
            if (one.getType() == 2) {
                throw new BusinessException("零星采购发货单已发货，不能删除");
            } else {
                orderShipService.delLiXinBillId(one);
            }

        }


        return R.success();
    }

    @GetMapping("/delMonthOrderShipByBillSn")
    @ApiOperation(value = "删除大宗月供发货单")
    @Transactional
    @NotResubmit
    @ApiImplicitParams({
            @ApiImplicitParam(name = "发货单Id", value = "billId", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delMonthOrderShipByBillSn( String billId) {

        OrderShip one = orderShipService.lambdaQuery().eq(OrderShip::getBillId, billId)
                .eq(OrderShip::getProductType, 12).one();
        if (one == null) {
            throw new BusinessException("大宗月供发货单不存在");
        } else {
            if (one.getType() == 2) {
                throw new BusinessException("大宗月供发货单已发货，不能删除");
            } else {
                orderShipService.delMonthOrderShipByBillSn(one);
            }

        }

        return R.success();
    }

    @GetMapping("/delDzOrderShipByBillSn")
    @ApiOperation(value = "删除大宗临购发货单(外部接口)")
    @Transactional
    @NotResubmit
    @ApiImplicitParams({
            @ApiImplicitParam(name = "发货单Id", value = "billId", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delDzOrderShipByBillSn(String billId) {

        OrderShip one = orderShipService.lambdaQuery().eq(OrderShip::getBillId, billId)
                .eq(OrderShip::getProductType, 13).one();
        if (one == null) {
            throw new BusinessException("发货单不存在");
        } else {
            if (one.getType() == 2) {
                throw new BusinessException("发货单已发货，不能删除");
            } else {
                orderShipService.delDzOrderShipByBillSn(one);
            }

        }
        return R.success();
    }


}

