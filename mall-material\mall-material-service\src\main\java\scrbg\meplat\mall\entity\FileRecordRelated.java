package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(value = "文件上传记录信息")
@Data
@TableName("file_record_related")
public class FileRecordRelated implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private String id;

    private String recordId;

    private String relatedId;

    @ApiModelProperty(value = "对象名称")

    private String objectName;


    @ApiModelProperty(value = "对象路径")

    private String objectPath;


    @ApiModelProperty(value = "不含IP/域名的对象路径")

    private String nonIpObjectPath;


    @ApiModelProperty(value = "桶名称")

    private String bucketName;


    @ApiModelProperty(value = "对象大小kb")

    private BigDecimal objectSizeKb;


    @ApiModelProperty(value = "对象大小mb")

    private BigDecimal objectSizeMb;

    private String userName;

    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;

}
