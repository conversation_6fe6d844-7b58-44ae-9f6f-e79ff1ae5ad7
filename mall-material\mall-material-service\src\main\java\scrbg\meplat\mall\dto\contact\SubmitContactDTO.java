package scrbg.meplat.mall.dto.contact;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2023-04-21 9:26
 */
@Data
public class SubmitContactDTO {


    @ApiModelProperty(value = "合同id")
    private String billId;
    @ApiModelProperty(value = "合同设备明细id")
    private String dtlId;
    @ApiModelProperty(value = "设备id")
    private String itemId;

    @ApiModelProperty(value = "规格型号")
    private String size;

    @ApiModelProperty(value = "设备名称")
    private String itemName;

    @ApiModelProperty(value = "已下单数量")
    private BigDecimal orderQty;

    @ApiModelProperty(value = "金额")
    private BigDecimal price;

    @ApiModelProperty(value = "总数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "选择数量")
    private BigDecimal count;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "设备的总金额")

    private BigDecimal amount;



    @ApiModelProperty(value = "合同信息")

    private SubmitContactInfoDTO contact;



    @ApiModelProperty(value = "订单商品")

    private SubmitContactOrderDTO order;







}
