package scrbg.meplat.mall.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import scrbg.meplat.mall.dto.mail.StationMessageReceiveDTO;
import scrbg.meplat.mall.entity.StationMessageReceive;
import scrbg.meplat.mall.vo.message.RemindMessageVo;
import scrbg.meplat.mall.vo.product.StationMessageReceiveVO;
import scrbg.meplat.mall.vo.product.material.StationMessageReceiveVo;

import java.util.List;

/**
 * @描述：站点接收消息 Mapper 接口
 * @作者: y
 * @日期: 2022-11-24
 */
@Mapper
@Repository
public interface StationMessageReceiveMapper extends BaseMapper<StationMessageReceive> {

    /**
     * 根据消息id等参数查询消息发送列表
     * @return
     */
    List<StationMessageReceiveVO> listStationMessageById(@Param("dto")StationMessageReceiveDTO dto);

    List<StationMessageReceiveVo> findByCondition(IPage<StationMessageReceiveVo> pages,  @Param("ew") QueryWrapper<StationMessageReceiveVo> wrapper);
    /**
     * 系统给平台发送消息
     *
     */
    @Insert("INSERT INTO station_message (title,is_delete,mall_type,station_message_id,send_id,send_name,send_date,content,all_read) values(#{title},'0','0',#{stationMessageId}, #{id}, #{sendName},#{sendDate},#{content},#{allRead})" )
    void insertMessage(@Param("stationMessageId") String stationMessageId,@Param("id")String id,@Param("sendName")String sendName,@Param("title")String title,@Param("content")String content,@Param("allRead")int allRead,@Param("sendDate")String sendDate);

    @Insert("INSERT INTO station_message_receive (station_message_receive_id,receive_id,is_read,is_delete,receive_code,station_message_id) values(#{stationMessageReceiveId},#{receiveId},'0','0',#{receiveCode},#{stationMessageId})" )
    void insertReceiveMessage(@Param("stationMessageReceiveId") String stationMessageReceiveId,@Param("receiveId")String receiveId,@Param("receiveCode")String receiveCode,@Param("stationMessageId")String stationMessageId);


    /**
     * 提醒消息
     *
     */
    List<RemindMessageVo> getRemindMessage(@Param("userId") String userId, @Param("shopId") String shopId);
    Integer getRemindMessageCount(@Param("userId") String userId, @Param("shopId") String shopId);
}
