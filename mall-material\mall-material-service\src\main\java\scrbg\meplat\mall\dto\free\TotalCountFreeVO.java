package scrbg.meplat.mall.dto.free;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @create 2024-04-11 11:40
 */
@Data
public class TotalCountFreeVO {
    @ApiModelProperty(value = "企业名称")
    private String enterpriseName;
    @ApiModelProperty(value = "信用额度")
    private BigDecimal arrearage;

    @ApiModelProperty(value = "是否开通店铺年服务")
    private Integer isOpenShop;

    @ApiModelProperty(value = "是否开通招标年服务")
    private Integer isOpenBid;

    @ApiModelProperty(value = "店铺年费截止日期")
    private LocalDate shopEndTime;

    @ApiModelProperty(value = "电子招标年费截止日期")
    private LocalDate bidEndTime;



    @ApiModelProperty(value = "店铺交易欠费金额")
    private BigDecimal shopPayFree;

    @ApiModelProperty(value = "合同履约交易欠费金额")
    private BigDecimal contractPayFree;


    @ApiModelProperty(value = "店铺是否超过信用额度")
    private Integer isShopExceedArrearage;

    @ApiModelProperty(value = "合同履约是否超过信用额度")
    private Integer isBidExceedArrearage;


    @ApiModelProperty(value = "总欠费金额")
    private BigDecimal totalPayFree;



    @ApiModelProperty(value = "是否开通店铺年服务")
    private String isOpenShopStr;

    @ApiModelProperty(value = "是否开通招标年服务")
    private String isOpenBidStr;

    @ApiModelProperty(value = "店铺年费截止日期")
    private String shopEndTimeStr;

    @ApiModelProperty(value = "电子招标年费截止日期")
    private String bidEndTimeStr;

    @ApiModelProperty(value = "是否欠费")
    private String isPayFreeStr;


    @ApiModelProperty(value = "店铺是否超过信用额度")
    private String isShopExceedArrearageStr;

    @ApiModelProperty(value = "合同履约是否超过信用额度")
    private String isBidExceedArrearageStr;

    public String getIsShopExceedArrearageStr() {
        if(isShopExceedArrearage == 1) {
            return "是";
        }else {
            return "否";
        }
    }

    public String getIsBidExceedArrearageStr() {
        if(isBidExceedArrearage == 1) {
            return "是";
        }else {
            return "否";
        }
    }

    public String getIsOpenShopStr() {
        return getIsOpenShop() == 1?"是":"否";
    }

    public String getIsOpenBidStr() {
        return getIsOpenBid() == 1?"是":"否";
    }

    public String getShopEndTimeStr() {
        return shopEndTime == null?"无开通记录":shopEndTime.toString();
    }

    public String getBidEndTimeStr() {
        return bidEndTime == null?"无开通记录":bidEndTime.toString();
    }

    public String getIsPayFreeStr() {
        int i = getTotalPayFree().compareTo(BigDecimal.ZERO);
        if(i == 1) {
            return "是";
        }else {
            return "否";
        }
    }

    public Integer getIsOpenShop() {
        LocalDate now = LocalDate.now();
        if(shopEndTime == null || now.isAfter(shopEndTime) || now.equals(shopEndTime)) {
            return 0;
        }else {
            return 1;
        }
    }

    public Integer getIsOpenBid() {
        LocalDate now = LocalDate.now();
        if(bidEndTime == null || now.isAfter(bidEndTime) || now.equals(bidEndTime)) {
            return 0;
        }else {
            return 1;
        }
    }

    public BigDecimal getTotalPayFree() {
        return contractPayFree.add(shopPayFree);
    }


}
