package scrbg.meplat.mall.enums.product;

/**
 * @package: scrbg.meplat.mall.enums.product
 * @author: 胡原武
 * @date: 2022.11.29
 */
public enum CollectEnum {

    COLLECT_TYPE_SHOP(1,"店铺"),
    COLLECT_TYPE_PRODUCT(2,"商品");


    /**
     * 编码
     */
    private int code;

    /**
     * 说明
     */
    private String remark;

    CollectEnum() {
    }

    CollectEnum(int code, String remark) {
        this.code = code;
        this.remark = remark;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
