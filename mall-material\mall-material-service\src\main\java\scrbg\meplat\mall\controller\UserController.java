package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.config.auth.IsRole;
import scrbg.meplat.mall.config.auth.RoleEnum;
import scrbg.meplat.mall.entity.User;
import scrbg.meplat.mall.service.ShopService;
import scrbg.meplat.mall.service.UserService;
import scrbg.meplat.mall.vo.user.UserShopEnterpriseInfoVO;

import java.util.List;
import java.util.Map;

/**
 * @描述：用户控制类
 * @作者: y
 * @日期: 2022-11-02
 */
@RestController
@RequestMapping("/platform/user")
@ApiSort(value = 500)
@Api(tags = "用户接口")
public class UserController {

    @Autowired
    public UserService userService;

    @Autowired
    ShopService shopService;

    @PostMapping("/listByParameters")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "account", value = "账号", dataTypeClass = String.class),
            @DynamicParameter(name = "userType", value = "用户类型（1 内部，2 外部）", dataTypeClass = Integer.class),
            @DynamicParameter(name = "nickName", value = "昵称", dataTypeClass = String.class),
            @DynamicParameter(name = "userMobile", value = "手机号", dataTypeClass = String.class),
            @DynamicParameter(name = "email", value = "邮箱", dataTypeClass = String.class),
            @DynamicParameter(name = "enterpriseId", value = "企业id", dataTypeClass = String.class),
            @DynamicParameter(name = "state", value = "账号状态", dataTypeClass = Integer.class),
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = userService.queryPage(jsonObject, new LambdaQueryWrapper<>());
        return PageR.success(page);
    }

    @PostMapping("/getUserCountList")
    @ApiOperation(value = "查询用户分页统计列表")
    @IsRole(roleName = RoleEnum.ROLE_11)
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "startCreateDate", value = "创建开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endCreateDate", value = "创建结束时间", dataTypeClass = String.class),
    })
    public PageR getUserCountList(@RequestBody JSONObject jsonObject) {
        PageUtils page = userService.getUserCountList(jsonObject,  Wrappers.lambdaQuery(User.class));
        return PageR.success(page);
    }

    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<User> findById(String userId) {
        User user = userService.getById(userId);
        return R.success(user);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody User user) {
        userService.create(user);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody User user) {
        userService.update(user);
        return R.success();
    }

    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        userService.delete(id);
        return R.success();
    }







    /**
     * 批量禁用账号
     *
     * @return
     */
    @PostMapping("/updateNotPublish")
    @ApiOperation(value = "批量禁用账号")
    public R updateNotPublish(@RequestBody JSONObject jsonObject) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        List<String> ids = (List<String>) innerMap.get("list");
        Integer mallType = (Integer) innerMap.get("mallType");
        userService.updateByPublish(ids, "0");
        return R.success();
    }
    @PostMapping("/changOpenUserState")
    @ApiOperation(value = "批量启用账号")
    public R changOpenUserState(@RequestBody JSONObject jsonObject) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        List<String> ids = (List<String>) innerMap.get("list");
        Integer mallType = (Integer) innerMap.get("mallType");
        userService.updateByPublish(ids, "1");
        return R.success();
    }

    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")

    public R deleteBatch(@RequestBody List<String> ids) {
        userService.removeByIds(ids);
        return R.success();
    }


    /**
     * 验证前端用户相关信息是否正确
     */
    @PostMapping("/verifyUserInfo")
    @ApiOperation(value = "个人用户信息验证")
    private Boolean verifyUserInfo() {
        return userService.verifyUserInfo();
    }

    @GetMapping("/getUserShopEnterpriseInfo")
    @ApiOperation(value = "获取用户的信息企业信息店铺信息")
    public R getUserShopEnterpriseInfo() {
        UserShopEnterpriseInfoVO vo = userService.getUserShopEnterpriseInfo();
        return R.success(vo);
    }

    @GetMapping("/getUserShopEnterpriseInfoByUserSn")
    @ApiOperation(value = "根据账号获取用户的信息企业信息店铺信息")
    public R getUserShopEnterpriseInfoByUserSn(String userNumber, Integer mallType) {
        UserShopEnterpriseInfoVO vo = userService.getUserShopEnterpriseInfoByUserSn(userNumber, mallType);
        return R.success(vo);
    }
}
