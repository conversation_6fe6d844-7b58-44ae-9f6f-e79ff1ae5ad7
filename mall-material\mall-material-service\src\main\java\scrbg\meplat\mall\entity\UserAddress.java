package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;
/**
 * @描述：用户地址
 * @作者: y
 * @日期: 2022-11-28
 */
@ApiModel(value="用户地址")
@Data
@TableName("user_address")
public class UserAddress extends MustBaseEntity implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "地址id")
    private String addressId;

    @ApiModelProperty(value = "用户id")
    private String userId;

    @ApiModelProperty(value = "地址别名")
    private String aliasAddress;

    @ApiModelProperty(value = "收件人姓名")
    private String receiverName;

    @ApiModelProperty(value = "收件人手机号")
    private String receiverMobile;

    @ApiModelProperty(value = "固定电话")
    private String fixedTelephone;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "省份")
    private String province;

    @ApiModelProperty(value = "城市")
    private String city;

    @ApiModelProperty(value = "区县")
    private String county;

    @ApiModelProperty(value = "详细地址")
    private String detailAddress;

    @ApiModelProperty(value = "邮编")
    private String postCode;

    @ApiModelProperty(value = "企业id")
    private String enterpriseId;

    @ApiModelProperty(value = "状态,1正常，0无效")
    private Integer state;

    @ApiModelProperty(value = "是否默认地址 1是 1:是  0:否")
    private Integer isDefaultAddress;

}
