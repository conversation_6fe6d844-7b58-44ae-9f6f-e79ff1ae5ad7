package scrbg.meplat.mall.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import scrbg.meplat.mall.entity.MessageInformations;

import java.util.List;

/**
 * @描述：反馈中心 Mapper 接口
 * @作者: y
 * @日期: 2022-11-22
 */
@Mapper
@Repository
public interface MessageInformationsMapper extends BaseMapper<MessageInformations> {
    List<MessageInformations> listByDeviceDemand(IPage<MessageInformations> pages, @Param("ew") QueryWrapper<MessageInformations> q);

}
