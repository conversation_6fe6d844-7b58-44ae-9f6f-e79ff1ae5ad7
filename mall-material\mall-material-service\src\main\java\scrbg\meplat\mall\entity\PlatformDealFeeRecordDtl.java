package scrbg.meplat.mall.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
/**
 * @描述：平台交易费缴费记录明细
 * @作者: ye
 * @日期: 2024-01-24
 */
@ApiModel(value="平台交易费缴费记录明细")
@Data
@TableName("platform_deal_fee_record_dtl")
public class PlatformDealFeeRecordDtl extends MustBaseEntity implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "缴费记录明细id")
    private String dealFeeRecordDtlId;

    @ApiModelProperty(value = "缴费记录id")

    private String dealFeeRecordId;

    @ApiModelProperty(value = "平台交易明细id")

    private String platformDealFeeDtlId;

    @ApiModelProperty(value = "缴费金额")

    private BigDecimal payAmount;

    @ApiModelProperty(value = "乐观锁")
    @Version
    private Integer version;

}