package scrbg.meplat.mall.controller.website.userCenter;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.entity.UserAddress;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.service.UserAddressService;
import scrbg.meplat.mall.util.ThreadLocalUtil;

/**
 * @描述：用户地址控制类
 * @作者: y
 * @日期: 2022-11-02
 */
@RestController
@RequestMapping("/userCenter/address")
@ApiSort(value = 200)
@Api(tags = "用户收货地址(个人中心)")
public class UserCenterAddressController {

    @Autowired
    public UserAddressService userAddressService;

    /**
     * 根据用户Id查询地址并分页
     *
     * @param jsonObject
     * @return
     */
    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据用户id查询用户收货地址并分页")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<UserAddress> listByEntity(@RequestBody JSONObject jsonObject) {
        //获取用户UserId
        String userId = ThreadLocalUtil.getCurrentUser().getUserId();
        PageUtils page = userAddressService.queryPage(jsonObject, new LambdaQueryWrapper<>(), userId);
        return PageR.success(page);
    }

    /**
     * 根据userId新增地址
     *
     * @param userAddress
     * @return
     */
    @PostMapping("/createByUserId")
    @ApiOperation(value = "新增地址有id则修改")
    public R createByUserId(@RequestBody UserAddress userAddress) {
        if(userAddress.getAddressId() != null){
            userAddressService.update(userAddress);
            return R.success();
        }else {
            String userId = ThreadLocalUtil.getCurrentUser().getUserId();
            String enterpriseId = ThreadLocalUtil.getCurrentUser().getEnterpriseId();
            userAddress.setUserId(userId);
            userAddress.setEnterpriseId(enterpriseId);
            R r = userAddressService.create(userAddress, new LambdaQueryWrapper<>());
            return r;
        }
    }

    /**
     * 根据addressId更新地址信息
     *
     * @param userAddress
     * @return
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody UserAddress userAddress) {
        userAddressService.update(userAddress);
        return R.success();
    }

    /**
     * 根据addressId删除地址
     *
     * @param id
     * @return
     */
    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        userAddressService.delete(id);
        return R.success();
    }
    @GetMapping("/isUserAdrees")
    @ApiOperation(value = "判断用户是否设置收货地址")
    public R isUserAdrees() {
        String enterpriseId = ThreadLocalUtil.getCurrentUser().getEnterpriseId();
        Integer count = userAddressService.lambdaQuery().eq(UserAddress::getEnterpriseId, enterpriseId).count();
        if (count==0){
            throw new BusinessException(500,"用户没有设置收货地址，请前往个人中心设置收货地址");
        }
        return R.success();
    }
    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<UserAddress> findById(String id) {
        UserAddress userAddress = userAddressService.getById(id);
        return R.success(userAddress);
    }
    /**
     * 根据addressId设置默认地址
     *
     * @param userAddress
     */
    @GetMapping("/setDefaultAddress")
    @ApiOperation(value = "根据addressId设置默认地址")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "addressId", value = "地址id", required = true,
                    dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "mallType", value = "商城类型", required = true,
                    dataType = "String", paramType = "query"),
    })
    public R setDefaultAddress(UserAddress userAddress) {
        String userId = ThreadLocalUtil.getCurrentUser().getUserId();
        userAddress.setUserId(userId);
        R r = userAddressService.setDefaultAddress(userAddress, new LambdaQueryWrapper<>());
        return r;
    }

    /**
     * 查询默认收货地址
     *
     * @param
     * @return
     */
    @GetMapping("/getDefaultAddress")
    @ApiOperation(value = "查询默认收货地址")
    public R getDefaultAddress() {
      UserAddress userAddress = userAddressService.getDefaultAddress();
        return R.success(userAddress);
    }

}
