package scrbg.meplat.mall.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
import scrbg.meplat.mall.vo.bidding.GetBidingRecordItemVO;
/**
 * 大宗临购清单和周转材料清单
 * @作者: ye
 * @日期: 2023-10-07
 */
@ApiModel(value="大宗临购清单和周转材料清单")
@Data
@TableName("synthesize_temporary")
public class SynthesizeTemporary extends MustBaseEntity implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "大宗临购单id")
    private String synthesizeTemporaryId;

    @ApiModelProperty(value = "大宗临购单编号")

    private String synthesizeTemporarySn;

    /**
     * 原表只是大宗临购清单，现在同时也存储周转材料清单，用这个类型区分（默认是大宗临购清单）
     */
    @ApiModelProperty(value = "清单类型 0 大宗临购 1 周转材料 默认值0")
    @TableField("stType")
    private Integer stType;


    @ApiModelProperty(value = "采购单位内部id")

    private String orgFarId;


    @ApiModelProperty(value = "采购单位id")

    private String orgId;


    @ApiModelProperty(value = "采购单位名称")

    private String orgName;


    @ApiModelProperty(value = "供应商内部机构id")

    private String supplierOrgFarId;


    @ApiModelProperty(value = "供应商机构id")

    private String supplierOrgId;


    @ApiModelProperty(value = "供应商机构名称")

    private String supplierOrgName;


    @ApiModelProperty(value = "供应商信用代码")

    private String supplierCreditCode;


    @ApiModelProperty(value = "供应商机构简码")

    private String supplierShortCode;

    @ApiModelProperty(value = "省")

    private String province;


    @ApiModelProperty(value = "市")

    private String city;


    @ApiModelProperty(value = "县、区")

    private String county;

    @ApiModelProperty(value = "项目收货地址")

    private String receiverAddress;

    @ApiModelProperty(value = "清单类型（1浮动价格2固定价格）")

    private Integer billType;

    @ApiModelProperty(value = "参考总金额（不会变化）")

    private BigDecimal referenceSumAmount;


    @ApiModelProperty(value = "综合总金额（会变化，网价+固定费+管理费）")

    private BigDecimal synthesizeSumAmount;


    @ApiModelProperty(value = "提交时间")

    private Date submitTime;


    @ApiModelProperty(value = "审核时间（供应商确认时间）")

    private Date auditTime;


//    @ApiModelProperty(value = "状态（0草稿1已提交2供应商已确认3已推送大宗临购计划）")
    @ApiModelProperty(value = "状态（0草稿1已提交11供应商已拒绝2供应商已确认待审核3审核通过4审核不通过5收货方拒绝6已推送大宗临购计划）")

    private Integer state;
    @ApiModelProperty(value = "是否已生成竞价（0否1是）")

    private Integer bidStatus;

    @ApiModelProperty(value = "采购单位是否删除（0否1是）这个删除是指供应商已确认后删除操作，采购单位不可见")

    private Integer orgIsDelete;


    @ApiModelProperty(value = "货款支付周期（单位月）")

    private Integer paymentWeek;

    @ApiModelProperty(value = "超期垫资利息（%）")

    private BigDecimal outPhaseInterest;

    @ApiModelProperty(value = "收货单位拒绝原因")

    private String refuseRes;


    @ApiModelProperty(value = "明细")
    @TableField(exist = false)
    private List<SynthesizeTemporaryDtl> dtls;


    @ApiModelProperty(value = "是否提交供应商（1是0否）")
    @TableField(exist = false)
    private Integer isSubmit;


    @ApiModelProperty(value = "审核历史")
    @TableField(exist = false)
    private List<AuditRecord> auditRecords;

    @ApiModelProperty(value = "附件")
    @TableField(exist = false)
    private List<StAttachment> attachments;

    @ApiModelProperty(value = "报价单")
    @TableField(exist = false)
    private List<GetBidingRecordItemVO> quotations;

}