package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import scrbg.meplat.mall.config.auth.IsRole;
import scrbg.meplat.mall.config.auth.RoleEnum;
import scrbg.meplat.mall.service.StatisticalNumService;
import scrbg.meplat.mall.entity.StatisticalNum;
import scrbg.meplat.mall.vo.platform.StatisticalNumVo;

import java.util.List;

/**
 * @描述：物资数据统计控制类
 * @作者: ye
 * @日期: 2023-08-27
 */
@RestController
@RequestMapping("/statisticalNum")
@Api(tags = "物资数据统计")
public class StatisticalNumController {

    @Autowired
    public StatisticalNumService statisticalNumService;

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<StatisticalNum> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = statisticalNumService.queryPage(jsonObject, new LambdaQueryWrapper<StatisticalNum>());
        return PageR.success(page);
    }

    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<StatisticalNum> findById(String id) {
        StatisticalNum statisticalNum = statisticalNumService.getById(id);
        return R.success(statisticalNum);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody StatisticalNum statisticalNum) {
        statisticalNumService.create(statisticalNum);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody StatisticalNum statisticalNum) {
        statisticalNumService.update(statisticalNum);
        return R.success();
    }

    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        statisticalNumService.delete(id);
        return R.success();
    }


    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")
    public R deleteBatch(@RequestBody List<String> ids) {
        statisticalNumService.removeByIds(ids);
        return R.success();
    }
    @PostMapping("/staticsSystem")
    @IsRole(roleName = RoleEnum.ROLE_11)
    @ApiOperation(value = "系统运营状况统计")
    public R staticsSystem(@RequestBody JSONObject jsonObject){
        List<StatisticalNumVo> voList = null ;
        int type =(int) jsonObject.get("type");
        if (jsonObject.get("dateScope")!=null && ((List) jsonObject.get("dateScope")).size()>0){
            voList = statisticalNumService.staticsSystem(jsonObject,type);
        }else {
            voList = statisticalNumService.staticsSystem(type);
        }
        return R.success(voList);
    }

}

