package scrbg.meplat.mall.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import scrbg.meplat.mall.entity.ShoppingCart;
import scrbg.meplat.mall.vo.product.ShoppingCartProductInfoVO;
import scrbg.meplat.mall.vo.product.ShoppingCartShopIdVO;
import scrbg.meplat.mall.vo.product.ShoppingCartVO;
import scrbg.meplat.mall.vo.product.website.WCartInfoVO;

import java.util.List;

/**
 * @描述：购物车 Mapper 接口
 * @作者: y
 * @日期: 2022-11-02
 */
@Mapper
@Repository
public interface ShoppingCartMapper extends BaseMapper<ShoppingCart> {

    /**
     * 根据购物车ids获取商品信息
     *
     * @param cartIdList
     * @return
     */
    List<ShoppingCartVO> listShoppingCartInfoByIds(List<String> cartIdList);

    /**
     * 根据购物车id和商品类型查询商品id集合
     *
     * @param cartIds
     * @param productType
     * @param userId
     * @return
     */
    List<ShoppingCartProductInfoVO> listProductIdAndCartByCartIds(@Param("cartIds") List<String> cartIds, @Param("productType") Integer productType, @Param("userId") String userId);

    /**
     * 根据购物车id和商品类型查询商品id集合
     *
     * @param cartIds
     * @param userId
     * @return
     */
    List<ShoppingCartShopIdVO> listShopIdAndCartByCartIds(@Param("cartIds") List<String> cartIds, @Param("userId") String userId);

    /**
     * 根据商品类型获取购物车列表
     *
     * @param productType
     * @param userId
     * @return
     */
    List<WCartInfoVO> getMyCartList(@Param("productType") Integer productType, @Param("userId") String userId, @Param("mallType") Integer mallType);

    /**
     * 根据商品类型获取购物车列表数量
     *
     * @param productType
     * @param userId
     * @return
     */
    Integer getMyCartListCount(@Param("productType") Integer productType, @Param("userId") String userId, @Param("mallType") Integer mallType);

    /**
     * 主键集合批量真实删除
     *
     * @param ids
     */
    @Delete("<script>" +
            "delete from shopping_cart where cart_id in " +
            "<foreach collection='ids' open='(' item='id_' separator=',' close=')'> #{id_}" +
            "</foreach>" +
            "</script>")
    void removeRealByIds(List<String> ids);

    /**
     * 清空购物车
     *
     * @param userId
     */
    @Delete("delete from shopping_cart where user_id = #{userId} and mall_type = #{mallType}")
    void emptyCart(String userId, Integer mallType);

    /**
     * 根据店铺id删除所有购物车
     *
     * @param shopId
     */
    @Delete("delete from shopping_cart where shop_id = #{shopId} and mall_type = #{mallType}")
    void removeRealByShopId(String shopId, Integer mallType);

    /**
     * 根據商品id刪除购物车
     *
     * @param productIds
     */
    @Delete("<script>" +
            "delete from shopping_cart where product_id in " +
            "<foreach collection='productIds' open='(' item='id_' separator=',' close=')'> #{id_}" +
            "</foreach>" +
            "</script>")
    void removeRealByProductIds(List<String> productIds);
}
