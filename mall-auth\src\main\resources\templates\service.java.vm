package ${package.Service};

import ${package.Entity}.${entity};
import ${superServiceClassPackage};
import com.scrbg.common.utils.PageUtils;
import ${package.Entity}.${entity};
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.List;
/**
 * @描述：$!{table.comment} 服务类
 * @作者: ${author}
 * @日期: ${date}
 */
public interface ${table.serviceName} extends ${superServiceClass}<${entity}> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<${entity}> queryWrapper);

        void create(${entity} ${table.entityPath});
        void update(${entity} ${table.entityPath});
        ${entity} getById(String id);

        void delete(String id);

        void deleteBatch( List<String> ids);
}
