package scrbg.meplat.mall.config;

import io.swagger.models.auth.In;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MallConfig {
    //物资商场为material(0)， 设备商场为 device(1)
    @Value("${mall.type}")
    public Integer mallType;

    @Value("${mall.isProductSearch}")
    public Integer isProductSearch;

    @Value("${mall.sendExchange}")
    public String sendCodeExchange;

    @Value("${mall.loginOutTime}")
    public Integer loginOutTime;

    @Value("${mall.prodPcwp2Url}")
    public String prodPcwp2Url;

    @Value("${mall.prodPcwp2Url02}")
    public String prodPcwp2Url02;

    @Value("${mall.thirdApiToken}")
    public String thirdApiToken;

    @Value("${mall.isDPlatformAdminOrgId}")
    public String isDPlatformAdminOrgId;

    @Value("${mall.isMPlatformAdminOrgId}")
    public String isMPlatformAdminOrgId;

    @Value("${mall.pcwp1ContactUrl}")
    public String pcwp1ContactUrl;

    @Value("${mall.pcwp1PlanUrl}")
    public String pcwp1PlanUrl;

    @Value("${mall.profilesActive}")
    public String profilesActive;

    @Value("${mall.isProductRepetitionCheck}")
    public Integer isProductRepetitionCheck;
    @Value("${mall.isImportAutoFillBrandUnit}")
    public Integer isImportAutoFillBrandUnit;

    @Value("${mall.pcwpPurchase}")
    public String pcwpPurchase;

    @Value("${mall.templateFormUrl}")
    public String templateFormUrl;

    @Value("${mall.isApiImportProductBradDispose}")
    public Integer isApiImportProductBradDispose;

    @Value("${mall.selfOrgName}")
    public String selfOrgName;
    @Value("${mall.isShowCode}")
    public Integer isShowCode;


    @Value("${mall.isLoginAuthQuery}")
    public Integer isLoginAuthQuery;

    @Value("${mall.isCountPlanOrderNum}")
    public Integer isCountPlanOrderNum;

    @Value("${mall.isNotRateAmount}")
    public Integer isNotRateAmount;
    // oss文件前缀
    @Value("${mall.ossPrefix}")
    public String ossPrefix;


    @Value("${mall.isContractConsumeNum}")
    public Integer isContractConsumeNum;

    @Value("${mall.isBusinessOrg}")
    public Integer isBusinessOrg;


    @Value("${mall.businessShopId}")
    public String businessShopId;

    @Value("${mall.selectMaterialMonthSupplyPlan}")
    public Integer selectMaterialMonthSupplyPlan;


    @Value("${mall.changAmountAndTaxPlanAmount}")
    public Integer changAmountAndTaxPlanAmount;


    @Value("${mall.isPlatformFee}")
    public Integer isPlatformFee;



    // 是否使用pcwp登陆
    @Value("${mall.isPCWPLogin}")
    public Integer isPCWPLogin;
}