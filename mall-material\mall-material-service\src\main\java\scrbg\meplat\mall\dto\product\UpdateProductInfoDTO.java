package scrbg.meplat.mall.dto.product;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-11-03 16:48
 */
@Data
public class UpdateProductInfoDTO {

    @ApiModelProperty(value = "商品id")
    private String productId;

    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "商品描述")
    private String productDescribe;

    @ApiModelProperty(value = "分类ID")
    private String classId;

    @ApiModelProperty(value = "商品的最低价")

    private BigDecimal productMinPrice;

    @ApiModelProperty(value = "媒体")
    private List<ProductFileSaveOrUpdateDTO> mediumList;


    @ApiModelProperty(value = "规格")
    private String spec;

    @ApiModelProperty(value = "规格图片")
    private String specImage;

    @ApiModelProperty(value = "原价")
    private BigDecimal originalPrice;

    @ApiModelProperty(value = "销售价格")
    private BigDecimal sellPrice;

    @ApiModelProperty(value = "库存")
    private BigDecimal stock;

    @ApiModelProperty(value = "计量单位")
    private String unit;

    @ApiModelProperty(value = "成本价")
    private BigDecimal costPrice;
}
