package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;

/**
 * @描述：商品属性
 * @作者: y
 * @日期: 2022-11-13
 */
@ApiModel(value = "商品属性")
@Data
@TableName("product_attribute")
public class ProductAttribute extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "属性id")

    private String attributeId;

    @ApiModelProperty(value = "属性名称")

    private String attributeName;

    @ApiModelProperty(value = "可选值列表[用逗号分隔]")

    private String attributeValue;

    @ApiModelProperty(value = "属性类型[0-销售属性，1-基本属性]")

    private Integer attributeType;

    @ApiModelProperty(value = "启用状态[0 - 停用，1 - 启用]")

    private Integer state;

    @ApiModelProperty(value = "父级id")

    private String parentId;

    @ApiModelProperty(value = "分类id")

    private String classId;

    @ApiModelProperty(value = "商品类型：0物资 1设备 2周材（设备租赁） 3周材（设备） 4二手设备 5租赁设备")
    private Integer productType;
}
