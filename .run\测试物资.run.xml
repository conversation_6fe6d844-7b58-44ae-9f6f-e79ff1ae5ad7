<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="测试物资" type="docker-deploy" factoryName="dockerfile" server-name="测试环境docker">
    <deployment type="dockerfile">
      <settings>
        <option name="imageTag" value="mall-api" />
        <option name="containerName" value="mmall-api" />
        <option name="portBindings">
          <list>
            <DockerPortBindingImpl>
              <option name="containerPort" value="9010" />
              <option name="hostPort" value="9010" />
            </DockerPortBindingImpl>
          </list>
        </option>
        <option name="commandLineOptions" value="--restart=always -v /home/<USER>/logs:/logs  -v /templateForm:/templateForm" />
        <option name="sourceFilePath" value="mall-material/mall-material-service/dockerfile" />
      </settings>
    </deployment>
    <method v="2">
      <option name="Maven.BeforeRunTask" enabled="true" file="$PROJECT_DIR$/mall-material/mall-material-service/pom.xml" goal="clean package -U -DskipTests" />
    </method>
  </configuration>
</component>