package scrbg.meplat.mall.dto.product.material.lcProduct;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
@Data
public class ProductCardZone {
    @ApiModelProperty(value = "商品id")

    private String productId;
    @ApiModelProperty(value = "购物车商品数量")

    private BigDecimal cartNum;
    @ApiModelProperty(value = "区域销售价格")

    private BigDecimal zonePrice;
    @ApiModelProperty(value = "区域地址")

    private String detailAddress;
    @ApiModelProperty(value = "姓名")

    private String receiverName;
    @ApiModelProperty(value = "手机号")

    private String receiverMobile;
    @ApiModelProperty(value = "区域地址Id")

    private String zoneId;
}
