package scrbg.meplat.mall.dto.bidding;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023-07-21 14:56
 */
@Data
public class AuditBidingInfoDTO {

    @ApiModelProperty(value = "竞价id")
    private String biddingId;

    @ApiModelProperty(value = "是否通过（1是0否）")
    private Integer isOpen;

    @ApiModelProperty(value = "未通过原因")
    private String auditResult;

    @ApiModelProperty(value = "竞价记录id(竞价记录中标使用)")
    private String bidRecordId;

}
