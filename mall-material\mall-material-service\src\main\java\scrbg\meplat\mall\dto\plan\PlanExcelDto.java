package scrbg.meplat.mall.dto.plan;


import java.math.BigDecimal;
import java.util.Date;

import com.alibaba.excel.annotation.ExcelProperty;

import lombok.Data;

/**
 * 采购计划导出Excel的数据模型。
 * 对应Plan实体中的主要字段。
 */
@Data
public class PlanExcelDto {

    @ExcelProperty("计划ID")
    private String billId;

    @ExcelProperty("计划编号")
    private String billNo;

    @ExcelProperty("PCWP计划ID")
    private String pBillId;

    @ExcelProperty("PCWP计划编号")
    private String pBillNo;

    @ExcelProperty("计划状态") // 1待审核2已审核3已完成
    private String state;

    @ExcelProperty("计划日期")
    private Date billDate;

    @ExcelProperty("计划类型") // 0零星采购 1大宗临购 2周转材料
    private Integer type;

    @ExcelProperty("机构ID")
    private String orgId;

    @ExcelProperty("机构名称")
    private String orgName;

    @ExcelProperty("机构简称")
    private String orgShort;

    @ExcelProperty("计划金额(不含税)")
    private BigDecimal planAmount;

    @ExcelProperty("计划金额（含税）")
    private BigDecimal taxPlanAmount;

    @ExcelProperty("税额")
    private BigDecimal taxAmount;

    @ExcelProperty("备注")
    private String remark;
}
