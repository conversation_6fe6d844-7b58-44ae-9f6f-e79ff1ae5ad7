package scrbg.meplat.mall.dto.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023-12-21 9:18
 */
@Data
public class OrgAndSon {

    @ApiModelProperty(value = "机构Id")
    private String orgId;

    @ApiModelProperty(value = "机构名称")
    private String orgName;

    @ApiModelProperty(value = "机构简码")
    private String shortCode;

    public OrgAndSon() {
    }

    public OrgAndSon(String orgId, String orgName, String shortCode) {
        this.orgId = orgId;
        this.orgName = orgName;
        this.shortCode = shortCode;
    }
}
