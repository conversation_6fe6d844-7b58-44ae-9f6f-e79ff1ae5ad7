package scrbg.meplat.mall.dto.thirdapi;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @create 2023-07-11 17:06
 */
@Data
public class CreateProductCategoryDTO {

    @ApiModelProperty(value = "分类id",required = true)
    @NotEmpty
    private String classId;

    @ApiModelProperty(value = "分类编号",required = true)
    @NotEmpty
    private String classNo;

    @ApiModelProperty(value = "分类名称",required = true)
    @NotEmpty
    private String className;

    @ApiModelProperty(value = "新增or修改（1新增2修改）",required = true)
    @NotNull
    @Max(value = 2, message = "输入错误！")
    @Min(value = 1, message = "输入错误！")
    private Integer saveOrUpdate;

    @ApiModelProperty(value = "秘钥key",required = true)
    @NotEmpty
    private String keyId;

    @ApiModelProperty(value = "父层级id",required = false)

    private String parentId;

    @ApiModelProperty(value = "状态（1启用 0停用）默认0",required = false)
    @Max(value = 1, message = "输入错误！")
    @Min(value = 0, message = "输入错误！")
    private Integer state;







    @ApiModelProperty(value = "请勿略",hidden = true)
    private Integer productType = 0;

    @ApiModelProperty(value = "请勿略",hidden = true)
    private Integer mallType = 0;
}
