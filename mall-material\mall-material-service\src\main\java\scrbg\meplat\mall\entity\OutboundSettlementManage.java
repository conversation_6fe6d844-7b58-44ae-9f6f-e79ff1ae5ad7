package scrbg.meplat.mall.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel(value = "出库结算管理")
@Data
@TableName("outbound_settlement")
public class OutboundSettlementManage {

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    @ApiModelProperty(value = "对账单id")
    private String reconciliationId;

    @ApiModelProperty(value = "业务类型")
    private Integer supplierType;

    @ApiModelProperty(value = "出库时间")
    private Date outboundTime;

    @ApiModelProperty(value = "收货单位Id")
    private String purchasingOrgId;

    @ApiModelProperty(value = "收货单位")
    private String purchasingOrgName;

    @ApiModelProperty(value = "供货单位Id")
    private String supplierId;

    @ApiModelProperty(value = "供货单位")
    private String supplierName;

    @ApiModelProperty(value = "仓库id")
    private String warehouseId;

    @ApiModelProperty(value = "合同编号")
    private String contractNo;


    @ApiModelProperty(value = "出库方式  1-手动出库 2-自动出库")
    private Integer outboundType;

    @ApiModelProperty(value = "审核状态")
    private Integer auditStatus;


    @ApiModelProperty(value = "销售含税总价")
    private BigDecimal xsRateAmount;

    @ApiModelProperty(value = "销售不含税总价")
    private BigDecimal xsNoRateAmount;

    @ApiModelProperty(value = "采购含税进价")
    private BigDecimal cgRateAmount;

    @ApiModelProperty(value = "采购不含税进价")
    private BigDecimal cgNoRateAmount;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "项目地址")
    private String projectAddress;

    @ApiModelProperty(value = "数量")
    private BigDecimal num;


    @ApiModelProperty(value = "期数")
    private String periodsNum;

    @ApiModelProperty(value = "供应商发票是否开具 0-否 1-是")
    private Integer supplierInvoiceStatus;

    @ApiModelProperty(value = "申请开票日期")
    private Date applyInvoiceTime;

    @ApiModelProperty(value = "受票单位")
    private String ticketReceivingUnit;

    @ApiModelProperty(value = "受票单位地址")
    private String ticketReceivingUnitAddress;

    @ApiModelProperty(value = "受票单位税号")
    private String ticketReceivingUnitTaxNo;

    @ApiModelProperty(value = "受票单位电话")
    private String ticketReceivingUnitPhone;

    @ApiModelProperty(value = "受票单位开户行")
    private String ticketReceivingUnitBank;

    @ApiModelProperty(value = "受票单位账号")
    private String ticketReceivingUnitAccount;

    @ApiModelProperty(value = "发货人")
    private String receiveName;

    @ApiModelProperty(value = "发货人手机号")
    private String receivePhone;

    @ApiModelProperty(value = "创建时间")
    private Date gmtCreate;

    @ApiModelProperty(value = "出库明细")
    private String settlementInfo;
}
