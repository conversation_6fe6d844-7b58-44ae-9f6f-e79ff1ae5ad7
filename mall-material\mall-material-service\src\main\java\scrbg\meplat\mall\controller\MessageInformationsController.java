package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.entity.MessageInformations;
import scrbg.meplat.mall.entity.StationMessage;
import scrbg.meplat.mall.service.MessageInformationsService;

import java.util.List;

/**
 * @描述：反馈中心控制类
 * @作者: y
 * @日期: 2022-11-22
 */
@RestController
@RequestMapping("/platform/messageInformations")
@Api(tags = "反馈中心")
public class MessageInformationsController {
    @Autowired
    public MessageInformationsService messageInformationsService;
    @PostMapping("/create")
    @ApiOperation(value = "新增新留言")
    public R save(@RequestBody MessageInformations messageInformations) {
        messageInformationsService.createMessage(messageInformations);
        return R.success();
    }

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页，用户查询留言")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<StationMessage> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = messageInformationsService.queryPage(jsonObject, new LambdaQueryWrapper<MessageInformations>());
        return PageR.success(page);
    }

    @PostMapping("/listByShowEntity")
    @ApiOperation(value = "根据实体属性分页，用户查看公开留言")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<StationMessage> listByShowEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = messageInformationsService.queryShowPage(jsonObject, new LambdaQueryWrapper<MessageInformations>());
        return PageR.success(page);
    }
    @PostMapping("/createRespond")
    @ApiOperation(value = "新增回复")
    public R respond(@RequestBody MessageInformations messageInformations) {
        messageInformationsService.createRespond(messageInformations);
        return R.success();
    }
//    @PostMapping("/update")
//    @ApiOperation(value = "修改")
//    public R update(@RequestBody MessageInformations messageInformations) {
//        messageInformationsService.update(messageInformations);
//        return R.success();
//    }

    @PostMapping("/listByEntit")
    @ApiOperation(value = "根据实体属性分页，管理员查询所有留言")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<StationMessage> listByAllEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = messageInformationsService.queryAllPage(jsonObject, new LambdaQueryWrapper<MessageInformations>());
        return PageR.success(page);
    }


    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        messageInformationsService.delete(id);
        return R.success();
    }


    @PostMapping("/updateNotPublish")
    @ApiOperation(value = "批量取消展示")
    public R updateNotPublish(@RequestBody List<String> ids) {
        messageInformationsService.updateByPublish(ids, "0");
        return R.success();
    }

    @PostMapping("/updateByPublish")
    @ApiOperation(value = "批量展示")
    public R updatePublish(@RequestBody List<String> ids) {
        messageInformationsService.updateByPublish(ids, "1");
        return R.success();
    }

}

