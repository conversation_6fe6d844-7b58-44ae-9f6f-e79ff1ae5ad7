package scrbg.meplat.mall.dto.plan;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2023-06-30 14:06
 */
@Data
public class UpdatePlanChangeDtlByPlanIdDtlDTO {

    @ApiModelProperty(value = "变更计划明细id")
    private String planDtlChangeId;


    @ApiModelProperty(value = "变更计划id")

    private String planChangeId;

    @ApiModelProperty(value = "源计划明细id")

    private String planDtlId;


    @ApiModelProperty(value = "源计划id")

    private String planId;

    @ApiModelProperty(value = "物资id")

    private String materialId;

    @ApiModelProperty(value = "分类路径名称（xxx/xxx/xxx）")
    private String classPathName;

    @ApiModelProperty(value = "分类路径id（xxx/xxx/xxx）")
    private String classPathId;

    @ApiModelProperty(value = "物资名称")

    private String materialName;


    @ApiModelProperty(value = "规格型号")

    private String spec;


    @ApiModelProperty(value = "计量单位")

    private String unit;


    @ApiModelProperty(value = "材质")

    private String texture;


    @ApiModelProperty(value = "本期计划数量")

    private BigDecimal thisPlanQty;


    @ApiModelProperty(value = "计划明细总数量")

    private BigDecimal sourceQty;


    @ApiModelProperty(value = "状态")

    private Integer state;



    @ApiModelProperty(value = "合同明细id")

    private String contractDtlId;


    @ApiModelProperty(value = "已生成订单数量")

    private BigDecimal orderQty;


    @ApiModelProperty(value = "明细修改状态（1新增2修改3删除）默认修改")

    private Integer dtlUpdateState;





}
