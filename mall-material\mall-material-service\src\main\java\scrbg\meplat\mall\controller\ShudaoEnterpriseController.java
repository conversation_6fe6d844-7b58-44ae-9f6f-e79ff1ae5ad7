package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

import org.springframework.web.multipart.MultipartFile;
import scrbg.meplat.mall.dto.payment.ImportPayDto;
import scrbg.meplat.mall.dto.payment.SdImportDto;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.service.ShudaoEnterpriseService;
import scrbg.meplat.mall.entity.ShudaoEnterprise;
import scrbg.meplat.mall.util.R;
import scrbg.meplat.mall.util.excel.EasyExcelUtils;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @描述：控制类
 * @作者: ye
 * @日期: 2023-08-15
 */
@RestController
@RequestMapping("/shudaoEnterprise")
@Api(tags = "")
public class ShudaoEnterpriseController {

    @Autowired
    public ShudaoEnterpriseService shudaoEnterpriseService;

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<ShudaoEnterprise> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = shudaoEnterpriseService.queryPage(jsonObject, new LambdaQueryWrapper<ShudaoEnterprise>());
        return PageR.success(page);
    }

    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<ShudaoEnterprise> findById(String id) {
        ShudaoEnterprise shudaoEnterprise = shudaoEnterpriseService.getById(id);
        return R.success(shudaoEnterprise);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody ShudaoEnterprise shudaoEnterprise) {
        shudaoEnterpriseService.create(shudaoEnterprise);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody ShudaoEnterprise shudaoEnterprise) {
        shudaoEnterpriseService.update(shudaoEnterprise);
        return R.success();
    }

    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        shudaoEnterpriseService.delete(id);
        return R.success();
    }


    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")
    public R deleteBatch(@RequestBody List<String> ids) {
        if (!CollectionUtils.isEmpty(ids)){
            for (String id : ids) {
                shudaoEnterpriseService.delete(id);
            }
        }
        return R.success();
    }
    @PostMapping("/excel/OutputExcel")
    @ApiOperation(value = "导初蜀道信息")
    public void exportShopPayExcelFile(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        shudaoEnterpriseService.exportShopPayExcelFile(jsonObject,response);
    }
    @PostMapping("/excel/uploadSdExcelFile")
    @ApiOperation(value = "导入蜀道信息")
    public void uploadShopPayExcelFile(@RequestPart("file") MultipartFile file, HttpServletResponse response) {
        shudaoEnterpriseService.uploadSdExcelFile(file,response);
    }
    @GetMapping("/excel/sdTemplate")
    @ApiOperation(value = "蜀道导入模板")
    public void supplierTemplate(HttpServletResponse response) {
        try {
            EasyExcelUtils.writeWeb("蜀道企业导入模板", SdImportDto.class, null, "蜀道企业模板", response);
        } catch (Exception e) {
            throw new BusinessException("模板下载失败" + e.getMessage());
            //log.error("异常信息：" + e.getMessage());
        }
    }
}

