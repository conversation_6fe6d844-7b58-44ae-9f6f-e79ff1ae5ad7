package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.config.auth.IsRole;
import scrbg.meplat.mall.config.auth.RoleEnum;
import scrbg.meplat.mall.config.logRecording.LogRecord;
import scrbg.meplat.mall.config.logRecording.enums.BusinessType;
import scrbg.meplat.mall.config.logRecording.enums.OperatorType;
import scrbg.meplat.mall.config.redis.redisson.NotResubmit;
import scrbg.meplat.mall.controller.website.userCenter.UserCenterOrderController;
import scrbg.meplat.mall.dto.ReturnProductUpdateOrderDTO;
import scrbg.meplat.mall.dto.bidding.CreateBidingByOrderDTO;
import scrbg.meplat.mall.dto.order.*;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.service.*;
import scrbg.meplat.mall.service.impl.OrdersServiceImpl;
import scrbg.meplat.mall.util.LogUtil;
import scrbg.meplat.mall.util.RestTemplateUtils;
import scrbg.meplat.mall.vo.platform.PlatformShopCountVo;
import scrbg.meplat.mall.vo.platform.ShopCountVo;
import scrbg.meplat.mall.vo.shopManage.reportForms.ProductFromVo;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


/**
 * @描述：商品订单控制类
 * @作者: y
 * @日期: 2022-11-02
 */
@RestController
@RequestMapping("/")
@ApiSort(value = 500)
@Api(tags = "订单管理")
public class OrdersController {

    @Autowired
    public OrdersService ordersService;

    @Autowired
    OrderItemService orderItemService;

    @Autowired
    ShopService shopService;

    @Autowired
    InterfaceLogsService interfaceLogsService;

    @Autowired
    RestTemplateUtils restTemplateUtils;

    @Autowired
    MallConfig mallConfig;

    @PostMapping("/platform/orders/listByParameters")
    @ApiOperation(value = "根据参数分页查询订单信息（平台）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "startDate", value = "创建时间开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endDate", value = "创建时间结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "okStartDate", value = "确认时间开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "okEndDate", value = "确认时间结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "startSuccessDate", value = "完成时间开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endSuccessDate", value = "完成时间结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "isQueryTwoOrder", value = "是否查询拆分订单（默认不查询）", dataTypeClass = Boolean.class),
            @DynamicParameter(name = "parentOrderId", value = "父订单id", dataTypeClass = String.class),
            @DynamicParameter(name = "deliverGoodsStartDate", value = "发货时间开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "deliverGoodsEndDate", value = "发货时间结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "keywords", value = "关键字（商品名称，订单号，店铺名称）", dataTypeClass = String.class),
            @DynamicParameter(name = "orderSn", value = "订单编号", dataTypeClass = String.class),
            @DynamicParameter(name = "orderClass", value = "订单类别", dataTypeClass = String.class),
            @DynamicParameter(name = "untitled", value = "产品名称", dataTypeClass = String.class),
            @DynamicParameter(name = "shopName", value = "店铺名称", dataTypeClass = String.class),
            @DynamicParameter(name = "belowPrice", value = "以下价格", dataTypeClass = String.class),
            @DynamicParameter(name = "abovePrice", value = "以上价格", dataTypeClass = String.class),
            @DynamicParameter(name = "state", value = "订单状态：0草稿 1已提交 2待确认 3已确认 4待签订合同 5已签合同 6已完成 7已关闭", dataTypeClass = Integer.class),
            @DynamicParameter(name = "productType", value = "商品类型：0物资 1设备 2周材（设备租赁） 3周材（设备） 4二手设备 5租赁设备", dataTypeClass = Integer.class),
            @DynamicParameter(name = "orderBy", value = "订单排序：1创建时间2确认时间3发货时间", dataTypeClass = Integer.class)
    })
    public PageR<Orders> listPlatformOrdersByParameters(@RequestBody JSONObject jsonObject) {
        PageUtils page = ordersService.listPlatformOrdersByParameters(jsonObject, Wrappers.lambdaQuery(Orders.class));
        return PageR.success(page);
    }

    @PostMapping("/platform/orders/purchaseList")
    @ApiOperation(value = "采购员App（平台）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "startDate", value = "创建时间开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endDate", value = "创建时间结束时间", dataTypeClass = String.class),
    })
    public PageR<Orders> purchaseList(@RequestBody JSONObject jsonObject) {
        PageUtils page = ordersService.purchaseList(jsonObject, Wrappers.lambdaQuery(Orders.class));
        return PageR.success(page);
    }

    @PostMapping("/platform/orders/selectOrderList")
    @ApiOperation(value = "采购员查看订单（履约平台）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "startDate", value = "创建时间开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endDate", value = "创建时间结束时间", dataTypeClass = String.class),
    })
    public PageR<Orders> selectOrderList(@RequestBody JSONObject jsonObject) {
        PageUtils page = ordersService.selectOrderList(jsonObject, Wrappers.lambdaQuery(Orders.class));
        return PageR.success(page);
    }

    @PostMapping("/orders/getPlatformOrdersCount")
    @ApiOperation(value = "根据参数分页查询订单信息（订单统计后台）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "productType", value = "商品类型", dataTypeClass = Integer.class),
            @DynamicParameter(name = "startDate", value = "完成时间开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endDate", value = "完成时间结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "isShop", value = "是否店铺统计", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = Integer.class),
            @DynamicParameter(name = "date", value = "时间筛选", dataTypeClass = String.class),
            @DynamicParameter(name = "orderCountOrProfitPriceTotal", value = "订单数或总利润", dataTypeClass = Integer.class),
    })
    @IsRole(roleName = RoleEnum.ROLE_11)
    public PageR getPlatformOrdersCount(@RequestBody JSONObject jsonObject) {
        PageUtils page = ordersService.getPlatformOrdersCount(jsonObject, Wrappers.lambdaQuery(Orders.class));
        return PageR.success(page);
    }

    @PostMapping("/orders/getTwoMaterialOrderList")
    @ApiOperation(value = "根据二级供应商查询可对账的发货单和退货单物资(物资公司)")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "startDate", value = "完成时间开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endDate", value = "完成时间结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "productType", value = "商品类型", dataTypeClass = Integer.class),
    })
    public PageR getTwoMaterialOrderList(@RequestBody JSONObject jsonObject) {
        PageUtils page = ordersService.getTwoMaterialOrderList(jsonObject, Wrappers.lambdaQuery(Orders.class));
        return PageR.success(page);
    }

    @PostMapping("/orders/getTwoSupplierMaterialOrderList")
    @ApiOperation(value = "根据二级供应商查询可对账的发货单和退货单物资（二级供应商）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "startDate", value = "完成时间开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endDate", value = "完成时间结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "productType", value = "商品类型", dataTypeClass = Integer.class),
    })
    public PageR getTwoSupplierMaterialOrderList(@RequestBody JSONObject jsonObject) {
        PageUtils page = ordersService.getTwoSupplierMaterialOrderList(jsonObject, Wrappers.lambdaQuery(Orders.class));
        return PageR.success(page);
    }

    @PostMapping("/orders/getSupplierPlatformOrdersCount")
    @ApiOperation(value = "根据参数分页查询订单信息（订单统计供应商平台）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "startDate", value = "完成时间开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endDate", value = "完成时间结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "isShop", value = "是否店铺统计", dataTypeClass = Integer.class),
    })
    public PageR getSupplierPlatformOrdersCount(@RequestBody JSONObject jsonObject) {
        PageUtils page = ordersService.getPlatformOrdersCount(jsonObject, Wrappers.lambdaQuery(Orders.class));
        return PageR.success(page);
    }

    @PostMapping("/platform/orderItem/listByParameters")
    @ApiOperation(value = "根据参数分页查询订单项（平台）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "orderId", value = "订单id", dataTypeClass = String.class),
            @DynamicParameter(name = "keywords", value = "关键字（规格名称）", dataTypeClass = String.class),
            @DynamicParameter(name = "productType", value = "商品类型：0物资 1设备 2周材（设备租赁） 3周材（设备） 4二手设备 5租赁设备", dataTypeClass = Integer.class)
    })
    public PageR<OrderItem> listOrderItemByParameters(@RequestBody JSONObject jsonObject) {
        PageUtils page = orderItemService.queryPage(jsonObject, Wrappers.lambdaQuery(OrderItem.class));
        return PageR.success(page);
    }

    @PostMapping("/shopManage/orders/listByParameters")
    @ApiOperation(value = "根据参数分页查询订单信息（店铺）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "startDate", value = "创建时间开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endDate", value = "创建时间结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "okStartDate", value = "确认时间开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "startSuccessDate", value = "完成时间开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endSuccessDate", value = "完成时间结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "okEndDate", value = "确认时间结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "parentOrderId", value = "父订单id", dataTypeClass = String.class),
            @DynamicParameter(name = "orgId", value = "本地机构id", dataTypeClass = String.class),
            @DynamicParameter(name = "isQueryTwoOrder", value = "是否查询拆分订单（默认不查询）", dataTypeClass = Boolean.class),
            @DynamicParameter(name = "deliverGoodsStartDate", value = "发货时间开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "deliverGoodsEndDate", value = "发货时间结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "orderSn", value = "订单编号", dataTypeClass = String.class),
            @DynamicParameter(name = "orderClass", value = "订单类型", dataTypeClass = String.class),
            @DynamicParameter(name = "orderClass", value = "订单类别", dataTypeClass = Integer.class),
            @DynamicParameter(name = "untitled", value = "产品名称", dataTypeClass = String.class),
            @DynamicParameter(name = "shopName", value = "店铺名称", dataTypeClass = String.class),
            @DynamicParameter(name = "keywords", value = "关键字（商品名称，订单号，店铺名称）", dataTypeClass = String.class),
            @DynamicParameter(name = "belowPrice", value = "以下价格", dataTypeClass = String.class),
            @DynamicParameter(name = "abovePrice", value = "以上价格", dataTypeClass = String.class),
            @DynamicParameter(name = "state", value = "订单状态：0草稿 1已提交 2待确认 3已确认 4待签订合同 5已签合同 6已完成 7已关闭", dataTypeClass = Integer.class),
            @DynamicParameter(name = "productType", value = "商品类型：0物资 1设备 2周材（设备租赁） 3周材（设备） 4二手设备 5租赁设备", dataTypeClass = Integer.class),
            @DynamicParameter(name = "orderBy", value = "订单排序：1创建时间2确认时间3发货时间", dataTypeClass = Integer.class)
    })
    public PageR<Orders> listShopManageOrdersByParameters(@RequestBody JSONObject jsonObject) {
        PageUtils page = ordersService.listShopManageOrdersByParameters(jsonObject, Wrappers.lambdaQuery(Orders.class));
        return PageR.success(page);
    }

    @PostMapping("/shopManage/orders/listContractTwoOrderList")
    @ApiOperation(value = "根据分页参数查询大宗合同二级订单")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
            @DynamicParameter(name = "parentOrderId", value = "父订单id", dataTypeClass = String.class),
            @DynamicParameter(name = "isQueryTwoOrder", value = "是否查询拆分订单（默认不查询）", dataTypeClass = Boolean.class),
            @DynamicParameter(name = "productType", value = "商品类型：0物资 1设备 2周材（设备租赁） 3周材（设备） 4二手设备 5租赁设备", dataTypeClass = Integer.class),
    })
    public PageR<Orders> listContractTwoOrderList(@RequestBody JSONObject jsonObject) {
        PageUtils page = ordersService.listContractTwoOrderList(jsonObject, Wrappers.lambdaQuery(Orders.class));
        return PageR.success(page);
    }

    @PostMapping("/shopManage/orders/listByParametersExport2")
    @ApiOperation(value = "根据参数分页查询订单信息（店铺导出pdf）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "startDate", value = "创建时间开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endDate", value = "创建时间结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "okStartDate", value = "确认时间开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "okEndDate", value = "确认时间结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "parentOrderId", value = "父订单id", dataTypeClass = String.class),
            @DynamicParameter(name = "orgId", value = "本地机构id", dataTypeClass = String.class),
            @DynamicParameter(name = "isQueryTwoOrder", value = "是否查询拆分订单（默认不查询）", dataTypeClass = Boolean.class),
            @DynamicParameter(name = "deliverGoodsStartDate", value = "发货时间开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "deliverGoodsEndDate", value = "发货时间结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "orderSn", value = "订单编号", dataTypeClass = String.class),
            @DynamicParameter(name = "orderClass", value = "订单类型", dataTypeClass = String.class),
            @DynamicParameter(name = "orderClass", value = "订单类别", dataTypeClass = Integer.class),
            @DynamicParameter(name = "untitled", value = "产品名称", dataTypeClass = String.class),
            @DynamicParameter(name = "shopName", value = "店铺名称", dataTypeClass = String.class),
            @DynamicParameter(name = "keywords", value = "关键字（商品名称，订单号，店铺名称）", dataTypeClass = String.class),
            @DynamicParameter(name = "belowPrice", value = "以下价格", dataTypeClass = String.class),
            @DynamicParameter(name = "abovePrice", value = "以上价格", dataTypeClass = String.class),
            @DynamicParameter(name = "state", value = "订单状态：0草稿 1已提交 2待确认 3已确认 4待签订合同 5已签合同 6已完成 7已关闭", dataTypeClass = Integer.class),
            @DynamicParameter(name = "productType", value = "商品类型：0物资 1设备 2周材（设备租赁） 3周材（设备） 4二手设备 5租赁设备", dataTypeClass = Integer.class),
            @DynamicParameter(name = "orderBy", value = "订单排序：1创建时间2确认时间3发货时间", dataTypeClass = Integer.class),
            @DynamicParameter(name = "ids", value = "订单id", dataTypeClass = Integer.class)
    })
    @LogRecord(title = "订单管理", businessType = BusinessType.EXPORT, operatorType = OperatorType.MANAGE)

    public R shopManageListByParametersExport(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        ordersService.shopManageListByParametersExport(jsonObject, Wrappers.lambdaQuery(Orders.class), response);
        return R.success();
    }

    @PostMapping("/shopManage/orders/listByParametersExport")
    @ApiOperation(value = "根据参数分页查询订单信息（店铺导出pdf根据订单划分）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "startDate", value = "创建时间开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endDate", value = "创建时间结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "okStartDate", value = "确认时间开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "okEndDate", value = "确认时间结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "parentOrderId", value = "父订单id", dataTypeClass = String.class),
            @DynamicParameter(name = "orgId", value = "本地机构id", dataTypeClass = String.class),
            @DynamicParameter(name = "isQueryTwoOrder", value = "是否查询拆分订单（默认不查询）", dataTypeClass = Boolean.class),
            @DynamicParameter(name = "deliverGoodsStartDate", value = "发货时间开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "deliverGoodsEndDate", value = "发货时间结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "orderSn", value = "订单编号", dataTypeClass = String.class),
            @DynamicParameter(name = "orderClass", value = "订单类型", dataTypeClass = String.class),
            @DynamicParameter(name = "orderClass", value = "订单类别", dataTypeClass = Integer.class),
            @DynamicParameter(name = "untitled", value = "产品名称", dataTypeClass = String.class),
            @DynamicParameter(name = "shopName", value = "店铺名称", dataTypeClass = String.class),
            @DynamicParameter(name = "keywords", value = "关键字（商品名称，订单号，店铺名称）", dataTypeClass = String.class),
            @DynamicParameter(name = "belowPrice", value = "以下价格", dataTypeClass = String.class),
            @DynamicParameter(name = "abovePrice", value = "以上价格", dataTypeClass = String.class),
            @DynamicParameter(name = "state", value = "订单状态：0草稿 1已提交 2待确认 3已确认 4待签订合同 5已签合同 6已完成 7已关闭", dataTypeClass = Integer.class),
            @DynamicParameter(name = "productType", value = "商品类型：0物资 1设备 2周材（设备租赁） 3周材（设备） 4二手设备 5租赁设备", dataTypeClass = Integer.class),
            @DynamicParameter(name = "orderBy", value = "订单排序：1创建时间2确认时间3发货时间", dataTypeClass = Integer.class),
            @DynamicParameter(name = "ids", value = "订单id", dataTypeClass = Integer.class)
    })
    @LogRecord(title = "订单管理", businessType = BusinessType.EXPORT, operatorType = OperatorType.MANAGE)

    public R shopManageListByParametersExport2(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        ordersService.shopManageListByParametersExport2(jsonObject, Wrappers.lambdaQuery(Orders.class), response);
        return R.success();
    }

    @PostMapping("/shopManage/orders/listByParametersLCExport")
    @ApiOperation(value = "根据参数分页查询订单信息（大宗零购超期垫资利和货款支付周期店铺导出pdf根据订单划分）")
    public R listByParametersLCExport(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        ordersService.listByParametersLCExport(jsonObject, Wrappers.lambdaQuery(Orders.class), response);
        return R.success();
    }

    @GetMapping("/shopManage/orders/getOrderInfoOutPutPdf")
    @ApiOperation(value = "根据订单id导出订单pdf（店铺导出pdf）")
    @LogRecord(title = "订单管理", businessType = BusinessType.EXPORT, operatorType = OperatorType.MANAGE)

    public R getOrderInfoOutPutPdf(String orderId, HttpServletResponse response) {
        ordersService.getOrderInfoOutPutPdf(orderId, response);
        return R.success();
    }

    @PostMapping("/shopManage/orders/getTwoOrderList")
    @ApiOperation(value = "查询拆的订单（店铺）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "affirmState", value = "拆单供方确认状态（0默认1已确认2已拒绝3未选择供方）", dataTypeClass = Integer.class),
            @DynamicParameter(name = "affirmStates", value = "拆单供方确认状态（0默认1已确认2已拒绝3未选择供方）", dataTypeClass = List.class),
            @DynamicParameter(name = "startDate", value = "创建时间开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endDate", value = "创建时间结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "okStartDate", value = "确认时间开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "okEndDate", value = "确认时间结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "parentOrderId", value = "父订单id", dataTypeClass = String.class),
            @DynamicParameter(name = "deliverGoodsStartDate", value = "发货时间开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "deliverGoodsEndDate", value = "发货时间结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "orderSn", value = "订单编号", dataTypeClass = String.class),
            @DynamicParameter(name = "orderClass", value = "订单类别", dataTypeClass = String.class),
            @DynamicParameter(name = "untitled", value = "产品名称", dataTypeClass = String.class),
            @DynamicParameter(name = "shopName", value = "店铺名称", dataTypeClass = String.class),
            @DynamicParameter(name = "keywords", value = "关键字（商品名称，订单号，店铺名称）", dataTypeClass = String.class),
            @DynamicParameter(name = "belowPrice", value = "以下价格", dataTypeClass = String.class),
            @DynamicParameter(name = "abovePrice", value = "以上价格", dataTypeClass = String.class),
            @DynamicParameter(name = "state", value = "订单状态：0草稿 1已提交 2待确认 3已确认 4待签订合同 5已签合同 6待发货 7已关闭 8 已发货 9待收货 10 已完成", dataTypeClass = Integer.class),
            @DynamicParameter(name = "productType", value = "商品类型：0物资 1设备 2周材（设备租赁） 3周材（设备） 4二手设备 5租赁设备", dataTypeClass = Integer.class),
            @DynamicParameter(name = "orderBy", value = "订单排序：1创建时间2确认时间3发货时间", dataTypeClass = Integer.class)
    })
    public PageR<Orders> getTwoOrderList(@RequestBody JSONObject jsonObject) {
        PageUtils page = ordersService.getTwoOrderList(jsonObject, Wrappers.lambdaQuery(Orders.class));
        return PageR.success(page);
    }
//    @PostMapping("/shopManage/orders/updateOrderPrice")
//    @ApiOperation(value = "改价")
//    public R updateOrderPrice(@Valid @RequestBody List<UpdateOrderPriceDTO> dtos) {
//        orderItemService.updateOrderPrice(dtos);
//        return R.success();
//    }


    @PostMapping("/shopManage/orders/updateOrderCostPrice")
    @ApiOperation(value = "大宗临购修改供方综合单价")
    @LogRecord(title = "订单管理", businessType = BusinessType.UPDATE, operatorType = OperatorType.MANAGE)
    public R updateOrderCostPrice(@Valid @RequestBody List<UpdateCostPriceDTO> dtos) {
        orderItemService.updateOrderCostPrice(dtos);
        return R.success();
    }

    @GetMapping("/orders/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "ID", required = true,
            dataType = "String", paramType = "query")
    })
    public R<Orders> findById(String id) {
        Orders orders = ordersService.getById(id);
        return R.success(orders);
    }

    @GetMapping("/orders/findByOrderSn")
    @ApiOperation(value = "根据订单号")
    @ApiImplicitParams({@ApiImplicitParam(name = "订单号", value = "orderSn", required = true,
            dataType = "String", paramType = "query")
    })
    public R<Orders> findByOrderSn(String orderSn) {
        Orders one = ordersService.getDataByOrderSn(orderSn);
        return R.success(one);
    }

    //
//    @PostMapping("/create")
//    @ApiOperation(value = "新增")
//    public R save(@RequestBody Orders orders) {
//        ordersService.create(orders);
//        return R.success();
//    }
//
    @PostMapping("/platform/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody Orders orders) {
        ordersService.update(orders);
        return R.success();
    }

    @GetMapping("/shopManage/orders/closeOrderSupplier")
    @ApiOperation(value = "清除供应商")
    public R closeOrderSupplier(String orderId) {
        ordersService.closeOrderSupplier(orderId);
        return R.success();
    }

    @PostMapping("/platform/orders/updateBatch")
    @ApiOperation(value = "批量修改")
    @NotResubmit
    public R updateBatch(@RequestBody List<Orders> orders) {
        ordersService.updateBatch(orders);
        return R.success();
    }

    @PostMapping("/shopManage/orders/update")
    @ApiOperation(value = "店铺修改订单")
    @NotResubmit
    public R shopUpdateOrder(@RequestBody Orders orders) {
        ordersService.update(orders);
        return R.success();
    }

    @PostMapping("/shopManage/orders/addOrderSupplier")
    @ApiOperation(value = "选择供应商")
    @NotResubmit
    @LogRecord(title = "订单管理", businessType = BusinessType.UPDATE, operatorType = OperatorType.MANAGE)
    public R addOrderSupplier(@RequestBody Orders orders) {
        ordersService.addOrderSupplier(orders);
        return R.success();
    }

    @GetMapping("/shopManage/orders/affirmTwoOrder")
    @ApiOperation(value = "供方确认订单")
    public R affirmTwoOrder(String orderId, Boolean isAffirm) {
        ordersService.affirmTwoOrder(orderId, isAffirm);
        return R.success();
    }

    @PostMapping("/shopManage/orders/masterAffirmTwoOrder")
    @ApiOperation(value = "主订单方确认订单")
    public R masterAffirmTwoOrder(@RequestBody MasterAffirmTwoOrderDTO dto) {
        ordersService.masterAffirmTwoOrder(dto);
        return R.success();
    }

    @PostMapping("/platform/orders/batchShipments")
    @ApiOperation(value = "批量发货")
    @NotResubmit
    @LogRecord(title = "订单管理", businessType = BusinessType.UPDATE, operatorType = OperatorType.MANAGE)
    public R batchShipments(@RequestBody List<BatchShipmentsDTO> dtos) {
        ordersService.batchShipments(dtos);
        return R.success();
    }


//    @GetMapping("/delete")
//    @ApiOperation(value = "根据主键删除")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "id", value = "ID", required = true,
//                    dataType = "String", paramType = "query")
//    })
//    public R delete(String id) {
//        ordersService.delete(id);
//        return R.success();
//    }
//
//
//    @PostMapping("/deleteBatch")
//    @ApiOperation(value = "根据主键批量删除")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "ids", value = "IDS", required = true,
//                    dataType = "list", paramType = "query")
//    })
//    public R deleteBatch(@RequestBody List<String> ids) {
//        ordersService.removeLoginByIds(ids);
//        return R.success();
//    }

    @PostMapping("platform/orders/getPlatformOrdersShopCount")
    @ApiOperation(value = "根据参数分页查询订单信息（商铺统计）")
    @IsRole(roleName = RoleEnum.ROLE_11)
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "startDate", value = "完成时间开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endDate", value = "完成时间结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "isShop", value = "是否店铺统计", dataTypeClass = Integer.class),
            @DynamicParameter(name = "productType", value = "商品类型", dataTypeClass = Integer.class),
    })
    public R<PlatformShopCountVo> getPlatformShopOrdersCount(@RequestBody JSONObject jsonObject) {
        PlatformShopCountVo vo = ordersService.getPlatformShopOrderCount(jsonObject, new QueryWrapper<ShopCountVo>());
        return R.success(vo);
    }

    @PostMapping("platform/orders/getPlatformOrdersShopCountExcel")
    @ApiOperation(value = "根据参数分页查询订单信息（商铺统计导出）")
    @IsRole(roleName = RoleEnum.ROLE_11)
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "startDate", value = "完成时间开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endDate", value = "完成时间结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "isShop", value = "是否店铺统计", dataTypeClass = Integer.class),
    })
    @LogRecord(title = "订单管理", businessType = BusinessType.EXPORT, operatorType = OperatorType.MANAGE)

    public R getPlatformOrdersShopCountExcel(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        ordersService.getPlatformOrdersShopCountExcel(jsonObject, new QueryWrapper<ProductFromVo>(), response);
        return R.success("导出成功");
    }

    @PostMapping("/shopManage/orders/batchUpdateTwoOrderPrice")
    @ApiOperation(value = "批量修改二级订单供方价格")
    @NotResubmit
    public R batchUpdateTwoOrderPrice(@RequestBody List<UpdateTwoOrderPriceDTO> dtos) {
        ordersService.batchUpdateTwoOrderPrice(dtos);
        return R.success();
    }

    @PostMapping("/shopManage/orders/returnProductUpdateOrder")
    @ApiOperation(value = "退货修改金额")
    @NotResubmit
    public R returnProductUpdateOrder(@RequestBody @Valid ReturnProductUpdateOrderDTO dto) {
        ordersService.returnProductUpdateOrder(dto);
        return R.success();
    }

    @PostMapping("/shopManage/orders/batchCreateTwoOrder")
    @ApiOperation(value = "根据主订单项id生成零星采购子订单（选择供方生成二级订单）")
    @NotResubmit
    public R batchCreateTwoOrder(@Valid @RequestBody BatchCreateTwoOrderDTO dto) {
        ordersService.batchCreateTwoOrder(dto);
        return R.success();
    }

    @PostMapping("/shopManage/orders/batchCreateLinXingTwoOrder")
    @ApiOperation(value = "根据主订单项id生成大宗临购子订单（选择供方生成二级订单）")
    @NotResubmit
    public R batchCreateLinXingTwoOrder(@Valid @RequestBody BatchCreateTwoOrderDTO dto) {
        ordersService.batchCreateLinXingTwoOrder(dto);
        return R.success();
    }

    @PostMapping("/shopManage/orders/batchCreateContractTwoOrder")
    @ApiOperation(value = "根据主订单项id生成大宗子订单（选择供方生成二级订单）")
    @NotResubmit
    public R batchCreateContractTwoOrder(@Valid @RequestBody BatchCreateTwoOrderDTO dto) {
        ordersService.batchCreateContractTwoOrder(dto);
        return R.success();
    }

    @PostMapping("/shopManage/orders/updateToBidingState")
    @ApiOperation(value = "根据订单明细id修改为待竞价状态")
    @NotResubmit
    public R updateToBidingState(@RequestBody List<String> orderItemIds) {
        ordersService.updateToBidingState(orderItemIds);
        return R.success();
    }

    @PostMapping("/shopManage/orders/createBidingByOrder")
    @ApiOperation(value = "根据订单生成竞价")
    @NotResubmit
    public R createBidingByOrder(@RequestBody CreateBidingByOrderDTO dto) {
        ordersService.createBidingByOrder(dto);
        return R.success();
    }

    @PostMapping("/shopManage/orders/updateBidingByOrder")
    @ApiOperation(value = "修改竞价")
    public R updateBidingByOrder(@RequestBody BiddingPurchase biddingPurchase) {
        ordersService.updateBidingByOrder(biddingPurchase);
        return R.success();
    }

    @PostMapping("/shopManage/orders/updateNotUseState")
    @ApiOperation(value = "根据订单明细id修改为未分配状态")
    @NotResubmit
    public R updateNotUseState(@RequestBody List<String> orderItemIds) {
        ordersService.updateNotUseState(orderItemIds);
        return R.success();
    }

    @PostMapping("/orderItem/getPlatformOrderItemCount")
    @ApiOperation(value = "根据参数分页查询订单信息（订单统计）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "startDate", value = "完成时间开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endDate", value = "完成时间结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "isShop", value = "是否店铺统计", dataTypeClass = Integer.class),
    })
    public PageR getPlatformOrderItemCount(@RequestBody JSONObject jsonObject) {
        PageUtils page = orderItemService.getPlatformOrderItemCount(jsonObject, new QueryWrapper<OrderItem>());
        return PageR.success(page);
    }

    @PostMapping("/shopManage/orders/getOrderItemPriceByMas")
    @ApiOperation(value = "根据可对账物资获取订单含税单价")
    public R getOrderItemPriceByMas(@RequestBody List<Map> maps) {
        List<Map> vos = ordersService.getOrderItemPriceByMas(maps);
        return R.success(vos);
    }

    @GetMapping("/shopManage/orders/orderCloseClick")
    @ApiOperation(value = "订单关单")
    public R orderCloseClick(String orderId) {
        String idStr = IdWorker.getIdStr();
        StringBuilder farArg = new StringBuilder();
        Integer productType = null;
        try {
            productType = ordersService.orderCloseClick(orderId, idStr, farArg);
        } catch (Exception e) {
            e.printStackTrace();
            if (productType != null && productType == 10) {
                LogUtil.writeErrorLog(idStr, "orderCloseClick", orderId, null, null, e.getMessage(), OrdersServiceImpl.class);
                InterfaceLogs iLog = new InterfaceLogs();
                iLog.setSecretKey(idStr);
                iLog.setClassPackage(OrdersController.class.getName());
                iLog.setMethodName("orderCloseClick");
                iLog.setLocalArguments(orderId);
                iLog.setFarArguments(farArg.toString());
                iLog.setIsSuccess(0);
                iLog.setLogType(1);
                iLog.setErrorInfo(e.getMessage());
                interfaceLogsService.create(iLog);
                restTemplateUtils.getPCWP2NotParams(mallConfig.prodPcwp2Url02 + "/thirdapi/sporadicPurchasePlan/rollBackUpdateShopDtl?keyId=" + idStr);
                throw new BusinessException(e.getMessage());
            } else {
                throw new BusinessException(e.getMessage());
            }
        }
        return R.success();
    }
}

