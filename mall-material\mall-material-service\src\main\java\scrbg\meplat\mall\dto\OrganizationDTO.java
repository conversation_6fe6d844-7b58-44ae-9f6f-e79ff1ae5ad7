package scrbg.meplat.mall.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @create 2022-12-21 14:39
 */
@Data
public class OrganizationDTO {

    @ApiModelProperty(value = "组织名称",required = true)
    @NotEmpty(message = "当前组织名称不能为空!")
    private String orgName;

    @ApiModelProperty(value = "组织id",required = true)
    @NotEmpty(message = "当前组织名称不能为空!")
    private String orgId;

    @ApiModelProperty(value = "机构简码",required = true)
    @NotEmpty(message = "当前组织名称不能为空!")
    private String shortCode;

    @ApiModelProperty(value = "机构类型(1:集团|2:分子公司|4:经理部|5:项目部|6:股份|7:事业部)",required = true)
    @NotNull(message = "当前组织名称不能为空!")
    private Integer orgType;

    @ApiModelProperty(value = "本地用户id")
    private String userId;

    private Integer mallType;
}
