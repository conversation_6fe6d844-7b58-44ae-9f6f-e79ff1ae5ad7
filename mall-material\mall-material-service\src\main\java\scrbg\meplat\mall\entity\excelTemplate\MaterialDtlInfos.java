package scrbg.meplat.mall.entity.excelTemplate;


import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@HeadRowHeight(40)
@ColumnWidth(25)
@ContentRowHeight(40)
public class MaterialDtlInfos implements Serializable {
    private static final long serialVersionUID = -5144055068797033734L;


    @ExcelProperty(value = "序号（自增即可）", index = 0)
    private Long id;

    @ExcelProperty(value = "物料名称", index = 1)
    private String materialName;

    @ExcelProperty(value = "物资类别名称(XXX/XXX/XXX)", index = 2)
    private String classNamePath;

    @ExcelProperty(value = "规格", index = 3)
    private String spec;


    @ExcelProperty(value = "计量单位", index = 4)
    private String unit;

}
