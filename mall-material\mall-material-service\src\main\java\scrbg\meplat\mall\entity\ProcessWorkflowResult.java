package scrbg.meplat.mall.entity;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MallBaseEntity;

/**
 * 流程配置
 * <AUTHOR>
 * @date: 2025年6月20日 上午9:28:16
 */
@ApiModel(value = "流程处理结果")
@Data
public class ProcessWorkflowResult extends MallBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "流程实例ID")
    private String instanceId;

    @ApiModelProperty(value = "当前节点id")
    private String currentNodeId;

    @ApiModelProperty(value = "实例状态(0运行中 1已完成 2已中止)")
    private Integer status;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "操作结果")
    private boolean success;

}