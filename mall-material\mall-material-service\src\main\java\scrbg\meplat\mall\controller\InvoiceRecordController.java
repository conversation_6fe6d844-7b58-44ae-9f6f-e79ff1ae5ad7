package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import scrbg.meplat.mall.service.InvoiceRecordService;
import scrbg.meplat.mall.entity.InvoiceRecord;
import scrbg.meplat.mall.vo.invoice.ChangInvoiceStateVo;

import java.util.List;

/**
 * @描述：控制类
 * @作者: ye
 * @日期: 2023-11-16
 */
@RestController
@RequestMapping("/invoiceRecord")
@Api(tags = "")
public class InvoiceRecordController {

    @Autowired
    public InvoiceRecordService invoiceRecordService;

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<InvoiceRecord> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = invoiceRecordService.queryPage(jsonObject, new LambdaQueryWrapper<InvoiceRecord>());
        return PageR.success(page);
    }

    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<InvoiceRecord> findById(String id) {
        InvoiceRecord invoiceRecord = invoiceRecordService.getById(id);
        return R.success(invoiceRecord);
    }
    @GetMapping("/getData")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<InvoiceRecord> getData() {
        InvoiceRecord invoiceRecord = invoiceRecordService.getData();
        return R.success(invoiceRecord);
    }
    @GetMapping("/getInvoiceRecord/{id}")
    @ApiOperation(value = "根据enterpriseId查询")
    public R<InvoiceRecord> getInvoiceRecord(@PathVariable String id) {
        InvoiceRecord invoiceRecord = invoiceRecordService.getInvoiceRecord(id);
        return R.success(invoiceRecord);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody InvoiceRecord invoiceRecord) {
        invoiceRecordService.create(invoiceRecord);
        return R.success();
    }

    @PostMapping("/updateState")
    @ApiOperation(value = "修改默认值")
    public R updateState(@RequestBody ChangInvoiceStateVo changInvoiceStateVo) {
        invoiceRecordService.updateState(changInvoiceStateVo);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody InvoiceRecord invoiceRecord) {
        invoiceRecordService.update(invoiceRecord);
        return R.success();
    }

    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        invoiceRecordService.delete(id);
        return R.success();
    }


    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")
    public R deleteBatch(@RequestBody List<String> ids) {
        invoiceRecordService.removeByIds(ids);
        return R.success();
    }
}

