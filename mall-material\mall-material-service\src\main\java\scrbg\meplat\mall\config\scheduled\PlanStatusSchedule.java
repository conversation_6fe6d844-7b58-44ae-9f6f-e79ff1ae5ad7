package scrbg.meplat.mall.config.scheduled;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import lombok.extern.log4j.Log4j2;
import scrbg.meplat.mall.entity.plan.Plan;
import scrbg.meplat.mall.pcwp.PcwpPageRes;
import scrbg.meplat.mall.pcwp.PcwpRes;
import scrbg.meplat.mall.pcwp.PcwpService;
import scrbg.meplat.mall.pcwp.third.model.BulkRetailPlanPageQueryCondition;
import scrbg.meplat.mall.pcwp.third.model.BulkRetailPlanPageQueryResult;
import scrbg.meplat.mall.pcwp.third.model.RevolPlanPageQueryCondition;
import scrbg.meplat.mall.pcwp.third.model.RevolPlanPageQueryResult;
import scrbg.meplat.mall.pcwp.third.model.SporadicPurchasePlanPageQueryCondition;
import scrbg.meplat.mall.pcwp.third.model.SporadicPurchasePlanQueryResult;
import scrbg.meplat.mall.service.plan.PlanService;

/**
 * 从pcwp同步零星采购计划的状态，每分钟执行一次
 * TODO 集群模式下定时任务的执行问题
 */
@Log4j2
@Component
@ConditionalOnProperty(name="app.schedule.enable",havingValue = "true")
public class PlanStatusSchedule {

    @Autowired
    private PcwpService pcwpService;

    @Autowired
    private PlanService planService;

    /**
     * 同步零星采购计划状态
     * 1分钟一次，目前方便测试，后续可以根据实际情况调整时间间隔
     */
    @Scheduled(cron = "0 * * * * ?")
    public void syncRetailPlanStatus() {
        Function<Integer, PcwpRes<PcwpPageRes<SporadicPurchasePlanQueryResult>>> resultMapper = page -> {
            SporadicPurchasePlanPageQueryCondition filter = new SporadicPurchasePlanPageQueryCondition();
            filter.setPage(page);
            return pcwpService.queryRetailPlansPage(filter);
        };
        Function<SporadicPurchasePlanQueryResult, String> billIdMapper =  SporadicPurchasePlanQueryResult::getBillId;
        Function<SporadicPurchasePlanQueryResult, Integer> stateMapper = SporadicPurchasePlanQueryResult::getState;
        syncPlanStatus(resultMapper, billIdMapper, stateMapper, "零星采购");
    }

    /**
     * 同步大宗临购计划状态
     * 1分钟一次，目前方便测试，后续可以根据实际情况调整时间间隔
     */
    @Scheduled(cron = "0 * * * * ?")
    public void syncBulkPlanStatus() {
        Function<Integer, PcwpRes<PcwpPageRes<BulkRetailPlanPageQueryResult>>> resultMapper = page -> {
            BulkRetailPlanPageQueryCondition filter = new BulkRetailPlanPageQueryCondition();
            filter.setPage(page);
            return pcwpService.queryPageBulkRetailPlan(filter);
        };
        Function<BulkRetailPlanPageQueryResult, String> billIdMapper =  BulkRetailPlanPageQueryResult::getBillId;
        Function<BulkRetailPlanPageQueryResult, Integer> stateMapper = r -> {
            return Integer.parseInt(r.getState());
        };
        syncPlanStatus(resultMapper, billIdMapper, stateMapper, "大宗临购");
    }
    /**
     * 同步周转材料计划状态
     * 1分钟一次，目前方便测试，后续可以根据实际情况调整时间间隔
     */
    @Scheduled(cron = "0 * * * * ?")
    public void syncRevolPlanStatus() {
        Function<Integer, PcwpRes<PcwpPageRes<RevolPlanPageQueryResult>>> resultMapper = page -> {
            RevolPlanPageQueryCondition filter = new RevolPlanPageQueryCondition();
            filter.setPage(page);
            return pcwpService.queryPageRevolPlan(filter);
        };
        Function<RevolPlanPageQueryResult, String> billIdMapper =  RevolPlanPageQueryResult::getBillId;
        Function<RevolPlanPageQueryResult, Integer> stateMapper = r -> {
            return Integer.parseInt(r.getState());
        };
        syncPlanStatus(resultMapper, billIdMapper, stateMapper, "周转材料");
    }

    public  <S,R> void syncPlanStatus(
        Function<Integer, PcwpRes<PcwpPageRes<R>>> resultMapper, 
        Function<R, String> billIdMapper, 
        Function<R, Integer> stateMapper,
        String typeStr) {
        int page = 1;
        int maxPage = 1000; // 避免无限循环
        // 每次同步一页，如果数据量太大的话，这里考虑用时间切割一下（需要结合业务）
        while (page <= maxPage) {

            PcwpRes<PcwpPageRes<R>> res = resultMapper.apply(page);
            if (res.getCode() != 200 || res.getData() == null) {
                log.error("分页查询"+typeStr+"计划失败,code:{},msg{}", res.getCode(), res.getMessage());
                break;
            }

            List<R> results = res.getData().getList();
            if (results.isEmpty()) {
                break;
            }
            // 查询和更新都只执行一次，减少与数据库的交互，优化性能
            List<String> billIds = results.stream()
                .map(billIdMapper)
                .collect(Collectors.toList());

            List<Plan> planList = planService.lambdaQuery()
                .in(Plan::getPBillId, billIds)
                .list();

            Map<String, Plan> planMap = new HashMap<String, Plan>();
            for (Plan p : planList) {
                planMap.put(p.getPBillId(), p);
            }

            List<Plan> plansToUpdate = new ArrayList<Plan>();
            for (R s : results) {
                String planId = billIdMapper.apply(s);
                int newState = stateMapper.apply(s);
                Plan plan = planMap.get(planId);
                if (plan != null) {
                    // 审核通过了
                    if (newState==1 && plan.getState().equals("1")) {
                        plan.setState("2");
                        plansToUpdate.add(plan);
                    }
                    // 审核没通过
                    if (newState==-1 && plan.getState().equals("1")) {
                        plan.setState("4");
                        plansToUpdate.add(plan);
                        // 需要同步审核不通过理由
                    }
                    // 目前只更新这两种情况，后续需要根据需求优化
                    // TODO 审核没通过的后续流程没有
                }
            }

            if (!plansToUpdate.isEmpty()) {
                log.debug("更新了{}条"+typeStr+"计划数据的状态信息", plansToUpdate.size());
                planService.updateBatchById(plansToUpdate);
            }
            page++;
        }
    }
}

