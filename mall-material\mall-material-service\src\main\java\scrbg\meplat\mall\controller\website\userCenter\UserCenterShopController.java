package scrbg.meplat.mall.controller.website.userCenter;

import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.docx4j.wml.P;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.service.*;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.vo.user.userCenter.OpenShopInfoVO;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * @描述：店铺控制类
 * @作者: y
 * @日期: 2022-11-02
 */
@RestController
@RequestMapping("/userCenter/shop")
@ApiSort(value = 200)
@Api(tags = "店铺（前台）")
public class UserCenterShopController {

    @Autowired
    ShopService shopService;

    @Autowired
    PlatformYearFeeRecordService platformYearFeeRecordService;

    @Autowired
    PlatformYearFeeService platformYearFeeService;

    @Autowired
    EnterpriseInfoService enterpriseInfoService;

    @Autowired
    PlatformFeeFileService platformFeeFileService;

    @Autowired
    AuditRecordService auditRecordService;

    @Autowired
    MallConfig mallConfig;

    @GetMapping("/getRestartOpenShopInfo")
    @ApiOperation(value = "根据店铺id查询店铺信息")
    public R<OpenShopInfoVO> getRestartOpenShopInfo(String shopId) {
        OpenShopInfoVO vo = shopService.getRestartOpenShopInfo(shopId);
        return R.success(vo);
    }

    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<Shop> findById(String id) {
        Shop shop = shopService.getById(id);
        return R.success(shop);
    }

    @GetMapping("/getShopIdBySocialCreditCode")
    @ApiOperation(value = "根据信用代码查询店铺id")
    @ApiImplicitParams({@ApiImplicitParam(name = "socialCreditCode", value = "社会信用代码", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<String> getShopIdBySocialCreditCode(String socialCreditCode) {
        if (StringUtils.isBlank(socialCreditCode)) {
            throw new BusinessException(400, "未携带供应商");
        }
        String shopId = shopService.getShopIdBySocialCreditCode(socialCreditCode);
        return R.success(shopId);
    }

    @GetMapping("/getCurrentStep")
    @ApiOperation(value = "获取当前开店步骤")
    public R<Map> getCurrentStep() {
        HashMap<Object, Object> map = new HashMap<>();
        if (mallConfig.isPlatformFee == 0) {
            map.put("shuDaoFlag", 1);
            map.put("currentStep", 9999);
            return R.success(map);
        }
        Integer step = 1;
        String enterpriseId = ThreadLocalUtil.getCurrentUser().getEnterpriseId();
        Shop shop = shopService.lambdaQuery().eq(Shop::getEnterpriseId, enterpriseId)
                .select(Shop::getAuditStatus, Shop::getShopId).one();
        if (shop != null) {
            if (shop.getAuditStatus() == 2) {
                step = 2;
            }
            if (shop.getAuditStatus() == 1) {
                // 审核通过查询缴费
                Integer count = platformYearFeeRecordService.lambdaQuery()
                        .eq(PlatformYearFeeRecord::getShopId, shop.getShopId())
                        .eq(PlatformYearFeeRecord::getRecordType, 1)
                        .eq(PlatformYearFeeRecord::getState, 1).count();

                PlatformYearFee platformYearFee = platformYearFeeService.lambdaQuery().eq(PlatformYearFee::getShopId, shop.getShopId())
                        .eq(PlatformYearFee::getServeType, 1)
                        .one();
                // 认为是欠费状态
                if (platformYearFee == null || platformYearFee.getServeEndTime() == null) {
                    step = 3; // 待缴费
                    List<PlatformYearFeeRecord> list = platformYearFeeRecordService.lambdaQuery().eq(PlatformYearFeeRecord::getShopId, shop.getShopId())
                            .eq(PlatformYearFeeRecord::getState, 3)
                            .eq(PlatformYearFeeRecord::getRecordType, 1)
                            .orderByDesc(PlatformYearFeeRecord::getGmtCreate)
                            .list();
                    if (list.size() > 0) {
                        PlatformYearFeeRecord platformYearFeeRecord = list.get(0);
                        List<PlatformFeeFile> fileList = platformFeeFileService.lambdaQuery()
                                .eq(PlatformFeeFile::getRelevanceId, platformYearFeeRecord.getPaymentRecordId())
                                .eq(PlatformFeeFile::getRelevanceType, 1).list();
                        platformYearFeeRecord.setFiles(fileList);
                        List<AuditRecord> auditRecords = auditRecordService.lambdaQuery().eq(AuditRecord::getRelevanceId, platformYearFeeRecord.getPaymentRecordId())
                                .eq(AuditRecord::getRelevanceType, 10).list();
                        platformYearFeeRecord.setAuditRecords(auditRecords);
                        map.put("platformYearFeeRecord", platformYearFeeRecord);
                    }
                    if (count > 0) {
                        step = 4; // 缴费审核
                    }
                } else {
                    step = 5; // 已完成
                }
            }
        }
        EnterpriseInfo one = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseId, enterpriseId)
                .select(EnterpriseInfo::getEnterpriseId, EnterpriseInfo::getShuDaoFlag).one();
        if (one.getShuDaoFlag() != null && one.getShuDaoFlag() == 1) {
            map.put("shuDaoFlag", 1);
            map.put("currentStep", 9999);
        } else {
            map.put("shuDaoFlag", 0);
            map.put("currentStep", step);
        }
        return R.success(map);
    }

    @GetMapping("/getIsShowFee")
    @ApiOperation(value = "供应商是否显示缴费菜单以及申请电子招标")
    public R<Map> getIsShowFee() {
        HashMap<Object, Object> map = new HashMap<>();
        String enterpriseId = ThreadLocalUtil.getCurrentUser().getEnterpriseId();
        EnterpriseInfo one = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseId, enterpriseId)
                .select(EnterpriseInfo::getEnterpriseId, EnterpriseInfo::getShuDaoFlag, EnterpriseInfo::getInteriorId).one();
        if (one.getShuDaoFlag() == 1 || one.getInteriorId() != null) {
            map.put("isShowFee", 0);
        } else {
            map.put("isShowFee", 1);
        }
        PlatformYearFee platformYearFee = platformYearFeeService.lambdaQuery().eq(PlatformYearFee::getEnterpriseId, enterpriseId)
                .eq(PlatformYearFee::getServeType, 2).one();
        if (platformYearFee == null) {
            map.put("isBidAddShow", 1);
        } else {
            if (platformYearFee.getServeEndTime() == null) {
                map.put("isBidAddShow", 1);
            } else {
                map.put("isBidAddShow", 0);
            }
        }
        return R.success(map);
    }
}

