package scrbg.meplat.mall.config;

import com.github.xiaoymin.knife4j.spring.annotations.EnableKnife4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.core.annotation.Order;
import springfox.bean.validators.configuration.BeanValidatorPluginsConfiguration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2WebMvc;

@Configuration
@EnableSwagger2WebMvc
@EnableKnife4j
@Import(BeanValidatorPluginsConfiguration.class)
public class SwaggerConfiguration {

    @Bean(value = "api")
    @Order(value = 1)
    public Docket groupRestApi1() {
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(groupApiInfo())
                .groupName("接口")
                .select()
//                .apis(RequestHandlerSelectors.withMethodAnnotation(ApiOperation.class))
                .apis(RequestHandlerSelectors.basePackage("scrbg.meplat.mall.controller"))
//                .apis(RequestHandlerSelectors.withClassAnnotation(Api.class))
                .paths(PathSelectors.any())
                .build();
    }
    @Bean(value = "api2")
    @Order(value = 1)
    public Docket groupRestApi2() {
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(groupApiInfo())
                .groupName("二手设备接口")
                .select()
                .apis(RequestHandlerSelectors.basePackage("scrbg.meplat.mall.outerController"))
                .paths(PathSelectors.any())
                .build();
    }

    @Bean(value = "api3")
    @Order(value = 1)
    public Docket groupRestApi3() {
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(groupApiInfo())
                .groupName("对外公网接口")
                .select()
                .apis(RequestHandlerSelectors.basePackage("scrbg.meplat.mall.outerOpenController"))
                .paths(PathSelectors.any())
                .build();
    }

    @Bean(value = "api4")
    @Order(value = 1)
    public Docket groupRestApi4() {
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(groupApiInfo())
                .groupName("pcwp接口")
                .select()
                .apis(RequestHandlerSelectors.basePackage("scrbg.meplat.mall.openInterface.pcwp2"))
                .paths(PathSelectors.any())
                .build();
    }

    private ApiInfo groupApiInfo() {
        Contact contact = new Contact("hzb", null, "");
        return new ApiInfoBuilder()
                .title("")
                .description("<div style='font-size:14px;color:red;'>商城服务</div>")
                .termsOfServiceUrl("http://www.group.com/")
                .contact(contact)
                .version("1.0")
                .build();
    }

}

