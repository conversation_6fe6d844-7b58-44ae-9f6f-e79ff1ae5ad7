package scrbg.meplat.mall.pcwp.auth.model;

import lombok.Data;

@Data
public class SignUp {
    private UserSignUp user; // 用户注册信息
    private OrgSignUp org; // 机构注册信息
    private String sysCode; // 系统编码

    @Data
    public static class UserSignUp {
        private String userName; // 用户名
        private Integer gender; // 性别(0:女|1:男)
        private String idNo; // 身份证号
        private String password; // 密码(加密后的)
        private String phoneNo; // 手机号
        private String adminNumber;
    }

    @Data
    public static class OrgSignUp {
        private Integer orgType; // 机构类型(1:个人|2:个体户|3:企业)
        private String orgName; // 机构名称
        private String shortCode; // 机构简码
    }
}

