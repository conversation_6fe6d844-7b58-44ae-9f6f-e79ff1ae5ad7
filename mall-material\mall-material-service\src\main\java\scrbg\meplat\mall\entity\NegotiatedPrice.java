package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @描述：报价
 * @作者: y
 * @日期: 2022-11-22
 */
@ApiModel(value = "报价")
@Data
@TableName("negotiated_price")
public class NegotiatedPrice extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "报价id")

    private String negotiatedPriceId;

    @ApiModelProperty(value = "报价金额")

    private BigDecimal enquiryAmount;

    @ApiModelProperty(value = "关联id")

    private String relevanceId;

    @ApiModelProperty(value = "报价类型（0求购2求租3招标）")

    private Integer type;

    @ApiModelProperty(value = "说明")

    private String illustrate;

    @ApiModelProperty(value = "状态")

    private Integer state;


}
