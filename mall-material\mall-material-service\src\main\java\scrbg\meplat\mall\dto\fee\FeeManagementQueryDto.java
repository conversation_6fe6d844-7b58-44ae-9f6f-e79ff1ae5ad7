package scrbg.meplat.mall.dto.fee;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import scrbg.meplat.mall.dto.PageDto;

import java.util.Date;
import java.util.List;

/**
 * @描述：缴费管理查询条件DTO
 * @作者: AI Assistant
 * @日期: 2025-07-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FeeManagementQueryDto extends PageDto {

    @ApiModelProperty(value = "缴费编号")
    private String paymentRecordUn;

    @ApiModelProperty(value = "企业名称")
    private String enterpriseName;

    @ApiModelProperty(value = "店铺名称")
    private String shopName;

    @ApiModelProperty(value = "服务类型（1、店铺年度服务费 2、店铺交易服务费）")
    private Integer serviceType;

    @ApiModelProperty(value = "审核状态  1、审核通过  2、审核不通过")
    private List<Integer> states;

    @ApiModelProperty(value = "缴费时间开始")
    private Date payTimeStart;

    @ApiModelProperty(value = "缴费时间结束")
    private Date payTimeEnd;

    @ApiModelProperty(value = "修改时间开始")
    private Date modifyTimeStart;

    @ApiModelProperty(value = "修改时间结束")
    private Date modifyTimeEnd;

    @ApiModelProperty(value = "审核时间开始")
    private Date auditTimeStart;

    @ApiModelProperty(value = "审核时间结束")
    private Date auditTimeEnd;

    @ApiModelProperty(value = "排序方式（1按缴费时间排序 2按修改时间排序 3按审核时间排序）")
    private Integer orderBy;

    @ApiModelProperty(value = "排序方向（ASC升序 DESC降序，默认DESC）")
    private String orderDirection;
}
