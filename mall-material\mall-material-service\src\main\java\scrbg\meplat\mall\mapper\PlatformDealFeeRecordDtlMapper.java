package scrbg.meplat.mall.mapper;

import org.apache.ibatis.annotations.Select;
import scrbg.meplat.mall.entity.PlatformDealFeeRecordDtl;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;
import org.apache.ibatis.annotations.Mapper;
import scrbg.meplat.mall.vo.fee.DealFeePayDtlList;

import java.util.List;

/**
 * @描述：平台交易费缴费记录明细 Mapper 接口
 * @作者: ye
 * @日期: 2024-01-24
 */
@Mapper
@Repository
public interface PlatformDealFeeRecordDtlMapper extends BaseMapper<PlatformDealFeeRecordDtl> {


    /**
     * 获取缴费明细
     * @param dealFeeRecordId
     * @return
     */
    @Select("SELECT\n" +
            "\tdfrd.deal_fee_record_dtl_id as dealFeeRecordDtlId,\n" +
            "\tdfrd.deal_fee_record_id as dealFeeRecordId,\n" +
            "\tdfrd.platform_deal_fee_dtl_id as platformDealFeeDtlId,\n" +
            "\tdfrd.pay_amount as payAmount,\n" +
            "\tdfd.relevance_id as relevanceId,\n" +
            "\tdfd.relevance_nu as relevanceNu,\n" +
            "\tdfd.relevance_type as relevanceType,\n" +
            "\tdfd.serve_type as serveType,\n" +
            "\tdfd.project_enterprise_id as projectEnterpriseId,\n" +
            "\tdfd.project_enterprise_name as projectEnterpriseName,\n" +
            "\tdfd.enterprise_id as enterpriseId,\n" +
            "\tdfd.enterprise_name as enterpriseName,\n" +
            "\tdfd.serve_fee as serveFee,\n" +
            "\tdfd.fee_ratio as feeRatio,\n" +
            "\tdfd.deal_amount as dealAmount,\n" +
            "\tdfd.pay_fee as payFee,\n" +
            "\tdfd.finish_pay_fee as finishPayFee,\n" +
            "\tdfd.residue_pay_fee as residuePayFee,\n" +
            "\tdfd.gmt_create as gmtCreate\n" +
            "FROM\n" +
            "\tplatform_deal_fee_record_dtl dfrd\n" +
            "\tINNER JOIN platform_deal_fee_dtl dfd ON dfrd.platform_deal_fee_dtl_id = dfd.platform_deal_fee_dtl_id \n" +
            "\twhere dfrd.is_delete = 0 and dfd.is_delete = 0 and dfrd.deal_fee_record_id = #{dealFeeRecordId};")
    List<DealFeePayDtlList> getDealFeePayDtlList(String dealFeeRecordId);

}