package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import scrbg.meplat.mall.entity.FinancialProducts;
import scrbg.meplat.mall.entity.Product;
import scrbg.meplat.mall.service.FinancialProductsService;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @描述：金融产品控制类
 * @作者: sund
 * @日期: 2022-11-10
 */
@RestController
@RequestMapping("")
@ApiSort(value = 500)
@Api(tags = "金融产品")
public class FinancialProductsController {

    @Autowired
    public FinancialProductsService financialProductsService;

    @PostMapping("/frontPage/financialProducts/listFinancialPage/classId")
    @ApiOperation(value = "根据分类id查询金融产品分页列表（前台）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "productName", value = "商品名称", dataTypeClass = String.class),
            @DynamicParameter(name = "classId", value = "分类ID", dataTypeClass = String.class)
    })
    public PageR<Product> frontPageQueryPage(@RequestBody(required = false) JSONObject jsonObject) {
        PageUtils page = financialProductsService.frontPageQueryPage(jsonObject, Wrappers.lambdaQuery(FinancialProducts.class));
        return PageR.success(page);
    }

    @PostMapping("/platform/financialProducts/listFinancialPage/classId")
    @ApiOperation(value = "根据分类id查询金融产品分页列表（平台）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
    })
    public PageR<Product> platformQueryPage(@RequestBody(required = false) JSONObject jsonObject) {
        PageUtils page = financialProductsService.platformQueryPage(jsonObject, Wrappers.lambdaQuery(FinancialProducts.class));
        return PageR.success(page);
    }

    @PostMapping("/shopManagement/financialProducts/listFinancialPage/classId")
    @ApiOperation(value = "根据分类id查询金融产品分页列表（店铺）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "productName", value = "商品名称", dataTypeClass = String.class),
            @DynamicParameter(name = "classId", value = "分类ID", dataTypeClass = String.class)
    })
    public PageR<Product> shopManagementQueryPage(@RequestBody(required = false) JSONObject jsonObject, HttpServletRequest request) {
        PageUtils page = financialProductsService.shopManagementQueryPage(jsonObject, Wrappers.lambdaQuery(FinancialProducts.class), request);
        return PageR.success(page);
    }


//    @GetMapping("/findById")
//    @ApiOperation(value = "根据主键查询")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "id", value = "ID", required = true,
//                    dataType = "String", paramType = "query")
//    })
//    public R<FinancialProducts> findById(String id) {
//        FinancialProducts financialProducts = financialProductsService.getById(id);
//        return R.success(financialProducts);
//    }

    //    @PostMapping("/create")
//    @ApiOperation(value = "新增")
//    public R save(@RequestBody FinancialProducts financialProducts, HttpServletRequest request) {
//        financialProductsService.create(financialProducts, request);
//        return R.success();
//    }
//
    @PostMapping("/financialProducts/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody FinancialProducts financialProducts) {
        financialProductsService.update(financialProducts);
        return R.success();
    }
//
//    @GetMapping("/delete")
//    @ApiOperation(value = "根据主键删除")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "id", value = "ID", required = true,
//                    dataType = "String", paramType = "query")
//    })
//    public R delete(String id) {
//        financialProductsService.delete(id);
//        return R.success();
//    }
//
//
//    @PostMapping("/deleteBatch")
//    @ApiOperation(value = "根据主键批量删除")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "ids", value = "IDS", required = true,
//                    dataType = "list", paramType = "query")
//    })
//    public R deleteBatch(@RequestBody List<String> ids) {
//        financialProductsService.removeByIds(ids);
//        return R.success();
//    }

    /**
     * Description:id修改状态
     */
    @PostMapping("/financialProducts/updatePublish")
    @ApiOperation(value = "批量发布")
    public R updatePublish(@RequestBody List<String> ids) {
        financialProductsService.updateByPublish(ids, "1");
        return R.success();
    }

    @PostMapping("/financialProducts/updateNotPublish")
    @ApiOperation(value = "批量取消发布")
    @ApiImplicitParam(name = "type", value = "1：发布  2：未发布", required = true,
            dataType = "Integer", paramType = "query")
    public R updateNotPublish(@RequestBody List<String> ids) {
        financialProductsService.updateByPublish(ids, "2");
        return R.success();
    }
}

