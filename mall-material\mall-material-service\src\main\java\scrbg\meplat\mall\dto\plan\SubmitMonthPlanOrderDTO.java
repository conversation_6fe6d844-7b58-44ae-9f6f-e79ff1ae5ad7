package scrbg.meplat.mall.dto.plan;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.MaterialMonthSupplyPlanDtl;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-07-03 16:14
 */
@Data
public class SubmitMonthPlanOrderDTO {

    @ApiModelProperty(value = "订单信息")
    private List<SubmitMonthPlanOrderItemDTO> dtos;
}
