package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * @描述：商品分类
 * @作者: y
 * @日期: 2022-11-13
 */
@ApiModel(value = "商品分类")
@Data
@TableName("product_category")
public class ProductCategory extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.INPUT)
    @ApiModelProperty(value = "分类id")

    private String classId;

    @ApiModelProperty(value = "分类名称 分类名称",required = true)
    @NotEmpty(message = "分类名称不能为空！")
    private String className;

    @ApiModelProperty(value = "分类关键字")

    private String classKeyword;

    @ApiModelProperty(value = "分类层级 1:一级大分类 2:二级分类 3:三级小分类")

    private Integer classLevel;

    @ApiModelProperty(value = "父层级id")

    private String parentId;

    @ApiModelProperty(value = "图标 logo")

    private String classIcon;

    @ApiModelProperty(value = "背景颜色")

    private String classBgColor;


    @ApiModelProperty(value = "分类的描述")

    private String classDescribe;


    @ApiModelProperty(value = "状态（1启用0停用）")

    private Integer state;

    @ApiModelProperty(value = "是否展示(1是，0否)")
    private Integer isExhibition;


    @ApiModelProperty(value = "分类路径")

    private String classPath;

    @ApiModelProperty(value = "是否有商品(1有，0没有)")
//    @TableField(value = "is_have_product", fill = FieldFill.INSERT)
    private Integer isHaveProduct;


    @ApiModelProperty(value = "分类编号")
    private String classNo;


    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @TableField(
            exist = false
    )
    @ApiModelProperty(value = "子节点")
    private List<ProductCategory> children;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @TableField(
            exist = false
    )
    @ApiModelProperty(hidden = true)
    private ProductCategory child;

    @ApiModelProperty(value = "分类类型（0物资）")
    private Integer productType;
}
