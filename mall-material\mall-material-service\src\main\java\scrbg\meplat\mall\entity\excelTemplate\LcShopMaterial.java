package scrbg.meplat.mall.entity.excelTemplate;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 给制定店铺导入商品
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@HeadRowHeight(40)
@ColumnWidth(25)
@ContentRowHeight(40)
public class LcShopMaterial implements Serializable {

//        private static final long serialVersionUID = -5144055068797033748L;

    @ExcelProperty(value = "序号（自增即可）")
    private Long id;

    @ExcelProperty(value = "商品名称（必填）")
    private String productName;

    @ExcelProperty(value = "分类名称(XXX/XXX/XXX)（必填）")
    private String classNamePath;

    @ExcelProperty(value = "物料编号（必填）")
    private String materialNo;

    @ExcelProperty(value = "物料名称（必填）")
    private String materialName;

    @ExcelProperty(value = "最低价")
    private BigDecimal productMinPrice;

    @ExcelProperty(value = "品牌名称")
    private String brandName;

    @ExcelProperty(value = "排序值")
    private Integer shopSort;

    @ExcelProperty(value = "规格（必填）")
    private String skuName;

    @ExcelProperty(value = "成本价（必填）")
    private BigDecimal costPrice;

    @ExcelProperty(value = "库存（必填）")
    private BigDecimal stock;

    @ExcelProperty(value = "计量单位")
    private String unit;

    @ExcelProperty(value = "原价（必填）")
    private BigDecimal originalPrice;

    @ExcelProperty(value = "销售价格（必填）")

    private BigDecimal sellPrice;

    @ExcelProperty(value = "商品材质")
    private String productTexture;

    @ExcelProperty(value = "副单位系数")

    private BigDecimal secondUnitNum;
    @ExcelProperty(value = "副计量单位")
    private String secondUnit;
    @ExcelProperty(value = "商品描述（可html代码）")
    @ColumnWidth(50)
    private String productDescribe;

}


