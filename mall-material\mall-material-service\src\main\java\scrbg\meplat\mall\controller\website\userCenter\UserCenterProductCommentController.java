package scrbg.meplat.mall.controller.website.userCenter;

import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.entity.OrderItem;
import scrbg.meplat.mall.entity.ProductComment;
import scrbg.meplat.mall.service.OrderItemService;
import scrbg.meplat.mall.service.ProductCommentService;

import java.util.List;

/**
 * @描述：商品评价控制类
 * @作者: y
 * @日期: 2022-11-02
 */
@RestController
@RequestMapping("/userCenter/productComment")
@Api(tags = "商品评价(个人中心)")
public class UserCenterProductCommentController {

    @Autowired
    public ProductCommentService productCommentService;
    @Autowired
    public OrderItemService orderItemService;

    @PostMapping("/create")
    @ApiOperation(value = "新增评价")
    public R save(@RequestBody ProductComment productComment) {
        productCommentService.create(productComment);
        return R.success();
    }
    @PostMapping("/update")
    @ApiOperation(value = "修改评价")
    public R updateComment(@RequestBody ProductComment productComment) {
        productCommentService.updateComment(productComment);
        return R.success();
    }

    @GetMapping("/getCommentByOrderItemId")
    @ApiOperation(value = "获取评价根据订单项id")
    public R getCommentByOrderItemId(String orderItemId) {
        ProductComment productComment =  productCommentService.getCommentByOrderItemId(orderItemId);
        return R.success(productComment);
    }

    @GetMapping("/getCommentByOrderId")
    @ApiOperation(value = "根据订单id获取评价")
    public R getCommentByOrderId(String orderId) {
        ProductComment productComment =  productCommentService.getCommentByOrderId(orderId);
        return R.success(productComment);
    }

    @PostMapping("/getOrderItemByOrderIds")
    @ApiOperation(value = "根据订单id批量查询订单项")
    public R listByOrderIds(@RequestBody List<String> orderIds) {
        List<OrderItem> list = orderItemService.getOrderItemByOrderIds(orderIds);
        return R.success(list);
    }

    @GetMapping("/deleteComment")
    @ApiOperation(value = "删除评价")
    public R deleteComment(String orderId) {
        productCommentService.deleteComment(orderId);
        return R.success();
    }
}

